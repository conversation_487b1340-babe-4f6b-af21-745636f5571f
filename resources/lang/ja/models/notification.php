<?php

return [
  'sidebar' => 'お知らせ一覧',
  'screen_name' => [
    'index' => 'お知らせ一覧',
    'create' => 'お知らせ作成',
    'show' => 'お知らせ詳細',
    'edit' => 'Notification編集',
  ],
  'field' => [
    'notification_div' => 'ステータス',
    'foreign_table' => 'Foreign Table',
    'foreign_id' => 'Foreign Id',
    'title' => 'お問合せタイトル',
    'body' => 'Body',
    'send_status' => 'Send Status',
    'send_schedule_at' => 'Send Schedule At',
    'sent_at' => '日付',
    'creator_id' => 'Creator Id',
    'target_user_id' => 'Target User Id',
    'deleted_at' => 'Deleted At',
  ],
    'button' => [
        'save' => '確認する'
    ]
];
