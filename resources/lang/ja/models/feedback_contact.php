<?php

return [
    'sidebar' => 'お問合せ送信詳細　返信',
    'screen_name' => [
        'create' => 'お問合せ送信詳細　返信',
        'show' => 'お問合せ送信詳細'
    ],
    'field' => [
        'notification_div' => 'ステータス',
        'foreign_table' => 'Foreign Table',
        'foreign_id' => 'Foreign Id',
        'title' => 'お問合せタイトル',
        'body' => 'Body',
        'send_status' => 'Send Status',
        'send_schedule_at' => 'Send Schedule At',
        'sent_at' => '日付',
        'creator_id' => 'Creator Id',
        'target_user_id' => 'Target User Id',
        'deleted_at' => 'Deleted At',
    ],
    'button' => [
        'save' => '確認する'
    ]
];
