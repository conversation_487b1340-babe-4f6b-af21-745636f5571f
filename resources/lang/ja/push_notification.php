<?php
return [

    'common_title' => 'お知らせ',

    'point' => [
        'confirm' => [
            'title' => 'ポイントお知らせ',
            'body' => ':nicknameさんからポイントがプレゼントされました。'
        ],
        'decline_receive' => [
            'title' => 'ポイントお知らせ',
            'body' => ':nicknameさんからポイントプレゼントをお断りされました。'
        ],
        'subtracted' => [
            'title' => 'ポイントお知らせ',
            'body' => 'ポイントが:nicknameさんにプレゼントされました。'
        ]
    ],
    'friend' => [
        'invite' => [
            'title' => 'フレンドお知らせ',
            'body' => ":nicknameさんから\n友達申請が来ました。"
        ],
        'add_friend_success' => [
            'title' => 'フレンドお知らせ',
            'body' => ":nicknameさんと\n友達になりました。"
        ]
    ],
    'party' => [
        'matched' => [
            'title' => 'パーティーお知らせ',
            'body' => ":group_type人グループをマッチングしました。"
        ],
        'matching_timeout' => [
            'title' => 'パーティーお知らせ',
            'body' => "飲み会がタイムアウトしました。"
        ],
        'started' => [
            'title' => 'パーティーお知らせ',
            'body' => "飲み会を開始します。"
        ],
        'ask_extend' => [
            'title' => 'パーティーお知らせ',
            'body' => "延長ユーザー:nicknameさんが30分の延長を希望しました。\n延長しますか？"
        ],
        'accepted_extend' => [
            'title' => 'パーティーお知らせ',
            'body' => "延長承認ユーザー:nicknameさんが30分延長しました。"
        ],
        'user_extended' => [
            'title' => 'パーティーお知らせ',
            'body' => "延長承認ユーザー:nicknameさんが30分延長しました。"
        ],
        'notice_end' => [
            'title' => 'パーティーお知らせ',
            'body' => "Party will end in 10 minutes"
        ],
        'stage_user_extended' => [
            'title' => 'パーティーお知らせ',
            'body' => "飲み会の延長した時間を開始します。"
        ],
        'stage_user_expired' => [
            'title' => 'パーティーお知らせ',
            'body' => "You are expired"
        ],
        'end' => [
            'title' => 'パーティーお知らせ',
            'body' => "飲み会が終了しました。"
        ]
    ],
    'setting_party' => [
        'invite' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんに\n:gender_name :group_type人グループ　:from_age歳～:to_age歳の\n飲み会に招待されました。"
        ],
        'invite_age_option' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんに\n:gender_name :group_type人グループの\n飲み会に招待されました。"
        ],
        'invite_with_friend' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんに友達飲み会に招待されました。\n:number_person人グループ"
        ],
        'delete' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんが参加を止めました。セッティングを終了します。"
        ],
        'delete_auto' => [
            'title' => '設定パーティーお知らせ',
            'body' => "グループが削除されました。"
        ],
        'decline' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんがグループの参加を否認しました。セッティングを終了します"
        ],
        'leave' => [
            'title' => '設定パーティーお知らせ',
            'body' => ":nicknameさんがLEAVED"
        ],
    ],
    'verification' => [
        'approve' => [
            'title' => '検証を承認しました。',
            'body' => 'ニックネーム:nicknameさんの身分証明書の画像審査が承認され本会員となりました。'
        ],
        'decline' => [
            'title' => '身分証明書を否認いたしました。',
            'body' => 'ニックネーム:nicknameさんの身分証明書の画像審査が否認されました。'
        ]
    ]

];

