<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted'             => ':attributeを承認してください。',
    'active_url'           => ':attributeは有効なURLではありません。',
    'after'                => ':attributeには:date以降の日付を入力してください。',
    'after_or_equal'       => ':attributeには:date以降もしくは同日時を入力してください。',
    'alpha'                => ':attributeにはアルファベッドのみ使用できます。',
    'alpha_dash'           => ":attributeには英数字('A-Z','a-z','0-9')とハイフンと下線('-','_')が使用できます。",
    'alpha_num'            => ":attributeには英数字('A-Z','a-z','0-9')が使用できます。",
    'array'                => ':attributeには配列を選択してください。',
    'before'               => ':attributeには:date以前の日付を入力してください。',
    'before_or_equal'      => ':attributeには:date以前もしくは同日時を入力してください。',
    'between'              => [
        'numeric' => ':attributeには:minから:maxまでの数字を入力してください。',
        'file'    => ':attributeには:min KBから:maxKBまでのサイズのファイルを選択してください。',
        'string'  => ':attributeは:min文字から:max文字にしてください。',
        'array'   => ':attributeの項目は:min個から:max個にしてください。',
    ],
    'boolean'              => ":attributeには'true'か'false'を入力してください。",
    'confirmed'            => ':attributeと:attribute確認が一致しません。',
    'date'                 => ':attributeは正しい日付ではありません。',
    'date_format'          => ":attributeの形式は':format'と合いません。",
    'different'            => ':attributeと:otherには異なるものを入力してください。',
    'digits'               => ':attributeは:digits桁にしてください。',
    'digits_between'       => ':attributeは:min桁から:max桁にしてください。',
    'dimensions'           => ':attributeは正しい縦横比ではありません。',
    'distinct'             => ':attributeに重複した値があります。',
    'email'                => ':attributeは有効なメールアドレス形式で入力してください。',
    'exists'               => '選択された:attributeは有効ではありません。',
    'file'                 => ':attributeはファイルでなければいけません。',
    'filled'               => ':attributeは必須です。',
    'image'                => ':attributeには画像を入力してください。',
    'in'                   => '選択された:attributeは有効ではありません。',
    'in_array'             => ':attributeは:otherに存在しません。',
    'integer'              => ':attributeには整数を入力してください。',
    'ip'                   => ':attributeには有効なIPアドレスを入力してください。',
    'ipv4'                 => ':attributeはIPv4アドレスを入力してください。',
    'ipv6'                 => ':attributeはIPv6アドレスを入力してください。',
    'json'                 => ':attributeには有効なJSON文字列を入力してください。',
    'max'                  => [
        'numeric' => ':attributeには:max以下の数字を入力してください。',
        'file'    => ':attributeには:maxKB以下のファイルを入力してください。',
        'string'  => ':attributeは:max文字以下にしてください。',
        'array'   => ':attributeの項目は:max個以下にしてください。',
    ],
    'mimes'                => ':attributeには:valuesタイプのファイルを選択してください。',
    'mimetypes'            => ':attributeには:valuesタイプのファイルを選択してください。',
    'min'                  => [
        'numeric' => ':attributeには:min以上の数字を入力してください。',
        'file'    => ':attributeには:minKB以上のファイルを選択してください。',
        'string'  => ':attribute半角英字:min文字以上となります。',
        'array'   => ':attributeの項目は:max個以上にしてください。',
    ],
    'not_in'               => '選択された:attributeは有効ではありません。',
    'numeric'              => ':attributeには数字を入力してください。',
    'present'              => ':attributeは必ず存在しなくてはいけません。',
    'regex'                => ':attributeには有効な正規表現を入力してください。',
    'required'             => ':attributeは必ず入力してください。',
    'required_if'          => ':otherが:valueの場合:attributeを入力してください。',
    'required_unless'      => ':otherが:value以外の場合:attributeを入力してください。',
    'required_with'        => ':valuesが入力されている場合:attributeも入力してください。',
    'required_with_all'    => ':valuesがすべて入力されている場合:attributeも入力してください。',
    'required_without'     => ':valuesが入力されていない場合:attributeを入力してください。',
    'required_without_all' => ':valuesがすべて入力されていない場合:attributeを入力してください。',
    'same'                 => ':attributeと:otherが一致しません。',
    'size'                 => [
        'numeric' => ':attributeには:sizeを入力してください。',
        'file'    => ':attributeには:sizeKBのファイルを選択してください。',
        'string'  => ':attributeは:size文字にしてください。',
        'array'   => ':attributeの項目は:size個にしてください。',
    ],
    'string'   => ':attributeには文字を入力してください。',
    'timezone' => ':attributeには有効なタイムゾーンを選択してください。',
    'unique'   => '入力した:attributeは既に使用されています。',
    'uploaded' => ':attributeのアップロードに失敗しました。',
    'url'      => ':attributeは有効なURL形式で入力してください。',

    'ascii_printable'    => ':attributeには半角英数字記号が使用できます。',
    'kana'               => ":attributeには全角カタカナと全半角スペースと全角ハイフン（'ー'）が使用できます。",
    'smart_tel'          => ':attributeには有効な電話番号を入力してください。',
    'tel'                => ':attributeには有効な電話番号を入力してください。',
    'smart_zipcode'          => ':attributeには有効な郵便番号を入力してください。',
    'zipcode'                => ':attributeには有効な郵便番号を入力してください。',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [

    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [

    ],

];
