import { DATE_DEFAULT, DATE_FORMAT_SLASH, DATE_MONTH, DATE_TIME_SPLASH, FULL_DATE_TIME_SPLASH, FULL_DATE_TIME } from '@/constant/date_format'
import moment from 'moment'

export function dateFormat (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(DATE_DEFAULT) : def
}

export function dateFormatSlash (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(DATE_FORMAT_SLASH) : def
}

export function dateTimeFormatSlash (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(DATE_TIME_SPLASH) : def
}

export function fullDateTimeFormat (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(FULL_DATE_TIME) : def
}

export function fullDateTimeFormatSlash (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(FULL_DATE_TIME_SPLASH) : def
}

export function dateMonthFormat (value, def = '-') {
  const date = moment(value)
  return date.isValid() ? date.format(DATE_MONTH) : def
}
