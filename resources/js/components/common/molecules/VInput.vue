<template>
  <validation-provider
    v-slot="validationContext"
    :name="validationFiled"
    :rules="validationRules"
    :vid="vid"
  >
    <div v-if="!isLabel">
      <b-form-textarea
        v-if="$attrs['type'] === 'textarea'"
        v-model="newValue"
        v-bind="$attrs"
        rows="4"
        max-rows="6"
        :state="getValidationState(validationContext)"
      />
      <b-form-input
        v-else
        v-model="newValue"
        v-bind="$attrs"
        :state="getValidationState(validationContext)"
        size="sm"
      />
      <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
    </div>
    <b-form-group
      v-else-if="isHorizontal"
      :label="label"
      :label-for="label"
      label-cols="4"
      label-cols-lg="2"
    >
      <b-form-textarea
        v-if="$attrs['type'] === 'textarea'"
        v-model="newValue"
        v-bind="$attrs"
        rows="4"
        max-rows="6"
        :state="getValidationState(validationContext)"
      />
      <b-form-input
        v-else
        v-model="newValue"
        v-bind="$attrs"
        :state="getValidationState(validationContext)"
        size="sm"
      />
      <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
    <b-form-group
      v-else
      :label="label"
      :label-for="label"
    >
      <b-form-textarea
        v-if="$attrs['type'] === 'textarea'"
        v-model="newValue"
        v-bind="$attrs"
        rows="4"
        max-rows="6"
        :state="getValidationState(validationContext)"
      />
      <b-form-input
        v-else
        v-model="newValue"
        v-bind="$attrs"
        :state="getValidationState(validationContext)"
      />
      <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </validation-provider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'

export default {
  name: 'VInput',
  components: { ValidationProvider },
  props: {
    value: {
      type: [String, Number, Boolean, Object, Array, Symbol, Function],
      default: null
    },
    label: {
      type: String,
      default: ''
    },
    vid: {
      type: String,
      default: ''
    },
    validationRules: {
      type: String,
      default: ''
    },
    validationFiled: {
      type: String,
      default: ''
    },
    isHorizontal: {
      type: Boolean,
      default: true
    },
    isImmediate: {
      type: Boolean,
      default: true
    },
    isLabel: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      newValue: this.value
    }
  },
  watch: {
    newValue (value) {
      this.$emit('input', value)
    }
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    }
  }
}
</script>
<style scoped>
  .invalid-feedback {
    display: block;
  }
</style>
