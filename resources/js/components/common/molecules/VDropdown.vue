<template>
  <validation-provider
    v-slot="validationContext"
    :name="validationFiled"
    :rules="validationRules"
  >
    <div v-if="!isLabel">
      <b-form-select
        v-model="newValue"
        :options="newOptions"
        :name="name"
        :state="getValidationState(validationContext)"
        size="sm"
      >
        <template
          v-if="isSelect"
          #first
        >
          <b-form-select-option
            :value="null"
          >
            {{ titleName }}
          </b-form-select-option>
        </template>
      </b-form-select>
      <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
    </div>
    <b-form-group
      v-else
      :label="label"
      :label-for="label"
      label-cols="4"
      label-cols-lg="2"
    >
      <b-form-select
        v-model="newValue"
        :options="newOptions"
        :name="name"
        :state="getValidationState(validationContext)"
        size="sm"
      >
        <template
          v-if="isSelect"
          #first
        >
          <b-form-select-option
            :value="null"
          >
            {{ titleName }}
          </b-form-select-option>
        </template>
      </b-form-select>
      <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
    </b-form-group>
  </validation-provider>
</template>

<script>
import { ValidationProvider } from 'vee-validate'
export default {
  name: 'VDropdown',
  components: { ValidationProvider },
  props: {
    value: {
      type: [String, Number, Boolean, Object, Array, Symbol, Function],
      default: null
    },
    label: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    validationRules: {
      type: String,
      default: ''
    },
    validationFiled: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default: () => {}
    },
    isSelect: {
      type: Boolean,
      default: false
    },
    isLabel: {
      type: Boolean,
      default: true
    },
    titleName: {
      type: String,
      default: '選んでください'
    }
  },
  data () {
    return {
      newValue: this.value ? this.value : null
    }
  },
  computed: {
    newOptions () {
      let options = []
      if (this.options) {
        Object.keys(this.options).forEach(key => {
          if (key === '0') {
            options.push({ 'value': null, 'text': this.options[key] })
          } else {
            options.push({ 'value': key, 'text': this.options[key] })
          }
        })
      }
      return options
    }
  },
  watch: {
    newValue (value) {
      this.$emit('input', value)
    }
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    }
  }
}
</script>
