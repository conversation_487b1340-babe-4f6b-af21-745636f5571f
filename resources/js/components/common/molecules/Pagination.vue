<template>
  <div>
    <div
      id="client-pagination"
      class="level"
    >
      <!-- display count result -->
      <div
        class="level-left"
      >
        {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
      </div>
      <!-- pagination -->
      <div v-if="isPaginator">
        <ul
          role="menubar"
          aria-disabled="false"
          aria-label="Pagination"
          class="pagination b-pagination"
        >
          <li
            role="presentation"
            class="page-item"
          >
            <button
              :disabled="disabledPreButton"
              role="menuitem"
              type="button"
              tabindex="-1"
              class="page-link"
              @click="changePage(1)"
            >
              «
            </button>
          </li>
          <li
            role="presentation"
            class="page-item"
          >
            <button
              :disabled="disabledPreButton"
              role="menuitem"
              type="button"
              tabindex="-1"
              class="page-link"
              @click.prevent="changePage(currentPage - 1)"
            >
              ‹
            </button>
          </li>
          <li
            v-for="item in pagesNumber"
            :key="item"
            role="presentation"
            :class="{'page-item active': item === currentPage}"
          >
            <button
              role="menuitemradio"
              type="button"
              tabindex="-1"
              class="page-link"
              @click="changePage(item)"
            >
              {{ item }}
            </button>
          </li>
          <li
            role="presentation"
            class="page-item"
          >
            <button
              :disabled="disabledNextButton"
              role="menuitem"
              type="button"
              tabindex="-1"
              class="page-link"
              @click="changePage(currentPage + 1)"
            >
              ›
            </button>
          </li>
          <li
            role="presentation"
            class="page-item"
          >
            <button
              :disabled="disabledNextButton"
              role="menuitem"
              type="button"
              tabindex="-1"
              class="page-link"
              @click="changePage(lastPage)"
            >
              »
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Pagination',
  props: {
    paginationData: {
      type: Object,
      default: function () {
        return {
          total: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
          perPage: 0
        }
      }
    }
  },
  data () {
    return {
      page: 1,
      total: this.paginationData.total,
      currentPage: this.paginationData.currentPage,
      lastPage: this.paginationData.lastPage,
      from: this.paginationData.from,
      to: this.paginationData.to,
      perPage: this.paginationData.perPage
    }
  },
  computed: {
    isPaginator: function () {
      return this.lastPage > 1
    },
    isData: function () {
      return this.total > 0
    },
    disabledPreButton: function () {
      return this.currentPage <= 1
    },
    disabledNextButton: function () {
      return this.currentPage >= this.lastPage
    },
    pagesNumber: function () {
      let offset = 2
      if (!this.to) {
        return []
      }
      let start = this.currentPage - offset
      if (start < 1) {
        start = 1
      }
      let end = start + (offset * 2)
      if (end > this.lastPage) {
        end = this.lastPage
      }
      start = end - (offset * 2)
      if (start < 1) {
        start = 1
      }
      let pagesArray = []
      while (start <= end) {
        pagesArray.push(start)
        start++
      }
      return pagesArray
    }
  },
  methods: {
    changePage: function (page) {
      if (page >= 1 && page <= this.lastPage) {
        location.href = this.updateParam(page, this.perPage)
      }
    },
    updateParam (page, perPage) {
      let searchParams = new URLSearchParams(window.location.search)
      searchParams.set('page', page)
      this.$eventHub.$emit('pagination-data', {
        page: page
      })
      return location.pathname + '?' + searchParams
    }
  }

}
</script>

<style scoped>
</style>
