<template>
  <div>
    <div
      v-for="(err, index) in error"
      :key="index"
    >
      <b-alert variant="danger" show dismissible>
        {{ err }}
      </b-alert>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ErrorBoundaryRow',
  props: {
    notifyKey: {
      type: String,
      required: true
    },
    error: {
      type: Array,
      required: true
    }
  },
  methods: {
    clickDelete: function () {
      this.$emit('notify-delete', this.notifyKey)
    }
  }
}
</script>

<style scoped>

</style>
