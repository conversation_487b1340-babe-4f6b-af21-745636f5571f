<template>
  <div v-if="total === 0">
    {{ $t('common.no_result') }}
  </div>
  <div v-else>
    <div class="level-left">
      {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
    </div>
    <b-pagination
      v-model="currentPage"
      :total-rows="total"
      :per-page="perPage"
    />
  </div>
</template>

<script>
export default {
  name: 'VPagination',
  props: {
    value: {
      type: [String, Number, Boolean, Object, Array, Symbol, Function],
      default: null
    },
    total: {
      type: Number,
      default: 0
    },
    perPage: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      currentPage: 1
    }
  },
  computed: {
    from: {
      get: function () {
        if (this.total === 0) {
          return 0
        } else {
          return (this.currentPage - 1) * this.perPage + 1
        }
      }
    },
    to: {
      get: function () {
        return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
      }
    }
  },
  watch: {
    currentPage (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped>

</style>
