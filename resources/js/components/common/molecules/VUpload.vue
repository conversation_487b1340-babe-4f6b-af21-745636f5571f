<template>
  <div>
    <b-form-group
      id="fileInput"
      :label="label"
      :label-for="label"
      label-cols="4"
      label-cols-lg="2"
      :class="{'dragdrop': true }"
    >
      <b-form-file
        ref="file"
        v-model="files"
        :file-name-formatter="formatNames"
        accept="image/jpeg, image/png, image/gif"
        placeholder="アップロードするファイルを選択、またはファイルをここにドラッグ＆ドロップしてください"
        drop-placeholder="ドロップしてください"
      />
      <validation-provider
        v-slot="validationContext"
        :name="validationFiled"
        :rules="validationRules"
      >
        <input
          v-model="newValue"
          :name="newName"
          :state="getValidationState(validationContext)"
          type="hidden"
        >
        <div
          class="row"
          v-html="previewFile"
        />
        <b-form-invalid-feedback>{{ validationContext.errors[0] }}</b-form-invalid-feedback>
      </validation-provider>
    </b-form-group>
  </div>
</template>

<script>
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import { ValidationProvider } from 'vee-validate'
export default {
  name: 'VUpload',
  components: { ValidationProvider },
  props: {
    value: {
      type: [String, Number, Boolean, Object, Array, Symbol, Function],
      default: () => []
    },
    label: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    validationFiled: {
      type: String,
      default: ''
    },
    validationRules: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    uploadFileUrl: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      newValue: this.value ? this.value : this.multiple ? [] : '',
      previewFile: this.makePreview(this.value),
      files: [],
      newName: this.multiple ? this.name + '[]' : this.name,
      oldTmp: null
    }
  },
  watch: {
    files () {
      this.uploadFileToServer()
    }
  },
  methods: {
    makePreview (data) {
      if (!data) {
        return ''
      }
      if (!this.multiple) {
        return `<div class="col-2">
                        <img src="${data}" class="img-thumbnail img-fluid">
                    </div> `
      }
      let preview = ''
      for (let file of data) {
        preview += `<div class="col-2">
                        <img src="${file}" class="img-thumbnail img-fluid">
                    </div> `
      }
      return preview
    },

    async uploadFileToServer () {
      try {
        this.$emit('update:isUploading', true)
        const formData = new FormData()

        if (!this.multiple) {
          formData.append('file', this.files)
          formData.append('old_tmp_path', this.oldTmp)
        } else {
          for (let file of this.files) {
            formData.append('files[]', file)
          }
        }

        const { data } = await axios.post(this.uploadFileUrl, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        if (!this.multiple) {
          this.oldTmp = data.filePath
          this.newValue = data.filePath
        } else {
          for (let file of data) {
            this.newValue.push(file.filePath)
          }
        }
        this.showUploadFile()
      } catch (e) {
        this.$nextTick(() => {
          throw new Error(extractLaravelError(e))
        })
      } finally {
        this.$emit('update:isUploading', false)
      }
    },
    showUploadFile () {
      let data = this.multiple ? this.files : [this.files]
      for (let i = 0; i < data.length; i++) {
        let reader = new FileReader() // instantiate a new file reader
        reader.addEventListener('load', function (e) {
          if (!this.multiple) {
            this.previewFile = `
                        <div class="col-2">
                            <img src="${e.target.result}" class="img-thumbnail img-fluid">
                        </div> `
          } else {
            for (let file of data) {
              this.previewFile += `
                        <div class="col-2">
                            <img src="${e.target.result}" class="img-thumbnail img-fluid">
                        </div> `
            }
          }
        }.bind(this), false) // add event listener
        reader.readAsDataURL(data[i])
      }
    },
    formatNames () {
      return 'アップロードするファイルを選択、またはファイルをここにドラッグ＆ドロップしてください'
    },
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    }
  }
}
</script>
<style>
  .invalid-feedback {
    display: block;
  }
  #preview img {
    max-width: 100%;
  }
  #fileInput.dragdrop .custom-file,
  #fileInput.dragdrop .custom-file-input {
    height: 100px;
  }
  #fileInput.dragdrop .custom-file-label {
    border: 0;
    border: 5px dotted skyblue;
    height: 100px;
    line-height: 90px;
    text-align: center;
    color: skyblue;
    padding: 0;
  }
  #fileInput.dragdrop .custom-file:hover .custom-file-label {
    background: rgb(75, 181, 225);
    color: #fff;
  }

  #fileInput.dragdrop .custom-file-label::after {
    display: none;
  }
</style>
