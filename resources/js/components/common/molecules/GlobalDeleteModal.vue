<template>
  <b-modal
    id="global-delete"
    :title="$t('common.btn.delete')"
    :ok-title="$t('common.btn.ok')"
    :cancel-title="$t('common.btn.cancel')"
    ok-variant="danger"
    no-close-on-backdrop
    :ok-disabled="submitted"
    @ok="clickDelete"
  >
    <p class="my-4">
      {{ $t('common.delete_confirm') }}
    </p>
  </b-modal>
</template>

<script>
export default {
  name: 'GlobalDeleteModal',
  data () {
    return {
      submitted: false,
      handleDelete: () => {}
    }
  },
  mounted () {
    this.$eventHub.$on('open-delete-modal', (func) => {
      this.handleDelete = func
      this.submitted = false
      this.$bvModal.show('global-delete')
    })
  },
  methods: {
    async clickDelete () {
      try {
        this.submitted = true
        await this.handleDelete()
      } finally {
        this.submitted = false
      }

      this.submitted = false
    }
  }
}
</script>
