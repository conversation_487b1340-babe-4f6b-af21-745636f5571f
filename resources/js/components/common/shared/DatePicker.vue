<script setup lang="ts">
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';
import { v4 as uuid } from 'uuid';
import { computed } from 'vue';
import { Japanese } from 'flatpickr/dist/l10n/ja';
import { ClockIcon } from '@heroicons/vue/24/outline';

interface Props {
  modelValue: string | null;
  label?: string;
  error?: string;
  name?: string;
  type?: 'date' | 'datetime' | 'time';
  id?: string;
  disabled?: boolean;
  required?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'date',
  disabled: false,
  id: () => `text-input-${uuid()}`,
  required: false,
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | null): void;
}>();

const flatpickrConfig = computed(() => {
  const baseConfig = {
    wrap: true,
    altInput: true,
    time_24hr: true,
    minuteIncrement: 1,
    locale: Japanese,
  };

  switch (props.type) {
    case 'datetime':
      return {
        ...baseConfig,
        enableTime: true,
        dateFormat: 'Y-m-d H:i',
        altFormat: 'Y-m-d H:i',
      };
    case 'time':
      return {
        ...baseConfig,
        enableTime: true,
        noCalendar: true,
        dateFormat: 'H:i',
        altFormat: 'H:i',
      };
    default:
      return {
        ...baseConfig,
        dateFormat: 'Y-m-d',
        altFormat: 'Y-m-d',
      };
  }
});

const handleInput = (value: string | null) => {
  emit('update:modelValue', value);
};
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    <div class="relative mb-1">
      <flat-pickr
        :id="id"
        :model-value="modelValue"
        @update:model-value="handleInput"
        :config="flatpickrConfig"
        class="h-11 w-full appearance-none rounded-lg border border-gray-300 bg-none px-4 py-2.5 pl-4 pr-11 text-sm text-gray-800 shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10"
        :class="disabled ? 'bg-gray-100' : 'bg-transparent'"
        :disabled="disabled"
      />
      <ClockIcon class="absolute text-gray-500 -translate-y-1/2 right-3 top-1/2 w-5 h-5" />
    </div>
    <div v-if="error" class="mt-1 text-sm text-error-500">{{ error }}</div>
  </div>
</template>
