<script setup lang="ts">
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
import Button from '@/components/common/shared/Button.vue';
import { useI18n } from '@/composables/useI18n';

const { t } = useI18n();

defineProps<{
  show: boolean;
  title?: string;
  message?: string;
}>();

const emit = defineEmits<{
  (e: 'confirm'): void;
  (e: 'cancel'): void;
}>();

const handleConfirm = () => {
  emit('confirm');
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<template>
  <div
    v-if="show"
    class="fixed inset-0 flex items-center justify-center modal z-999"
  >
    <div
      class="fixed inset-0 h-full w-full bg-gray-400/50 transition-opacity"
      aria-hidden="true"
      @click="handleCancel"
    ></div>
    <div
      class="relative max-w-md w-full mx-4 bg-white rounded-3xl p-6 shadow-xl"
    >
      <div class="flex items-center justify-center mb-4">
        <div class="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full">
          <ExclamationTriangleIcon class="w-6 h-6 text-yellow-600" />
        </div>
      </div>
      
      <div class="text-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">
          {{ title || 'Xác nhận rời khỏi trang' }}
        </h3>
        <p class="text-sm text-gray-600">
          {{ message || 'Bạn có thay đổi chưa được lưu. Nếu rời khỏi trang bây giờ, các thay đổi sẽ bị mất.' }}
        </p>
      </div>

      <div class="flex flex-col sm:flex-row gap-3">
        <Button
          variant="outline"
          size="sm"
          class="flex-1 order-2 sm:order-1"
          @click="handleCancel"
        >
          Ở lại trang
        </Button>
        <Button
          variant="primary"
          size="sm"
          class="flex-1 order-1 sm:order-2 bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700"
          @click="handleConfirm"
        >
          Rời khỏi mà không lưu
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.modal {
  backdrop-filter: blur(4px);
}
</style>
