<template>
  <div>
    <div class="card">
      <b-table
        striped
        hover
        :fields="fields"
        :items="ticketPlans"
        @row-clicked="goToDetail"
      >
        <template v-slot:cell(actions)="row">
          <b-button
            size="sm"
            class="mr-1 btn btn-info"
            @click="goToEdit(row.item.id)"
          >
            {{ $t('common.btn.edit') }}
          </b-button>
          <b-button
            size="sm"
            class="mr-1 btn btn-danger"
            @click="deleteTicketPlan(row.item.id)"
          >
            {{ $t('common.btn.delete') }}
          </b-button>
        </template>
      </b-table>
      <div class="card-footer">
        <pagination
          :pagination-data="paginationOption"
        />
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import Pagination from '@/components/common/molecules/Pagination'
export default {
  name: 'TicketPlanList',
  components: { Pagination },
  props: {
    pagination: {
      type: Object,
      default: function () {
        return {
        }
      }
    }
  },
  data () {
    return {
      ticketPlans: this.pagination.data || [],
      paginationOption: {
        total: this.pagination.total || 0,
        currentPage: this.pagination.current_page || 0,
        lastPage: this.pagination.last_page || 0,
        from: this.pagination.from || 0,
        to: this.pagination.to || 0,
        perPage: this.pagination.per_page || 0
      },
      fields: [
        {
          key: 'name',
          label: this.$t('models.ticket_plan.field.name')
        },
        {
          key: 'normalMoney',
          label: this.$t('models.ticket_plan.field.normal_money')
        },
        {
          key: 'money',
          label: this.$t('models.ticket_plan.field.money')
        },
        {
          key: 'ticket',
          label: this.$t('models.ticket_plan.field.ticket')
        },
        {
          key: 'time',
          label: this.$t('models.ticket_plan.field.time')
        },
        {
          key: 'timeUnitName',
          label: this.$t('models.ticket_plan.field.time_minute')
        },
        {
          key: 'iosPackageId',
          label: this.$t('models.ticket_plan.field.ios_package_id')
        },
        {
          key: 'androidPackageId',
          label: this.$t('models.ticket_plan.field.android_package_id')
        },
        {
          key: 'actions',
          label: this.$t('common.action'),
          tdClass: 'action-size'
        }
      ]
    }
  },
  methods: {
    goToDetail (ticketPlan) {
      location.href = this.route('admin.ticket_plan.show', ticketPlan.id)
    },
    goToEdit (id) {
      location.href = this.route('admin.ticket_plan.edit', id)
    },
    deleteTicketPlan (id) {
      this.$eventHub.$emit('open-delete-modal', async () => {
        try {
          await axios.delete(this.route('admin.ticket_plan.delete', id))
          location.href = this.route('admin.ticket_plan.index')
        } catch (e) {
          this.$nextTick(() => {
            throw new Error(extractLaravelError(e))
          })
        }
      })
    }
  }
}
</script>
