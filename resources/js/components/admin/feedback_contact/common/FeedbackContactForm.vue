<template>
  <b-card>
    <b-table-simple
      bordered
      small
    >
      <b-tbody class="custom-table-feedback">
        <!-- Send ID -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            受信ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactId }}
          </b-td>
        </b-tr>

        <!-- Receive ID -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            会員ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactMemberId }}
          </b-td>
        </b-tr>

        <!-- status -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            ステータス
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
            :style="{color : contactStatus === 1 ? 'red' : 'black'}"
          >
            {{ contactStatusName }}
          </b-td>
        </b-tr>

        <!-- Warning -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            警告
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            <b-form-select
              v-model="contactWarning"
              style="width: 120px"
              :options="options"
              name="enable_warning"
            />
          </b-td>
        </b-tr>

        <!-- Date reply -->
        <b-tr>
          <b-th>
            送信日
          </b-th>
          <b-td
            class="text-center"
            style="vertical-align: middle"
            colspan="2"
          >
            <b-row
              align-h="center"
              class="justify-content-md-center"
            >
              <b-col
                cols="12"
                md="auto"
              >
                <v-datetime-picker
                  :key="keyChanging"
                  v-model="sentAt"
                  :validation-filed="$t('models.notification.field.sent_at')"
                  validation-rules="required"
                  :is-label="false"
                  :is-disabled="disableInput"
                  name="sent_at"
                  date-type="datetime"
                  style="width: 240px"
                />
              </b-col>
              <b-col
                cols="auto"
              >
                <b-button
                  size="sm"
                  variant="outline-primary"
                  class="btn primary custom-btn-send-now"
                  @click="customSend"
                >
                  {{ (isSendNow) ? $t('models.feedback.field.choose_datetime') : $t('models.feedback.field.send_now') }}
                </b-button>
              </b-col>
            </b-row>
          </b-td>
        </b-tr>

        <!-- Contact Date -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            お問合せ日付
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactAt }}
          </b-td>
        </b-tr>

        <!-- Report Item -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            お問合せ項目
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactDivName }}
          </b-td>
        </b-tr>

        <!-- content -->
        <b-tr>
          <b-td
            class="text-left border"
            colspan="7"
          >
            {{ content }}
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>

    <!-- report feedback input -->
    <v-input
      v-model="editorContent"
      class="custom-input-message"
      :validation-filed="$t('models.user_feedback.field.content')"
      validation-rules="required"
      type="textarea"
      name="content"
      :is-label="false"
      maxlength="5000"
    />

    <!-- Target user Id -->
    <v-input
      v-show="false"
      v-model="contactedUserId"
      type="text"
      name="target_user_id"
    />
    <!-- Foreign id -->
    <v-input
      v-show="false"
      v-model="contactId"
      type="text"
      name="foreign_id"
    />
  </b-card>
</template>

<script>
import moment from 'moment'
import VDatetimePicker from '@/components/common/molecules/VDatetimePicker'
import VInput from '@/components/common/molecules/VInput'
export default {
  name: 'FeedbackContactForm',
  components: { VInput, VDatetimePicker },
  props: {
    userContact: {
      type: [Object, Array],
      default: () => ({
        title: null,
        body: null,
        notificationDiv: null

      })
    },
    userId: {
      type: Number,
      default: null
    }
  },
  data () {
    const { sentAt, contactAt, content, contactDivName, contactWarning, contactStatusName, contactStatus, contactId, contactMemberId, contactedUserId } = this.userContact
    return {
      editorContent: '',
      keyChanging: 0,
      disableInput: true,
      isSendNow: true,
      sentAt,
      content,
      contactDivName,
      contactWarning,
      contactStatusName,
      contactStatus,
      contactId,
      contactMemberId,
      contactedUserId,
      contactAt,
      options: [
        { value: 0, text: '正常' },
        { value: 1, text: '返信する' }
      ]
    }
  },
  methods: {
    customSend () {
      this.isSendNow = !this.isSendNow
      // Send now
      if (this.isSendNow) {
        this.sentAt = moment().format('YYYY-MM-DD HH:mm')
      }
      (this.isSendNow) ? this.disableInput = true : this.disableInput = false
    }
  }
}
</script>

<style scoped>
  .custom-table-feedback th {
    text-align: center;
    width: 350px;
    vertical-align: middle;
  }

  .custom-table-feedback td {
    text-align: center;
    vertical-align: middle;
  }

  .custom-btn-send-now {
    width: 120px;
    text-align: center;
    vertical-align: middle;
  }
</style>
