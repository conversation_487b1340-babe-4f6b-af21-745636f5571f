<template>
  <div class="card">
    <b-table-simple
      bordered
      hover
      striped
    >
      <b-tbody class="custom-table-announcement-detail">
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.notification_div') }}
          </b-th>
          <b-td>
            {{ notification.notificationDivName }}
          </b-td>
        </b-tr>
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.sent_at') }}
          </b-th>
          <b-td>
            {{ notification.sentAt }}
          </b-td>
        </b-tr>
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.title') }}
          </b-th>
          <b-td>
            {{ notification.title }}
          </b-td>
        </b-tr>
        <b-tr>
          <b-td
            colspan="2"
            class="custom-message"
          >
            {{ notification.body }}
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <div class="card-footer text-center">
      <a
        class="btn btn-default"
        :href="route('admin.notification.index')"
      >
        {{ $t('common.btn.back') }}
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotificationDetail',
  props: {
    notification: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style scoped>
  .custom-message {
    text-align: left;
    height: 400px;
    overflow: auto;
  }
  .custom-table-announcement-detail th {
    padding-left: 12px;
    padding-right: 12px;
    width: 250px;
    vertical-align: middle;
    text-align: center;
  }
  .custom-table-announcement-detail td {
    padding-left: 12px;
    padding-right: 12px;
  }
</style>
