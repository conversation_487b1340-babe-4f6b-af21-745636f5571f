<template>
  <div class="card box">
    <b-row>
      <b-col>
        <table class="table table-sm tbl-detail-info custom-table-search">
          <tbody>
            <tr>
              <td>
                <b-form-input
                  v-model="filter.title"
                  size="sm"
                  type="text"
                  placeholder="お問合せタイトル"
                  @keyup.enter="search"
                />
              </td>
              <td class="custom-td-search">
                日付
              </td>
              <td>
                <v-datetime-picker
                  :key="keyChanging"
                  v-model="notificationDateStart"
                  :is-label="false"
                  date-type="date"
                />
              </td>
              <td>
                <b-button
                  class="s-clear"
                  size="sm"
                  @click="clearAnnouncementDateStart"
                >
                  クリア
                </b-button>
              </td>
              <td class="custom-td-search">
                ~
              </td>
              <td>
                <v-datetime-picker
                  :key="keyChanging"
                  v-model="notificationDateEnd"
                  :is-label="false"
                  date-type="date"
                />
              </td>
              <td>
                <b-button
                  class="s-clear"
                  size="sm"
                  @click="clearAnnouncementDateEnd"
                >
                  クリア
                </b-button>
              </td>
              <td>
                <v-dropdown
                  v-model="filter.notificationStatus"
                  :is-label="false"
                  :is-select="true"
                  :options="$root.lazyOption.notificationDivOption"
                />
              </td>
              <td>
                <b-button
                  variant="outline-info"
                  class="mb-2"
                  size="sm"
                  @click="search"
                >
                  <b-icon
                    icon="search"
                    aria-hidden="true"
                  />
                </b-button>
              </td>
            </tr>
            <tr>
              <td colspan="9">
                <b-form-invalid-feedback
                  v-if="checkDate === true"
                  align="center"
                >
                  いつからの日付は必ずいつまでの日付より小さくて選択してください。
                </b-form-invalid-feedback>
              </td>
            </tr>
          </tbody>
        </table>
      </b-col>
    </b-row>
    <b-row style="margin-bottom: 1rem">
      <b-col class="text-right">
        <b-button
          variant="outline-success"
          @click="goToCreate"
        >
          {{ $t('models.notification.screen_name.create') }}
          <i class="far fa-envelope" />
        </b-button>
      </b-col>
    </b-row>
    <div class="card">
      <b-table-simple
        hover
        bordered
        striped
      >
        <b-thead align="center">
          <b-th colspan="3">
            サイトからのお知らせ
          </b-th>
        </b-thead>
        <b-tbody>
          <b-tr
            v-for="(data, index) in paginatedItems"
            :key="index"
            @click="goToDetail(data.id)"
          >
            <b-td
              class="align-middle"
              style="width: 130px"
            >
              <div
                class="text-center"
                style="padding: 2px"
                :style="colorIsImportant(data.notificationDiv)"
              >
                {{ data.notificationDivName }}
              </div>
            </b-td>
            <b-td
              class="align-middle"
              style="width: 200px"
            >
              {{ data.sentAt }}
            </b-td>
            <b-td class="align-middle">
              {{ data.title }}
            </b-td>
          </b-tr>
        </b-tbody>
        <b-tbody v-if="notifications.length === 0">
          <b-tr>
            <b-td
              align="center"
              colspan="3"
            >
              データが選択されていません。
            </b-td>
          </b-tr>
        </b-tbody>
      </b-table-simple>
      <div class="card-footer">
        <div class="level-left text-center">
          {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
        </div>
        <b-pagination
          v-if="filterItems.length > perPage"
          v-model="currentPage"
          :total-rows="total"
          :per-page="perPage"
          align="center"
          @change="onPageChanged"
        />
      </div>
    </div>
  </div>
</template>

<script>
import VDatetimePicker from '@/components/common/molecules/VDatetimePicker'
import VDropdown from '@/components/common/molecules/VDropdown'
import moment from 'moment'
export default {
  name: 'NotificationList',
  components: { VDatetimePicker, VDropdown },
  props: {
    notification: {
      type: Array,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      keyChanging: 0,
      notifications: this.notification || [],
      paginatedItems: this.filterItems,
      perPage: 15,
      currentPage: 1,
      filter: {
        title: null,
        notificationDateStart: null,
        notificationDateEnd: null,
        notificationStatus: null
      },
      appliedFilter: {
        title: null,
        notificationDateStart: null,
        notificationDateEnd: null,
        notificationStatus: null
      },
      notificationDateStart: null,
      notificationDateEnd: null
    }
  },
  computed: {
    filterItems () {
      let resultItems = [...this.notifications]
      for (let i = 0; i < resultItems.length; i++) {
        if (resultItems[i].sentAt) {
          let timeStamp = resultItems[i].sentAt
          let year = moment(timeStamp).format('YYYY')
          let month = moment(timeStamp).format('M')
          let date = moment(timeStamp).format('D')
          if (month > 0 && month < 10) {
            month = '0' + month
          }
          if (date > 0 && date < 10) {
            date = '0' + date
          }
          let time = year + '' + month + '' + date
          time = parseInt(time)
          resultItems[i].sendAtConvert = time
        }
      }
      if (this.appliedFilter.title) {
        resultItems = resultItems.filter(item => item.title.toUpperCase().includes(this.appliedFilter.title.toUpperCase()))
      }
      if (this.appliedFilter.notificationStatus) {
        resultItems = resultItems.filter(item => item.notificationDiv == this.appliedFilter.notificationStatus)
      }
      if (this.appliedFilter.notificationDateStart) {
        resultItems = resultItems.filter(item => item.sendAtConvert >= this.filter.notificationDateStart)
      }
      if (this.appliedFilter.notificationDateEnd) {
        resultItems = resultItems.filter(item => item.sendAtConvert <= this.filter.notificationDateEnd)
      }
      return resultItems
    },
    total () {
      return this.filterItems.length
    },
    from () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
      }
    },
    checkDate () {
      let data = Date.parse(this.notificationDateStart) - Date.parse(this.notificationDateEnd) || null
      if (data) {
        if (data < 0) {
          return false
        }
        return true
      }
      return false
    },
  },
  mounted () {
    this.search()
    this.paginate(this.perPage, 0)
  },
  methods: {
    goToCreate () {
      location.href = this.route('admin.notification.create')
    },
    goToDetail (id) {
      location.href = this.route('admin.notification.show', id)
    },
    paginate (pageSize, pageNumber) {
      let itemsToParse = this.filterItems
      this.paginatedItems = itemsToParse.slice(
        pageNumber * pageSize, (pageNumber + 1) * pageSize
      )
    },
    onPageChanged (page) {
      this.paginate(this.perPage, page - 1)
    },
    search () {
      function dateConvertToNumber (datetime) {
        if (datetime) {
          let d = new Date(datetime)
          let year = moment(d).format('YYYY')
          let month = moment(d).format('M')
          let date = moment(d).format('D')
          if (month > 0 && month < 10) {
            month = '0' + month
          }
          if (date > 0 && date < 10) {
            date = '0' + date
          }
          datetime = year + '' + month + '' + date
        }
        return parseInt(datetime)
      }
      if (typeof (this.notificationDateStart) !== 'number') {
        this.filter.notificationDateStart = dateConvertToNumber(this.notificationDateStart)
      }
      if (typeof (this.announcementDateEnd) !== 'number') {
        this.filter.announcementDateEnd = dateConvertToNumber(this.notificationDateEnd)
      }
      this.appliedFilter = { ...this.filter }
      this.paginatedItems = this.filterItems
      this.paginate(this.perPage, 0)
    },
    clearAnnouncementDateStart () {
      this.notificationDateStart = null
      this.keyChanging++
    },
    clearAnnouncementDateEnd () {
      this.notificationDateEnd = null
      this.keyChanging++
    },
    colorIsImportant (notificationDiv) {
      if (notificationDiv === 8) {
        return { 'backgroundColor': '#ffff14' }
      } else if (notificationDiv === 7) {
        return { 'backgroundColor': '#c51414', 'color': '#ffffff' }
      }
    }
  }
}
</script>

<style scoped>

</style>
