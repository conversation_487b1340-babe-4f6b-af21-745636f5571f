<template>
  <div class="card-body">
    <b-table-simple
      bordered
      small
    >
      <b-tbody class="custom-table-notification">
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.notification_div') }}
          </b-th>
          <b-td colspan="2">
            <v-dropdown
              v-model="notificationDiv"
              name="notificationDiv"
              :validation-filed="$t('models.notification.field.notification_div')"
              :is-label="false"
              :is-select="true"
              title-name="ステータス"
              :value-title="0"
              :options="$root.lazyOption.notificationDivOption"
            />
          </b-td>
        </b-tr>
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.sent_at') }}
          </b-th>
          <b-td>
            <v-datetime-picker
              :key="keyChanging"
              v-model="sentAt"
              :validation-filed="$t('models.notification.field.sent_at')"
              validation-rules="required"
              :is-label="false"
              :is-disabled="disableInput"
              name="sentAt"
              date-type="datetime"
            />
          </b-td>
          <b-td
            class="custom-btn-send-now"
            variant="info"
            @click="sendNow"
          >
            {{ $t('common.btn.send_now') }}
          </b-td>
        </b-tr>
        <b-tr>
          <b-th>
            {{ $t('models.notification.field.title') }}
          </b-th>
          <b-td colspan="2">
            <v-input
              v-model="title"
              :validation-filed="$t('models.notification.field.title')"
              validation-rules="required|max:100"
              type="text"
              :is-label="false"
              name="title"
              maxlength="100"
            />
          </b-td>
        </b-tr>
        <b-tr>
          <b-td
            colspan="3"
            class="custom-input-message"
          >
            <v-input
              v-model="body"
              :validation-filed="$t('models.notification.field.body')"
              validation-rules="required"
              type="textarea"
              :is-label="false"
              name="body"
              maxlength="5000"
            />
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
  </div>
</template>

<script>
import VInput from '@/components/common/molecules/VInput'
import VDatetimePicker from '@/components/common/molecules/VDatetimePicker'
import VDropdown from '@/components/common/molecules/VDropdown'
import moment from 'moment'
export default {
  name: 'NotificationForm',
  components: { VDropdown, VDatetimePicker, VInput },
  props: {
    notifications: {
      type: Object,
      default: () => ({
        title: null,
        body: null,
        notification_div: null,
        sent_at: null
      })
    }
  },
  data () {
    const { title, body, notificationDiv, sentAt } = this.notifications
    return {
      keyChanging: 0,
      title,
      body,
      notificationDiv,
      sentAt,
      disableInput: false,
      isSendNow: true
    }
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    },
    sendNow () {
      this.isSendNow = !this.isSendNow
      if (this.isSendNow) {
        this.sentAt = moment().format('YYYY-MM-DD HH:mm')
      }
      this.isSendNow ? this.disableInput = true : this.disableInput = false
      this.keyChanging++
    }
  }
}
</script>

<style scoped>
  .custom-table-notification th {
    text-align: center;
    width: 250px;
    vertical-align: middle;
  }
  .custom-btn-send-now {
    width: 120px;
    text-align: center;
    vertical-align: middle;
  }
</style>
