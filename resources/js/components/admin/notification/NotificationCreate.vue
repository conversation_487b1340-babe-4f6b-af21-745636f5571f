<template>
  <div class="card">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.notification.store')"
      >
        <notification-form
          ref="notificationForm"
          :notifications="notifications"
        />
        <!-- Card-body -->
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.notification.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(openCommonModal)"
          >
            {{ $t('models.notification.button.save') }}
          </button>
        </div>
      </v-form>
      <v-modal
        title="お知ら作成"
        ok-text="送る"
        :handle-submitting="submitForm"
        ok-variant="primary"
      >
        <p
          class="my-4"
        >
          お知ら作成?
        </p>
      </v-modal>
    </ValidationObserver>
  </div>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import NotificationForm from '@/components/admin/notification/common/NotificationForm'
import VModal from '@/components/common/molecules/VModal'
export default {
  name: 'NotificationCreate',
  components: { NotificationForm, VForm, ValidationObserver, VModal },
  props: {
    notifications: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    openCommonModal () {
      this.$bvModal.show('common-modal')
    },
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>
