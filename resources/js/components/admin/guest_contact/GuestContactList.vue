<template>
  <div>
    <b-row style="margin-bottom: 1rem; padding: 0 15px">
      <b-col
        cols="5"
        class="custom-col-search"
      >
        <b-form-input
          v-model="filter.text"
          size="sm"
          type="text"
          placeholder="お名前/メールアドレス"
          @keyup.enter="search"
        />
      </b-col>
      <b-col
        cols="5"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filter.contactDiv"
          size="sm"
          :options="$root.lazyOption.contactDivOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              お問合せ
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col>
        <b-button
          variant="outline-info"
          class="mb-2"
          size="sm"
          @click="search"
        >
          <b-icon
            icon="search"
            aria-hidden="true"
          />
        </b-button>
      </b-col>
    </b-row>
    <b-row style="margin-bottom: 1rem">
      <b-col cols="6">
        <b-button-group>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortOldDate"
          >
            新しい日付
          </b-button>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortNewDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <div class="card">
      <b-table-simple
        bordered
        class="custom-table"
      >
        <b-thead>
          <b-th>
            項番
          </b-th>
          <b-th>
            {{ $t('models.guest_contact.field.id') }}
          </b-th>
          <b-th>
            {{ $t('models.guest_contact.field.name') }}
          </b-th>
          <b-th>
            {{ $t('models.guest_contact.field.email') }}
          </b-th>
          <b-th>
            {{ $t('models.guest_contact.field.created_at') }}
          </b-th>
          <b-th>
            {{ $t('models.guest_contact.field.contact_div') }}
          </b-th>
          <b-th>
            <i class="far fa-envelope fa-2x" />
          </b-th>
          <b-th>
            =
          </b-th>
        </b-thead>
        <b-tbody
          v-for="(data, index) in paginatedItems"
          :key="index"
        >
          <b-tr v-if="data.hasFeedback">
            <b-td rowspan="2">
              {{ index+1 }}
            </b-td>
            <b-td>
              {{ data.id }}
            </b-td>
            <b-td rowspan="2">
              {{ data.name }}
            </b-td>
            <b-td rowspan="2">
              {{ data.email }}
            </b-td>
            <b-td>
              {{ data.createdAt }}
            </b-td>
            <b-td rowspan="2">
              {{ data.contactDivName }}
            </b-td>
            <b-td rowspan="2" />
            <b-td @click="goToDetail(data.id)">
              =
            </b-td>
          </b-tr>
          <b-tr v-if="data.hasFeedback">
            <b-td>
              {{ data.feedbackId }}
            </b-td>
            <b-td>
              {{ data.feedbackAt }}
            </b-td>
            <b-td @click="goToFeedbackDetail(data.feedbackId)">
              =
            </b-td>
          </b-tr>
          <b-tr v-else>
            <b-td>
              {{ index + 1 }}
            </b-td>
            <b-td>
              {{ data.id }}
            </b-td>
            <b-td>
              {{ data.name }}
            </b-td>
            <b-td>
              {{ data.email }}
            </b-td>
            <b-td>
              {{ data.createdAt }}
            </b-td>
            <b-td>
              {{ data.contactDivName }}
            </b-td>
            <b-td @click="goToSendFeedback(data.id)">
              <i class="far fa-envelope fa-2x" />
            </b-td>
            <b-td @click="goToDetail(data.id)">
              =
            </b-td>
          </b-tr>
        </b-tbody>
        <b-tbody v-if="guestContacts.length === 0">
          <b-tr>
            <b-td
              align="center"
              colspan="8"
            >
              データが選択されていません。
            </b-td>
          </b-tr>
        </b-tbody>
      </b-table-simple>
      <div class="card-footer">
        <div>
          <div class="level-left">
            {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
          </div>
          <b-pagination
            v-if="filterItems.length > perPage"
            v-model="currentPage"
            :total-rows="total"
            :per-page="perPage"
            aria-controls="chat"
            @change="onPageChanged"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GuestContactList',
  props: {
    guestContact: {
      type: [Object, Array],
      default: function () {
        return {
        }
      }
    }
  },
  data () {
    return {
      guestContacts: this.guestContact || [],
      paginatedItems: this.filterItems,
      perPage: 15,
      currentPage: 1,
      filter: {
        contactDiv: null,
        text: null
      },
      appliedFilter: {
        contactDiv: null,
        text: null
      }
    }
  },
  computed: {
    filterItems () {
      let resultItems = [...this.guestContacts]
      if (this.appliedFilter.contactDiv) {
        resultItems = resultItems.filter(item => item.contactDiv == this.appliedFilter.contactDiv)
      }
      if (this.appliedFilter.text) {
        resultItems = resultItems.filter(item => item.name == this.appliedFilter.text || item.email == this.appliedFilter.text)
      }
      return resultItems
    },
    total () {
      return this.filterItems.length
    },
    from () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
    }
  },
  mounted () {
    this.search()
    this.paginate(this.perPage, 0)
  },
  methods: {
    goToDetail (id) {
      location.href = this.route('admin.guest_contact.show', id)
    },
    goToSendFeedback (id) {
      location.href = this.route('admin.feedback_guest_contact.create', id)
    },
    goToFeedbackDetail (id) {
      location.href = this.route('admin.feedback_guest_contact.show', id)
    },
    sortOldDate () {
      this.paginatedItems = this.paginatedItems.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
    },
    sortNewDate () {
      this.paginatedItems = this.paginatedItems.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
    },
    paginate (pageSize, pageNumber) {
      let itemsToParse = this.filterItems
      this.paginatedItems = itemsToParse.slice(
        pageNumber * pageSize, (pageNumber + 1) * pageSize
      )
    },
    onPageChanged (page) {
      this.paginate(this.perPage, page - 1)
    },
    search () {
      this.appliedFilter = { ...this.filter }
      this.paginatedItems = this.filterItems
      this.paginate(this.perPage, 0)
    }
  }
}
</script>

<style scoped>

</style>
