<template>
  <div class="card box">
    <div class="card">
      <b-table-simple bordered>
        <b-thead align="center">
          <b-th colspan="2">
            会員情報
          </b-th>
          <b-th>
            証明書
          </b-th>
          <b-th>
            否認
          </b-th>
        </b-thead>
        <b-tbody
          v-for="(data, index) in paginatedItems"
          :key="index"
          align="center"
          class="custom-tr-body"
        >
          <!--image modal-->
          <div
            id="fsModal"
            class="modal animated bounceIn"
            tabindex="-1"
            role="dialog"
            aria-labelledby="myModalLabel"
            aria-hidden="true"
          >
            <!-- dialog -->
            <div class="modal-dialog">
              <div
                class="modal-body"
                style="text-align: center;vertical-align: middle"
              >
                <div class="test-modal">
                  <h2>1. Modal sub-title</h2>
                  <b-img
                    style=" margin: 80px"
                    :src="imageFullSreen"
                    center
                    thumbnail
                    fluid
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- v-for -->
          <b-tr>
            <b-td style="width: 150px">
              {{ data.memberId }}
            </b-td>
            <b-td style="width: 150px">
              {{ data.createdAtFormat }}
            </b-td>
            <b-td rowspan="5">
              <b-img
                :src="data.image"
                style="width: auto; max-height: 350px"
                class="btn-modal"
                data-toggle="modal"
                data-target="#fsModal"
                thumbnail
                fluidindex
                @click="goToImageFullScreen(data.image)"
              />
            </b-td>
            <b-td
              rowspan="5"
              style="width: 100px"
            >
              <b-form-checkbox
                v-model="userIds"
                name="approval_status"
                :value="data.userId"
                button
                button-variant="outline-danger"
              >
                {{ $t('models.user_verification.button.decline') }}
              </b-form-checkbox>
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="2">
              {{ data.nickname }}
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="2">
              {{ data.gender }}
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="2">
              {{ data.birthday }}
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="2">
              {{ data.identificationName }}
            </b-td>
          </b-tr>
        </b-tbody>
        <b-tbody v-if="userVerifications.length === 0">
          <b-tr>
            <b-td
              align="center"
              colspan="5"
            >
              データが選択されていません。
            </b-td>
          </b-tr>
        </b-tbody>
      </b-table-simple>
      <div class="card-footer">
        <div>
          <div class="level-left">
            {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
          </div>
          <b-pagination
            v-if="userVerifications.length > perPage"
            v-model="currentPage"
            :total-rows="total"
            :per-page="perPage"
            @change="onPageChanged"
          />
          <b-button
            v-if="userVerifications.length > 0"
            class="custom-button"
            variant="success"
            squared
            block
            @click="openCommonModal"
          >
            {{ $t('models.user_verification.button.approve') }}
          </b-button>
        </div>
      </div>
    </div>

    <!-- Modal Submit -->
    <v-modal
      title="承認"
      ok-variant="primary"
      :handle-submitting="submitModal"
    >
      <p class="my-4">
        身分証明書を承認しますか？
      </p>
    </v-modal>
  </div>
</template>

<script>
import VModal from '@/components/common/molecules/VModal'
import { extractLaravelError } from '@/utils/helpers/promise'
import axios from 'axios'

export default {
  name: 'UserVerificationList',
  components: { VModal },
  props: {
    userVerification: {
      type: [Object, Array],
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      imageFullSreen: null,
      keyChanging: 0,
      userVerifications: this.userVerification || [],
      paginatedItems: this.userVerification,
      declineClick: [],
      perPage: 10,
      currentPage: 1,
      mainProps: { width: 500, height: 350 },
      userIds: []
    }
  },
  computed: {
    total () {
      return this.userVerifications.length
    },
    from () {
      if (this.userVerifications.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
    }
  },
  mounted () {
    this.paginate(this.perPage, 0)
  },
  methods: {
    goToImageFullScreen (url) {
      this.imageFullSreen = url
      this.$refs['fsModal'].show()
    },
    paginate (pageSize, pageNumber) {
      let itemsToParse = this.userVerifications
      this.paginatedItems = itemsToParse.slice(
        pageNumber * pageSize, (pageNumber + 1) * pageSize
      )
    },
    onPageChanged (page) {
      this.paginate(this.perPage, page - 1)
    },
    openCommonModal () {
      this.$bvModal.show('common-modal')
    },
    async submitModal () {
      try {
        let data = null
        if (this.userIds.length === 0) {
          data = 0
        } else {
          data = this.userIds
        }
        await axios.post(this.route('admin.user_verification.approve', { user_id: data }))
        location.href = this.route('admin.user_verification.index')
      } catch (e) {
        this.$nextTick(() => {
          throw new Error(extractLaravelError(e))
        })
      }
    }
  }
}
</script>

<style scoped>
.custom-tr-body tr td {
  vertical-align: middle;
}
.custom-button {
  height: 75px;
}

.test-modal {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  display: flex;
  position: fixed;
}

.modal {
  position: fixed;
  text-align: center;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
}

.modal-dialog {
  position: fixed;
  text-align: center;
  vertical-align: middle;
  margin: 0;
  width: 100%;
  height: 100%;
  padding: 0;
}

.modal-body {
  position: absolute;
  text-align: center;
  vertical-align: middle;
  top: 0;
  bottom: 0;
  width: 100%;
  font-weight: 300;
  overflow: auto;
}
</style>
