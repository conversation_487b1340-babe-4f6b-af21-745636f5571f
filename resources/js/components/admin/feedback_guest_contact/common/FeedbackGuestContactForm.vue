<template>
  <b-table-simple bordered>
    <b-tbody>
      <b-tr>
        <b-th class="th-form-width">
          {{ $t('models.guest_contact.field.id') }}
        </b-th>
        <b-td class="td-form-width">
          {{ guestContact.id }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-th class="th-form-width">
          {{ $t('models.guest_contact.field.name') }}
        </b-th>
        <b-td class="td-form-width">
          {{ guestContact.name }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-th class="th-form-width">
          {{ $t('models.guest_contact.field.email') }}
        </b-th>
        <b-td class="td-form-width">
          {{ guestContact.email }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-th class="th-form-width">
          {{ $t('models.guest_contact.field.contact_div') }}
        </b-th>
        <b-td class="td-form-width">
          {{ guestContact.contactDivName }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-th class="th-form-width">
          {{ $t('models.guest_contact.field.created_at') }}
        </b-th>
        <b-td class="td-form-width">
          {{ guestContact.createdAt }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-td
          colspan="2"
          class="feedback-form-height"
        >
          {{ guestContact.body }}
        </b-td>
      </b-tr>
      <b-tr>
        <b-td
          colspan="2"
          class="custom-input-feedback"
        >
          <v-input
            v-model="content"
            name="content"
            type="textarea"
            :is-label="false"
            maxlength="5000"
          />
        </b-td>
        <v-input
          v-show="false"
          v-model="email"
          name="guest_user_id"
          type="text"
        />
        <v-input
          v-show="false"
          v-model="id"
          name="foreign_id"
          type="text"
        />
      </b-tr>
    </b-tbody>
  </b-table-simple>
</template>

<script>
import VInput from '@/components/common/molecules/VInput'
export default {
  name: 'FeedbackGuestContactForm',
  components: { VInput },
  props: {
    guestContact: {
      type: [Object, Array],
      default: () => {}
    }
  },
  data () {
    const { content, email, id } = this.guestContact
    return {
      content,
      email,
      id
    }
  },
  methods: {
    getValidationState ({ dirty, validated, valid = null }) {
      return dirty || validated ? valid : null
    }
  }
}
</script>

<style scoped>
.feedback-form-height {
  height: 150px;
}
.th-form-width {
  width: 30%;
  text-align: center;
}
.td-form-width {
  text-align: center;
}
</style>
