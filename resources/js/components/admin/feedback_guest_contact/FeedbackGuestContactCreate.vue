<template>
  <div class="card">
    <ValidationObserver v-slot="{failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.feedback_guest_contact.store')"
      >
        <feedback-guest-contact-form
          ref="feedbackGuestContactForm"
          :guest-contact="guestContact"
        />
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.guest_contact.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(openCommonModal)"
          >
            {{ $t('common.btn.send') }}
          </button>
        </div>
      </v-form>
      <v-modal
        title="お知ら作成"
        ok-text="送る"
        :handle-submitting="submitForm"
        ok-variant="primary"
      >
        <p
          class="my-4"
        >
          お知ら作成?
        </p>
      </v-modal>
    </ValidationObserver>
  </div>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import FeedbackGuestContactForm from '@/components/admin/feedback_guest_contact/common/FeedbackGuestContactForm'
import VModal from '@/components/common/molecules/VModal'
export default {
  name: 'FeedbackGuestContactCreate',
  components: { FeedbackGuestContactForm, ValidationObserver, VForm, VModal },
  props: {
    guestContact: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    openCommonModal () {
      this.$bvModal.show('common-modal')
    },
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>

<style scoped>

</style>
