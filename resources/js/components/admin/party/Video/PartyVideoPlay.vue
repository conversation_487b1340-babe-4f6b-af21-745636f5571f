<template>
  <div>
    <div
      class="center-block"
      style="text-align: center"
    >
      <video
        ref="video"
        style="text-align: center;vertical-align: middle"
        width="1024"
        height="768"
        autoplay="autoplay"
        muted
        controls
      >
        <source
          :src="source"
          type="video/mp4"
        >
      </video>
      <!--      <video-->
      <!--        ref="video"-->
      <!--        width="100%"-->
      <!--        height="640"-->
      <!--        controls-->
      <!--      />-->
    </div>
    <!-- Footer -->
    <div
      class="card-footer text-center"
      style="margin-top: 10px; margin-bottom: 10px"
    >
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.party.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </div>
</template>

<script>

import HLSCore from '@core-player/playcore-hls'
import Hls from 'hls.js'

export default {
  name: 'PartyVideoPlay',
  props: {
    source: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      HLSCore
    }
  },
  mounted () {
    // let hls = new Hls()
    // let stream = 'https://rimoccha.s3-ap-northeast-1.amazonaws.com/public/party/01c7e5465442e4557967c29227b0ae94_tu_party_5.m3u8'
    // let video = this.$refs['video']
    // hls.loadSource(stream)
    // hls.attachMedia(video)
    // hls.on(Hls.Events.MANIFEST_PARSED, function () {
    //   video.play()
    // })
  }
}
</script>

<style scoped>

</style>
