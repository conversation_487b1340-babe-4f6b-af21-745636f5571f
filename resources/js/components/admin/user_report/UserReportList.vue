<template>
  <div class="card box">
    <b-row>
      <b-col>
        <table class="table table-sm tbl-detail-info custom-table-search">
          <tbody>
            <tr>
              <td>
                <b-form-input
                  v-model="filter.text"
                  size="sm"
                  type="text"
                  placeholder="飲み会ID/会員ID/ニックネーム"
                  @keyup.enter="search"
                />
              </td>
              <td class="custom-td-search">
                通報日
              </td>
              <td>
                <v-datetime-picker
                  :key="keyChanging"
                  v-model="reportedDateStart"
                  :is-label="false"
                  date-type="date"
                />
              </td>
              <td>
                <b-button
                  class="s-clear"
                  size="sm"
                  @click="clearReportedDateStart"
                >
                  クリア
                </b-button>
              </td>
              <td class="custom-td-search">
                ~
              </td>
              <td>
                <v-datetime-picker
                  :key="keyChanging"
                  v-model="reportedDateEnd"
                  :is-label="false"
                  date-type="date"
                />
              </td>
              <td>
                <b-button
                  class="s-clear"
                  size="sm"
                  @click="clearReportedDateEnd"
                >
                  クリア
                </b-button>
              </td>
              <td>
                <b-form-select
                  v-model="filter.status"
                  :options="$root.lazyOption.reportStatusOption"
                  size="sm"
                >
                  <template #first>
                    <b-form-select-option
                      :value="null"
                    >
                      ステータス
                    </b-form-select-option>
                  </template>
                </b-form-select>
              </td>
              <td>
                <b-button
                  variant="outline-info"
                  class="mb-2"
                  size="sm"
                  @click="search"
                >
                  <b-icon
                    icon="search"
                    aria-hidden="true"
                  />
                </b-button>
              </td>
            </tr>
          </tbody>
        </table>
      </b-col>
    </b-row>
    <b-row style="margin-bottom: 1rem">
      <b-col cols="6">
        <b-button-group>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortNewDate"
          >
            新しい日付
          </b-button>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortOldDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <div class="card">
      <b-table-simple
        bordered
        small
      >
        <b-thead align="center">
          <b-th>
            {{ $t('models.user_report.field.party_id') }}
          </b-th>
          <b-th>
            {{ $t('models.user_report.field.user_id') }}
          </b-th>
          <b-th>
            {{ $t('models.user_report.field.category') }}
          </b-th>
          <b-th>
            {{ $t('models.user_report.field.status') }}
          </b-th>
          <b-th>
            {{ $t('models.user_report.field.warning') }}
          </b-th>
          <b-th colspan="3" />
        </b-thead>
        <b-tbody
          v-for="(data, index) in paginatedItems"
          :key="index"
        >
          <b-tr class="custom-tr" style="background-color: #f6f6f6 ">
            <b-td style="width: 180px">
              {{ data.partyId }}
            </b-td>
            <b-td style="width: 180px">
              {{ data.memberId }}
            </b-td>
            <b-td style="width: 280px">
              {{ data.reportDivName }}
            </b-td>
            <b-td style="width: 180px">
              <span
                :style="{color : data.reportStatus === 1 ? 'red' : 'black'}"
              >
                {{ data.reportStatusName }}
              </span>
            </b-td>
            <b-td style="width: 180px">
              <span
                :style="{color : data.warning === 1 ? 'red' : 'black'}"
              >
                 {{ data.warningName }}
              </span>
            </b-td>
            <b-td
              style="width: 960px; text-align: right; padding-right: 40px"
              class="custom-contact-btn"
            >
              <b-button
                variant="link"
                class="btn"
                @click="goToSendReport(data.userId, data.id, data.genderUserId)"
              >
                <i class="far fa-envelope fa-2x" />
              </b-button>
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="6">
              <span style="padding-right: 20px">通報日: {{ data.reportDate }}</span>
              <span style="padding-right: 20px">飲み会日: {{ data.timePartyStart }}</span>
              <span class="custom-span">飲み会時間{{ data.partyTime }}分 /</span>
              <span class="custom-span">該当時間: {{ data.timeReport }}</span>
              <a
                href="#"
                class="custom-span"
                @click="goToPartyDetail"
              >
                飲み会リンク
              </a>
            </b-td>
          </b-tr>
          <b-tr>
            <b-td
              colspan="6"
            >
              <div>友達飲み会</div>
              <div>
                男性：
                <span
                  v-for="(userInfo, newIndex) in data.memberIsMale"
                  :key="newIndex"
                >
                  <span>
                    <a
                      href="#"
                      class="custom-span"
                      @click="goToUserDetail(userInfo.userId)"
                    >
                      {{ userInfo.nickname }} &nbsp;&nbsp;会員ID {{ userInfo.memberId }},
                    </a>
                  </span>
                </span>
              </div>
              <div>
                女性：
                <span
                  v-for="(userInfo, newIndex) in data.memberIsFemale"
                  :key="newIndex"
                >
                  <span>
                    <a
                      href="#"
                      class="custom-span"
                      @click="goToUserDetail(userInfo.userId)"
                    >
                      {{ userInfo.nickname }} &nbsp;&nbsp;会員ID {{ userInfo.memberId }},
                    </a>
                  </span>
                </span>
              </div>
            </b-td>
          </b-tr>
          <b-tr>
            <b-td colspan="6">
              <b-col />
              <span style="padding-right: 5px">通報項目：</span>
              <span style="padding-right: 50px">
                {{ data.reportDivName }}
              </span>
              <span>確認会員：</span>
              <span class="custom-span" v-if="data.genderReportedId === 1">
                男性会員
              </span>
              <span class="custom-span" v-else>
                女性会員
              </span>
              <span>
                <a
                  href="#"
                  class="custom-span"
                  @click="goToUserDetail(data.userReportedId)"
                >
                  {{ data.nicknameReportedUser }} &nbsp;&nbsp;会員ID {{ data.reportedMemberId }}
                </a>
              </span>
            </b-td>
          </b-tr>
          <b-tr class="custom-reason-report">
            <b-td
              colspan="6"
              v-html="data.content"
            />
          </b-tr>
        </b-tbody>
      </b-table-simple>
      <div class="card-footer">
        <div class="level-left">
          {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
        </div>
        <b-pagination
          v-if="filterItems.length > perPage"
          v-model="currentPage"
          :total-rows="total"
          :per-page="perPage"
          @change="onPageChanged"
        />
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import VDatetimePicker from '@/components/common/molecules/VDatetimePicker'
import moment from 'moment'
export default {
  name: 'UserReportList',
  components: { VDatetimePicker },
  props: {
    userReport: {
      type: Array,
      default: function () {
        return {
        }
      }
    }
  },
  data () {
    return {
      keyChanging: 0,
      perPage: 10,
      currentPage: 1,
      paginatedItems: this.filterItems,
      userReports: this.userReport,
      filter: {
        status: null,
        reportedDateStart: null,
        reportedDateEnd: null,
        text: null
      },
      appliedFilter: {
        status: null,
        reportedDateStart: null,
        reportedDateEnd: null,
        text: null
      },
      reportedDateStart: null,
      reportedDateEnd: null
    }
  },
  computed: {
    filterItems () {
      let resultItems = [...this.userReports]

      for (let i = 0; i < resultItems.length; i++) {
        if (resultItems[i].createdAt) {
          let timeStamp = resultItems[i].createdAt
          let year = moment(timeStamp).format('YYYY')
          let month = moment(timeStamp).format('M')
          let date = moment(timeStamp).format('D')
          if (month > 0 && month < 10) {
            month = '0' + month
          }
          if (date > 0 && date < 10) {
            date = '0' + date
          }
          let time = year + '' + month + '' + date
          time = parseInt(time)
          resultItems[i].createdAtConvert = time
        }
      }
      if (this.appliedFilter.status) {
        resultItems = resultItems.filter(item => item.reportStatus == this.appliedFilter.status)
      }
      if (this.appliedFilter.text) {
        resultItems = resultItems.filter(item => item.nicknameReportedUser.toUpperCase().includes(this.appliedFilter.text.toUpperCase()) || item.partyId == this.appliedFilter.text || item.memberId == this.appliedFilter.text || item.reportedMemberId == this.appliedFilter.text)
      }
      if (this.appliedFilter.reportedDateStart) {
        resultItems = resultItems.filter(item => item.createdAtConvert >= this.filter.reportedDateStart)
      }
      if (this.appliedFilter.reportedDateEnd) {
        resultItems = resultItems.filter(item => item.createdAtConvert <= this.filter.reportedDateEnd)
      }
      return resultItems
    },
    total () {
      return this.filterItems.length
    },
    from () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
      }
    }
  },
  mounted () {
    this.search()
    this.paginate(this.perPage, 0)
  },
  methods: {
    paginate (pageSize, pageNumber) {
      let itemsToParse = this.filterItems
      this.paginatedItems = itemsToParse.slice(
        pageNumber * pageSize, (pageNumber + 1) * pageSize
      )
    },
    onPageChanged (page) {
      this.paginate(this.perPage, page - 1)
    },
    async updateReportStatus (data) {
      try {
        await axios.post(this.route('admin.user_report.update', data.id), { 'report_status': data.reportStatus })
        location.href = this.route('admin.user_report.index')
      } catch (e) {
        this.$nextTick(() => {
          throw new Error(extractLaravelError(e))
        })
      }
    },
    goToPartyDetail () {
      console.log('here')
    },
    goToUserDetail (userId) {
      location.href = this.route('admin.user.show', [userId, 0])
    },
    goToSendReport (userId, reportId, gender) {
      location.href = this.route('admin.feedback_report.create') + '?user_id=' + userId + '&report_id=' + reportId + '&gender=' + gender
    },
    search () {
      function dateConvertToNumber (datetime) {
        if (datetime) {
          let d = new Date(datetime)
          let year = moment(d).format('YYYY')
          let month = moment(d).format('M')
          let date = moment(d).format('D')
          if (month > 0 && month < 10) {
            month = '0' + month
          }
          if (date > 0 && date < 10) {
            date = '0' + date
          }
          datetime = year + '' + month + '' + date
        }
        return parseInt(datetime)
      }
      if (typeof (this.reportedDateStart) !== 'number') {
        this.filter.reportedDateStart = dateConvertToNumber(this.reportedDateStart)
      }
      if (typeof (this.reportedDateEnd) !== 'number') {
        this.filter.reportedDateEnd = dateConvertToNumber(this.reportedDateEnd)
      }
      this.appliedFilter = { ...this.filter }
      this.paginatedItems = this.filterItems
      this.onPageChanged(1)
    },
    sortNewDate () {
      this.userReports = this.userReports.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
      this.paginatedItems = this.filterItems
      this.paginate(this.perPage, 0)
    },
    sortOldDate () {
      this.userReports = this.userReports.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
      this.paginatedItems = this.filterItems
      this.paginate(this.perPage, 0)
    },
    clearReportedDateStart () {
      this.reportedDateStart = null
      this.keyChanging++
    },
    clearReportedDateEnd () {
      this.reportedDateEnd = null
      this.keyChanging++
    }
  }
}
</script>

<style scoped>
    .custom-span {
        padding-right: 15px;
    }
</style>
