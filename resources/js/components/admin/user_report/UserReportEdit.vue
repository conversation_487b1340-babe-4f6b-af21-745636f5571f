<template>
  <div class="card">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.user_report.update', user_report.id)"
        method="put"
      >
        <user-report-form
          :user-report="user_report"
          :is-edit="true"
        />
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.user_report.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.save') }}
          </button>
        </div>
      </v-form>
    </ValidationObserver>
  </div>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import UserReportForm from '@/components/admin/user_report/common/UserReportForm'
export default {
  name: 'UserReportEdit',
  components: { UserReportForm, VForm, ValidationObserver },
  props: {
    user_report: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>
