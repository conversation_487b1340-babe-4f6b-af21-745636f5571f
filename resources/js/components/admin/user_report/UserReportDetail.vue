<template>
  <div class="card">
    <div class="card-body">
      <!-- User Id Field -->
      <div class="row">
        <label class="col-lg-2 col-4 col-form-label">{{ $t('models.user_report.field.user_id') }}</label>
        <div class="col-form-detail">
          {{ user_report.userId }}
        </div>
      </div>

      <!-- Report Div Field -->
      <div class="row">
        <label class="col-lg-2 col-4 col-form-label">{{ $t('models.user_report.field.report_div') }}</label>
        <div class="col-form-detail">
          {{ user_report.reportDivName }}
        </div>
      </div>

      <!-- Detail Reason Field -->
      <div class="row">
        <label class="col-lg-2 col-4 col-form-label">{{ $t('models.user_report.field.content') }}</label>
        <div class="col-form-detail">
          {{ user_report.content }}
        </div>
      </div>

      <!-- Created At Field -->
      <div class="row">
        <label class="col-lg-2 col-4 col-form-label">{{ $t('common.created_at') }}</label>
        <div class="col-form-detail">
          {{ user_report.createdAt }}
        </div>
      </div>

      <!-- Updated At Field -->
      <div class="row">
        <label class="col-lg-2 col-4 col-form-label">{{ $t('common.updated_at') }}</label>
        <div class="col-form-detail">
          {{ user_report.updatedAt }}
        </div>
      </div>

    </div>
    <div class="card-footer text-center">
      <a
        class="btn btn-default"
        :href="route('admin.user_report.index')"
      >
        {{ $t('common.btn.back') }}
      </a>
    </div>
  </div>
</template>
<script>
export default {
  name: 'UserReportDetail',
  props: {
    user_report: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
