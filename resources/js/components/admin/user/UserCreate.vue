<template>
  <b-card border-variant="info">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.user.store')"
      >
        <user-form
          :user="user"
        />
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.user.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.save') }}
          </button>
        </div>
      </v-form>
    </ValidationObserver>
  </b-card>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import UserForm from '@/components/admin/user/common/UserForm'
export default {
  name: 'UserCreate',
  components: { UserForm, VForm, ValidationObserver },
  props: {
    user: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>
