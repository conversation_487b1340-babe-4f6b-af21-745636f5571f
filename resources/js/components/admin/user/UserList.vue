<template>
  <div>
    <b-card>
      <b-row
        align-h="center"
      >
        <!-- Text Input-->
        <b-col
          cols="4"
        >
          <b-form-input
            size="sm"
            v-model="filterUser.text"
            placeholder="会員ID/ニックネーム"
          />
        </b-col>
        <!-- Gender Input -->
        <b-col
          cols="2"
        >
          <b-form-select
            size="sm"
            v-model="filterUser.gender"
            :options="$root.lazyOption.genderOption"
          >
            <template #first>
              <b-form-select-option
                :value="null"
              >
                性別
              </b-form-select-option>
            </template>
          </b-form-select>
        </b-col>
        <!-- Platform Input -->
        <b-col
          cols="2"
        >
          <b-form-select
            size="sm"
            v-model="filterUser.platform"
            :options="$root.lazyOption.mobilePlatformOption"
          >
            <template #first>
              <b-form-select-option
                :value="null"
              >
                プラットホーム
              </b-form-select-option>
            </template>
          </b-form-select>
        </b-col>
        <!-- Status Input -->
        <b-col
          cols="2"
        >
          <b-form-select
            size="sm"
            v-model="filterUser.status"
            :options="$root.lazyOption.userStatusOption"
          >
            <template #first>
              <b-form-select-option
                :value="null"
              >
                ステータス
              </b-form-select-option>
            </template>
          </b-form-select>
        </b-col>
        <!-- Search Button-->
        <b-col
          cols="1"
          class="ml-3"
        >
          <b-button
            size="sm"
            variant="outline-primary"
            @click="searchUser"
          >
            <b-icon icon="search" />
          </b-button>
        </b-col>
      </b-row>
      <!-- 生年月日 -->
      <b-row
        class="mt-3"
        align-h="center"
      >
        <b-col
          cols="1"
          style="text-align: center"
          class="custom-col-search"
        >
          <p>生年月日</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="fromBirthday"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="fromBirthday"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
        <b-col
          style="text-align: center"
          class="custom-col-search"
          cols="1"
        >
          <p>~</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="toBirthday"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="toBirthday"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
      </b-row>
      <!-- 登録日 -->
      <b-row
        class="mt-3"
        align-h="center"
      >
        <b-col
          cols="1"
          style="text-align: center"
          class="custom-col-search"
        >
          <p>登録日</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="fromRegisterDay"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="fromRegisterDay"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
        <b-col
          style="text-align: center"
          class="custom-col-search"
          cols="1"
        >
          <p>~</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="toRegisterDay"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="toRegisterDay"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
      </b-row>
      <!-- 退会日 -->
      <b-row
        class="mt-3"
        align-h="center"
      >
        <b-col
          cols="1"
          style="text-align: center"
          class="custom-col-search"
        >
          <p>撤退日</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="fromWithdrawDay"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="fromWithdrawDay"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
        <b-col
          style="text-align: center"
          class="custom-col-search"
          cols="1"
        >
          <p>~</p>
        </b-col>
        <b-col
          cols="2.5"
          class="custom-col-search"
        >
          <b-input-group>
            <b-datepicker
              v-model="toWithdrawDay"
              size="sm"
              left
              button-only
              locale="ja"
            />
            <b-input-group-append>
              <b-form-input
                v-model="toWithdrawDay"
                size="sm"
                type="text"
                placeholder="登録日を入力してください"
                style="width: 180px"
              />
            </b-input-group-append>
          </b-input-group>
        </b-col>
      </b-row>
      <!-- Sort by gender -->
      <b-row
        class="mt-3"
        align-h="between"
      >
        <b-col
          cols="6"
          style="text-align: left"
        >
          <b-button-group>
            <b-button
              size="sm"
              class="btn btn-outline-primary"
              variant="outline-secondary"
              style="width: 120px; text-align: center"
              @click="sortByMale"
            >
              男性
            </b-button>
            <b-button
              size="sm"
              class="btn btn-outline-primary"
              variant="outline-secondary"
              style="width: 120px; text-align: center"
              @click="sortByFeMale"
            >
              女性
            </b-button>
          </b-button-group>
        </b-col>
        <b-col
          cols="6"
          style="text-align: right"
        >
          <b-button
            size="sm"
            variant="outline-success"
            @click="downloadCSV"
          >
            <i class="fas fa-download" />
            ＣＳＶダウンロード
          </b-button>
        </b-col>
      </b-row>
    </b-card>
    <!-- Information Table -->
    <b-card>
      <b-table
        striped
        hover
        :fields="fields"
        :items="filterUserItems"
        :per-page="perPage"
        :current-page="currentPage"
        :sort-by.sync="sortBy"
        :sort-desc.sync="sortDesc"
        style="text-align: center"
        @row-clicked="goToDetail"
      >
        <template v-slot:cell(index)="data">
          {{ data.index + 1 + (currentPage-1) * perPage }}
        </template>
        <template v-slot:cell(actions)="row">
          <b-button
            size="sm"
            variant="outline-primary"
            class="mr-1 btn"
            @click="goToDetail(row.item.id)"
          >
            =
          </b-button>
        </template>
      </b-table>
      <div class="card-footer">
        <div>
          <v-pagination
            v-model="currentPage"
            :total="total"
            :per-page="perPage"
          />
        </div>
      </div>
    </b-card>
  </div>
</template>
<script>
import moment from 'moment'
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import VPagination from '@/components/common/molecules/VPagination'

export default {
  name: 'UserList',
  components: { VPagination },
  props: {
    keyword: {
      type: String,
      default: null
    },
    user: {
      type: [Object, Array],
      default: () => {
      }
    }
  },
  data () {
    return {
      users: this.user || [],
      newKeyword: this.keyword,
      perPage: 20,
      currentPage: 1,
      createdDate: null,
      sortBy: 'genderName',
      sortDesc: false,
      fromBirthday: null,
      toBirthday: null,
      fromRegisterDay: null,
      toRegisterDay: null,
      fromWithdrawDay: null,
      toWithdrawDay: null,
      filterUser: {
        text: null,
        datetime: null,
        gender: null,
        status: null,
        platform: null
      },
      appliedFilterUser: {
        text: null,
        datetime: null,
        gender: null,
        status: null,
        platform: null
      },
      fields: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'memberId',
          label: this.$t('models.user.field.member_id')
        },
        {
          key: 'statusName',
          label: this.$t('models.user.field.status')
        },
        {
          key: 'genderName',
          label: this.$t('models.user.field.gender')
        },
        {
          key: 'nickname',
          label: this.$t('models.user.field.nickname')
        },
        {
          key: 'birthday',
          label: this.$t('models.user.field.birthday')
        },
        {
          key: 'registerAt',
          label: this.$t('models.user.field.register_day')
        },
        {
          key: 'withdrawAt',
          label: this.$t('models.user.field.withdraw_day')
        },
        {
          key: 'actions',
          label: this.$t('common.action')
        }
      ]
    }
  },
  computed: {
    /**
       * filter User Items
       */
    filterUserItems () {
      let resultUserItems = [...this.user]
      for (let i = 0; i < resultUserItems.length; i++) {
        // convert created at
        if (resultUserItems[i].birthday) {
          resultUserItems[i].birthdayConvert = this.dateConvertToNumber(resultUserItems[i].birthday)
        }
        // convert register at
        if (resultUserItems[i].registerAt) {
          resultUserItems[i].registerAtConvert = this.dateConvertToNumber(resultUserItems[i].registerAt)
        }
        // convert withdraw at
        if (resultUserItems[i].withdrawAt) {
          resultUserItems[i].withdrawAtConvert = this.dateConvertToNumber(resultUserItems[i].withdrawAt)
        }
      }
      // filter birthday
      resultUserItems = this.filterBirthday(resultUserItems, this.appliedFilterUser.fromBirthday, this.appliedFilterUser.toBirthday)
      // filter register day
      resultUserItems = this.filterRegisterDay(resultUserItems, this.appliedFilterUser.fromRegisterDay, this.appliedFilterUser.toRegisterDay)
      // filter withdraw day
      resultUserItems = this.filterWithdrawDay(resultUserItems, this.appliedFilterUser.fromWithdrawDay, this.appliedFilterUser.toWithdrawDay)

      if (this.appliedFilterUser.text) {
        let text = this.appliedFilterUser.text
        resultUserItems = resultUserItems.filter(item => (item.memberId == text) || ((item.nickname) ? item.nickname.includes(text) == true : false))
      }
      if (this.appliedFilterUser.datetime) {
        resultUserItems = resultUserItems.filter(item => item.createdAtConvert == this.appliedFilterUser.datetime)
      }
      if (this.appliedFilterUser.gender) {
        resultUserItems = resultUserItems.filter(item => item.gender == this.appliedFilterUser.gender)
      }
      if (this.appliedFilterUser.status) {
        resultUserItems = resultUserItems.filter(item => item.status == this.appliedFilterUser.status)
      }
      if (this.appliedFilterUser.platform) {
        resultUserItems = resultUserItems.filter(item => item.mobilePlatform == this.appliedFilterUser.platform)
      }
      return resultUserItems
    },
    /**
       * total
       */
    total () {
      return this.filterUserItems.length
    }
  },
  methods: {
    downloadCSV () {
      location.href = this.route('admin.user.download_csv')
    },
    goToDetail (id) {
      let index = 0
      location.href = this.route('admin.user.show', [id, index])
    },
    goToEdit (id) {
      location.href = this.route('admin.user.edit', id)
    },
    /**
       * search User
       */
    searchUser () {
      // convert from birthday
      this.filterUser.fromBirthday = this.dateConvertToNumber(this.fromBirthday)
      // convert to birthday
      this.filterUser.toBirthday = this.dateConvertToNumber(this.toBirthday)

      // convert from register day
      this.filterUser.fromRegisterDay = this.dateConvertToNumber(this.fromRegisterDay)
      // convert to register day
      this.filterUser.toRegisterDay = this.dateConvertToNumber(this.toRegisterDay)

      // convert from withdraw day
      this.filterUser.fromWithdrawDay = this.dateConvertToNumber(this.fromWithdrawDay)
      // convert to withdraw day
      this.filterUser.toRegisterDay = this.dateConvertToNumber(this.toRegisterDay)

      this.appliedFilterUser = { ...this.filterUser }
    },
    /**
       * delete User
       */
    deleteUser (userId) {
      this.$eventHub.$emit('open-delete-modal', async () => {
        try {
          await axios.delete(this.route('admin.user.delete', userId))
          location.href = this.route('admin.user.index')
        } catch (e) {
          this.$nextTick(() => {
            throw new Error(extractLaravelError(e))
          })
        }
      })
    },
    /**
       * sort By Male
       */
    sortByMale () {
      this.sortDesc = true
    },
    /**
       * sort By FeMale
       */
    sortByFeMale () {
      this.sortDesc = false
    },
    /**
       * date Convert To Number
       * @param datetime
       * @returns {number|null}
       */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')
        if (date > 0 && date < 10) {
          date = '0' + date
        }
        if (month > 0 && month < 10) {
          month = '0' + month
        }
        datetime = year + '' + month + '' + date
        return parseInt(datetime)
      }
      return null
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterBirthday (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert >= fromDay && item.birthdayConvert <= toDay)
      }
      return resultItem
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterRegisterDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert >= fromDay && item.registerAtConvert <= toDay)
      }
      return resultItem
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterWithdrawDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert >= fromDay && item.withdrawAtConvert <= toDay)
      }
      return resultItem
    }
  }
}
</script>
