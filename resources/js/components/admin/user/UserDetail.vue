<template>
  <b-tabs v-model="index">
    <b-tab :title="$t('models.user.detail.title.profile')">
      <detail-user-profile
        :user="user"
      />
    </b-tab>
    <!-- UserFriend Tab -->
    <b-tab :title="$t('models.user.detail.title.user-friend')">
      <detail-user-friend
        :friend="friends"
        :user="user"
      />
    </b-tab>
    <!-- User Friend Message Tab -->
    <b-tab :title="$t('models.user.detail.title.user-friend-message')">
      <detail-user-friend-message
        v-if="userMessage"
        :friend-message="userMessage"
      />
    </b-tab>
    <!-- UserTicket Tab -->
    <b-tab
      v-if="(user.gender === 1)"
      :title="$t('models.user.detail.title.user-ticket')"
    >
      <detail-user-ticket
        :ticket="tickets"
        :nickname="user.nickname"
      />
    </b-tab>
    <!-- User Buy Point Tab  -->
    <b-tab
      v-if="(user.gender === 1)"
      :title="$t('models.user.detail.title.user-buy-point')"
    >
      <detail-user-buy-point
        :buy-point="buyPoints"
        :nickname="user.nickname"
      />
    </b-tab>
    <!-- User Present Point Tab -->
    <b-tab
      :title=" (user.gender === 1) ? $t('models.user.detail.title.user-present-point') : $t('models.user.detail.title.user-receive-point')"
    >
      <!-- Present Point -->
      <detail-user-present-point
        v-if="(user.gender === 1)"
        :present-point="presentPoints"
        :user="user"
      />
      <!-- Receive Point -->
      <detail-user-receive-point
        v-else
        :receive-point="receivePoints"
        :user="user"
      />
    </b-tab>
    <!-- Block User Tab -->
    <b-tab
      :title="$t('models.user.detail.title.user-block')"
    >
      <detail-user-block
        :user-block="userBlocks"
        :nickname="user.nickname"
      />
    </b-tab>
    <!-- User Report Tab -->
    <b-tab
      :title="$t('models.user.detail.title.user-report')"
    >
      <detail-user-report
        :user-report="userReports"
        :user="user"
      />
    </b-tab>
    <!-- User Contact Tab -->
    <b-tab :title="$t('models.user.detail.title.user-contact')">
      <detail-user-contact
        :user-contact="userContacts"
        :user="user"
      />
    </b-tab>
  </b-tabs>
</template>
<script>
import axios from 'axios'
import moment from 'moment'

export default {
  name: 'UserDetail',
  components: { },
  props: {
    user: {
      type: [Object, Array],
      default: () => {}
    },
    userMessage: {
      type: Object,
      default: () => {}
    },
    tabIndex: {
      type: [Number, String],
      default: 0
    }
  },
  data () {
    return {
      index: this.tabIndex,
      friendDate: null,
      presentPointDate: null,
      friends: [],
      userMessages: [],
      tickets: [],
      friendMessages: [],
      buyPoints: [],
      presentPoints: [],
      receivePoints: [],
      userBlocks: [],
      userReports: [],
      userContacts: [],
      tabFriend: false,
      tabMessage: false,
      tabFriendMessage: false,
      tabBlock: false,
      tabTicket: false,
      tabBuyPoint: false,
      tabPresentPoint: false,
      tabUserBlock: false,
      tabUserReport: false,
      tabUserContact: false,
      mainProps: { width: 200, height: 200, class: 'mb-2' }
    }
  },
  computed: {

  },
  watch: {
    async tabIndex (idx) {
      /** Tab friend */
      if (idx === 1) {
        if (this.tabFriend === false) {
          const { data } = await axios.get(this.route('admin.user.friend', this.user.id))
          this.friends = data.result_detail
          this.tabFriend = true
        }
      }
      /** Tab User friend Message */
      if (idx === 2) {
        if (this.tabMessage === false) {
          const { data } = await axios.get(this.route('admin.user.message', this.user.id))
          this.messages = data.result_detail
          this.tabMessage = true
        }
      }
      /** Tab ticket */
      if (idx === 3) {
        if (this.tabTicket === false) {
          const { data } = await axios.get(this.route('admin.user.get-buy-ticket', this.user.id))
          this.tickets = data.result_detail
          this.tabTicket = true
        }
      }
      /** Tab buy point */
      if (idx === 4) {
        if (this.tabBuyPoint === false) {
          const { data } = await axios.get(this.route('admin.user.get-buy-point', this.user.id))
          this.buyPoints = data.result_detail
          this.tabBuyPoint = true
        }
      }
      /** Tab present point */
      if (idx === 5) {
        if (this.tabPresentPoint === false) {
          if (this.user.gender === 1) {
            const { data } = await axios.get(this.route('admin.user.get-present-point', this.user.id))
            this.presentPoints = data.result_detail
            this.tabPresentPoint = true
          } else {
            const { data } = await axios.get(this.route('admin.user.get-receive-point', this.user.id))
            this.receivePoints = data.result_detail
            this.tabPresentPoint = true
          }
        }
      }
      /** Tab User Block */
      if (idx === 6) {
        if (this.tabUserBlock === false) {
          const { data } = await axios.get(this.route('admin.user.get-user-block', this.user.id))
          this.userBlocks = data.result_detail
          this.tabUserBlock = true
        }
      }
      /** Tab report */
      if (idx === 7 || idx === 5) {
        if (this.tabUserReport === false) {
          const { data } = await axios.get(this.route('admin.user.get-user-report', this.user.id))
          this.userReports = data.result_detail
          this.tabUserReport = true
        }
      }
      /** Tab contact */
      if (idx === 8 || idx === 6) {
        if (this.tabUserContact === false) {
          const { data } = await axios.get(this.route('admin.user.get-user-contact', this.user.id))
          this.userContacts = data.result_detail
          this.tabUserContact = true
        }
      }
    }
  },
  async created () {
    /** Load All */
    if (this.tabIndex === 0) {
      this.LoadAll()
    }
    /** Tab friend */
    if (this.tabIndex === 1) {
      if (this.tabFriend === false) {
        const { data } = await axios.get(this.route('admin.user.friend', this.user.id))
        this.friends = data.result_detail
        this.tabFriend = true
        this.LoadAll()
      }
    }
    /** Tab Message */
    if (this.tabIndex === 2) {
      this.LoadAll()
    }
    /** Tab contact */
    if (this.tabIndex === 8 || this.tabIndex === 6) {
      if (this.tabUserContact === false) {
        const { data } = await axios.get(this.route('admin.user.get-user-contact', this.user.id))
        this.userContacts = data.result_detail
        this.tabUserContact = true
        this.LoadAll()
      }
    }
    /** Tab report */
    if (this.tabIndex === 7 || this.tabIndex === 5) {
      if (this.tabUserReport === false) {
        const { data } = await axios.get(this.route('admin.user.get-user-report', this.user.id))
        this.userReports = data.result_detail
        this.tabUserReport = true
        this.LoadAll()
      }
    }
  },
  methods: {
    /**
       * LoadAll
       */
    async LoadAll () {
      // Load Friend
      if (this.tabFriend === false) {
        const { data } = await axios.get(this.route('admin.user.friend', this.user.id))
        this.friends = data.result_detail
        this.tabFriend = true
      }
      // Load Ticket
      if (this.tabTicket === false) {
        const { data } = await axios.get(this.route('admin.user.get-buy-ticket', this.user.id))
        this.tickets = data.result_detail
        this.tabTicket = true
      }
      // Load Point
      if (this.tabBuyPoint === false) {
        const { data } = await axios.get(this.route('admin.user.get-buy-point', this.user.id))
        this.buyPoints = data.result_detail
        this.tabBuyPoint = true
      }
      // Load Present and Receive Point
      if (this.tabPresentPoint === false) {
        if (this.user.gender === 1) {
          const { data } = await axios.get(this.route('admin.user.get-present-point', this.user.id))
          this.presentPoints = data.result_detail
          this.tabPresentPoint = true
        } else {
          const { data } = await axios.get(this.route('admin.user.get-receive-point', this.user.id))
          this.receivePoints = data.result_detail
          this.tabPresentPoint = true
        }
      }
      // Load User Block
      if (this.tabUserBlock === false) {
        const { data } = await axios.get(this.route('admin.user.get-user-block', this.user.id))
        this.userBlocks = data.result_detail
        this.tabUserBlock = true
      }
      // Load report
      if (this.tabUserReport === false) {
        const { data } = await axios.get(this.route('admin.user.get-user-report', this.user.id))
        this.userReports = data.result_detail
        this.tabUserReport = true
      }
      // Load contact
      if (this.tabUserContact === false) {
        const { data } = await axios.get(this.route('admin.user.get-user-contact', this.user.id))
        this.userContacts = data.result_detail
        this.tabUserContact = true
      }
    },
    /**
             * sortNewDate
             */
    sortNewDate () {
      this.userContacts = this.userContacts.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
    },
    /**
             * sortOldDate
             */
    sortOldDate () {
      this.userContacts = this.userContacts.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
    },
    /**
             * date Convert To Number
             * @param datetime
             * @returns {number}
             */
    dateConvertToNumber (datetime) {
      let d = new Date(datetime)
      let year = moment(d).format('YYY')
      let month = moment(d).format('MM')
      let date = moment(d).format('DD')
      if (date > 0 && date < 10) {
        date = '0' + date
      }
      if (month > 0 && month < 10) {
        month = '0' + month
      }
      datetime = year + '' + month + '' + date
      return parseInt(datetime)
    }
  }
}
</script>
<style scoped>
  tdFriend {
    width: 350px;
  }
</style>
