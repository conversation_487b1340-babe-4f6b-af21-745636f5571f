<template>
  <b-card
    :header="$t('models.user.detail.user_contact.user-contact',{nickname: user.nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-row class="mb-5 mt-2">
      <b-col cols="5">
        <b-form-input
          v-model="filterContact.text"
          size="sm"
          placeholder="フリーキーワード"
        />
      </b-col>
      <!--Select Contact Div Option -->
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterContact.contactDiv"
          size="sm"
          :options="$root.lazyOption.contactDivOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              お問合せ
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <!--Select Contact State -->
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterContact.contactStatus"
          size="sm"
          :options="$root.lazyOption.contactState"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              ステータス
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="1"
        style="text-align: right"
      >
        <b-button
          size="sm"
          variant="outline-primary"
          @click="searchContact"
        >
          <b-icon
            icon="search"
          />
        </b-button>
      </b-col>
    </b-row>
    <b-row
      class="mb-2 mt-2"
      align-h="between"
    >
      <!-- Sort By Date -->
      <b-col cols="6">
        <b-button-group>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px"
            @click="sortNewDate"
          >
            新しい日付
          </b-button>
          <b-button
            size="sm"
            style="width: 120px"
            variant="outline-secondary"
            @click="sortOldDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <b-table-simple
      style="text-align: center"
    >
      <b-thead>
        <b-tr>
          <b-th>{{ $t('models.user.field.index') }}</b-th>
          <b-th>{{ $t('models.user.detail.user_contact.id') }}</b-th>
          <b-th>{{ $t('models.user.detail.user_contact.created_at') }}</b-th>
          <b-th>{{ $t('models.user.detail.user_contact.state') }}</b-th>
          <b-th>{{ $t('models.user.detail.user_contact.warning') }}</b-th>
          <b-th>{{ $t('models.user.detail.user_contact.contact-div') }}</b-th>
          <b-th><i class="far fa-envelope fa-2x" /></b-th>
          <b-th><i class="fas fa-equals fa-1x" /></b-th>
        </b-tr>
      </b-thead>
      <b-tbody
        v-for="(row, index) in items"
        :key="index"
      >
        <b-tr
          v-if="row.hasFeedback"
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            {{ index + 1 + (currentPageUserContact - 1) * perPageUserContact }}
          </b-td>
          <b-td class="border">
            {{ row.contactId }}
          </b-td>
          <b-td class="border">
            {{ row.createdAt }}
          </b-td>
          <b-td
            class="border"
            rowspan="2"
            style="vertical-align: middle; text-align: center"
          >
            {{ row.contactStatusName }}
          </b-td>
          <b-td
            class="border"
            rowspan="2"
            style="vertical-align: middle; text-align: center"
          >
            {{ row.contactWarningName }}
          </b-td>
          <b-td
            class="border"
            rowspan="2"
            style="vertical-align: middle; text-align: center"
          >
            {{ row.contactDivName }}
          </b-td>
          <b-td
            class="border"
            rowspan="2"
          />
          <b-td
            class="border"
            style="vertical-align: middle; text-align: center"
          >
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToUserContactDetail(row.contactId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
        <b-tr
          v-if="row.hasFeedback"
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td class="border">
            {{ row.feedbackId }}
          </b-td>
          <b-td class="border">
            {{ row.feedbackAt }}
          </b-td>
          <b-td
            class="border"
            style="vertical-align: middle; text-align: center"
          >
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToFeedbackDetail(row.feedbackId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
        <b-tr
          v-else
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td
            class="border"
          >
            {{ index + 1 + (currentPageUserContact - 1) * perPageUserContact }}
          </b-td>
          <b-td class="border">
            {{ row.contactId }}
          </b-td>
          <b-td class="border">
            {{ row.createdAt }}
          </b-td>
          <b-td
            class="border"
          >
            <b-form-select
              v-model="row.contactStatus"
              name="contactStatus"
              :style="{color : row.contactStatus === 1 ? 'red' : 'black'}"
              style="width: 120px"
              size="sm"
              :options="options"
              @change="updateContactStatus(row)"
            />
          </b-td>
          <b-td
            class="border"
          >
            {{ row.contactWarningName }}
          </b-td>
          <b-td
            class="border"
          >
            {{ row.contactDivName }}
          </b-td>
          <b-td
            class="border"
          >
            <b-button
              size="sm"
              class="mr-1 btn"
              variant="link"
              @click="goToFeedback(row.contactId)"
            >
              <i class="far fa-envelope fa-2x" />
            </b-button>
          </b-td>
          <b-td
            class="border"
            style="vertical-align: middle; text-align: center"
          >
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToUserContactDetail(row.contactId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <div class="card-body">
      <v-pagination
        v-model="currentPageUserContact"
        :total="totalUserContact"
        :per-page="perPageUserContact"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import VPagination from '@/components/common/molecules/VPagination'
import TabUserIndex from '@/constant/tab_user_index'

export default {
  name: 'DetailUserContact',
  components: { VPagination },
  props: {
    userContact: {
      type: Array,
      default: () => {}
    },
    user: {
      type: Object,
      default: () => {
      }
    }
  },
  data () {
    return {
      currentPageUserContact: 1,
      perPageUserContact: 5,
      /** Filter */
      filterContact: {
        text: null,
        contactStatus: null,
        contactDiv: null
      },
      appliedFilterContact: {
        text: null,
        contactStatus: null,
        contactDiv: null
      },
      fieldUserContacts: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'id',
          label: this.$t('models.user.detail.user_contact.id')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user.detail.user_contact.created_at')
        },
        {
          key: 'contactStatusName',
          label: this.$t('models.user.detail.user_contact.state')
        },
        {
          key: 'warning',
          label: this.$t('models.user.detail.user_contact.warning')
        },
        {
          key: 'contactDivName',
          label: this.$t('models.user.detail.user_contact.contact-div')
        },
        {
          key: 'msg',
          label: 'msg'
        },
        {
          key: 'actions',
          tdClass: 'tdFriend'
        }
      ],
      options: [
        { value: 1, text: '未対応' },
        { value: 3, text: '対応不可' }
      ]
    }
  },
  computed: {
    /**
             * filter Contact Items
             */
    filterContactItems () {
      let resultContactItems = [...this.userContact]

      if (this.appliedFilterContact.contactDiv) {
        resultContactItems = resultContactItems.filter(item => item.contactDiv == this.appliedFilterContact.contactDiv)
      }
      if (this.appliedFilterContact.contactStatus) {
        resultContactItems = resultContactItems.filter(item => item.contactStatus == this.appliedFilterContact.contactStatus)
      }
      if (this.appliedFilterContact.text) {
        resultContactItems = resultContactItems.filter(item => item.content.includes(this.appliedFilterContact.text) || item.contactId == this.appliedFilterContact.text || item.feedbackId == this.appliedFilterContact.text)
      }

      return resultContactItems
    },
    /**
       * items
       */
    items () {
      let results = []
      let resultContactItems = this.filterContactItems
      let k = 0
      if (this.currentPageUserContact) {
        let length = resultContactItems.length
        if (length <= this.perPageUserContact) {
          results = resultContactItems
        } else {
          let currentPage = this.currentPageUserContact
          let from = (currentPage - 1) * this.perPageUserContact
          let target = currentPage * this.perPageUserContact
          if (target >= length) {
            for (let i = from; i < length; i++) {
              results[k] = resultContactItems[i]
              k = k + 1
            }
          } else {
            for (let i = from; i < target; i++) {
              results[k] = resultContactItems[i]
              k = k + 1
            }
          }
        }
      }
      return results
    },
    /** Tab User Contact */
    totalUserContact: {
      get: function () {
        return this.filterContactItems.length
      }
    }
  },
  methods: {
    isEven (value) {
      if (value % 2 === 0) { return true } else { return false }
    },
    /**
       * downloadCSV
       */
    downloadCSV () {
      location.href = this.route('admin.user_contact.download_csv')
    },
    /**
       * goTo Feedback
       */
    goToFeedback (contactId) {
      location.href = this.route('admin.feedback_contact.create') + '?user_id=' + this.user.id + '&gender=' + this.user.gender + '&contact_id=' + contactId
    },
    /**
       * goToUserContactDetail
       */
    goToUserContactDetail (contactId) {
      location.href = this.route('admin.detail_user_contact.show') + '?user_id=' + this.user.id + '&gender=' + this.user.gender + '&contact_id=' + contactId
    },
    /**
       * goToFeedbackDetail
       */
    goToFeedbackDetail (feedbackId) {
      location.href = this.route('admin.feedback_contact.show', [feedbackId]) + '?user_id=' + this.user.id + '&gender=' + this.user.gender
    },
    /**
     * search
     */
    searchContact () {
      this.appliedFilterContact = { ...this.filterContact }
    },
    /**
       * sortNewDate
       */
    sortNewDate () {
      this.userContact = this.userContact.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
    },
    /**
       * sortOldDate
       */
    sortOldDate () {
      this.userContact = this.userContact.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
    },
    /**
       *
       * @param item
       * @returns {Promise<void>}
       */
    async updateContactStatus (item) {
      try {
        await axios.post(this.route('admin.user_contact.update', item.contactId), { 'contact_status': item.contactStatus })
        if (this.user.gender === 1) {
          location.href = this.route('admin.user.show', [this.user.id, TabUserIndex.MALE.TAB_CONTACT])
        } else {
          location.href = this.route('admin.user.show', [this.user.id, TabUserIndex.FEMALE.TAB_CONTACT])
        }
      } catch (e) {
        this.$nextTick(() => {
          throw new Error(extractLaravelError(e))
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
