<template>
  <div>
    <b-row
      v-if="friendMessage"
      class="mt-3 mb-2"
      align-h="between"
    >
      <b-col
        cols="6"
        style="text-align: left"
      >
        <b-button-group>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByNewDate"
          >
            新しい日付
          </b-button>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByOldDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <message-detail
      v-if="friendMessage"
      :message="sortMessage"
      :user="user"
      :partner="partner"
    />
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'DetailUserFriendMessage',
  components: { },
  props: {
    friendMessage: {
      type: [Object, Array],
      default: () => {}
    }
  },
  data () {
    return {
      sortNewDate: false,
      sortOldDate: false,
      user: this.friendMessage.user,
      partner: this.friendMessage.partner
    }
  },
  computed: {
    sortMessage () {
      let message = this.friendMessage.data
      for (let i = 0; i < message.length; i++) {
        if (message[i].createdAt) {
          message[i].createdAtConvert = this.dateConvertToNumber(message[i].createdAt)
        }
      }
      // sort new date
      if (this.sortNewDate) {
        message.sort(function (a, b) {
          return a.createdAtConvert - b.createdAtConvert
        })
      }
      // sort old date
      if (this.sortOldDate) {
        message.sort(function (a, b) {
          return b.createdAtConvert - a.createdAtConvert
        })
      }
      return message
    }
  },
  mounted () {
    this.$forceUpdate()
  },
  methods: {
    /**
       * sort By New Date
       */
    sortByNewDate () {
      this.sortNewDate = true
      this.sortOldDate = false
    },
    /**
       * sort By Old Date
       */
    sortByOldDate () {
      this.sortOldDate = true
      this.sortNewDate = false
    },
    /**
       *
       * @param datetime
       * @returns {null|number}
       */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')
        let hour = moment(d).format('HH')
        let minute = moment(d).minute()
        if (date > 0 && date < 10) {
          date = '0' + date
        }
        if (month > 0 && month < 10) {
          month = '0' + month
        }
        datetime = year + '' + month + '' + date + '' + hour + '' + minute
        return parseInt(datetime)
      }
      return null
    }
  }
}
</script>

<style scoped>

</style>
