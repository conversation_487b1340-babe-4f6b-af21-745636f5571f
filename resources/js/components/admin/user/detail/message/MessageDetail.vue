<template>
  <div>
    <b-card
      border-variant="primary"
      header-bg-variant="primary"
      :header="$t('models.user.detail.friend-message.user-friend-message' ,{user: user.nickname ,partner: partner.nickname})"
      header-text-variant="black"
      align="left"
      class="mt-3"
      fluid
    >
      <b-row>
        <b-col cols="12">
          <div
            v-chat-scroll
            class="chat-box"
          >
            <b-list-group class="list-box">
              <b-list-group-item
                v-for="chat in chats"
                :key="chat.key"
                class="chat-item"
              >
                <div
                  v-if="chat.senderId === partner.id"
                  class="chat-message text-right"
                >
                  <div class="right-bubble">
                    <b-row
                      align-h="end"
                      class="justify-content-md-right"
                    >
                      <b-col cols="9">
                        <div class="bubble-right mt-1">
                          <p
                            class="max-lines px-3 py-1"
                            style="text-align: left"
                          >
                            {{ chat.content }}
                          </p>
                        </div>
                        <div>
                          <span class="msg-date px-3" style="text-align: right">{{ chat.createdAt }}</span>
                        </div>
                      </b-col>
                      <b-col
                        cols="1"
                      >
                        <b-img
                          thumbnail
                          fluid
                          rounded="circle"
                          v-bind="mainProps"
                          :src="getPartnerAvatar"
                          alt=""
                          @error="onImgPartnerError"
                        />
                        <p
                          class="msg-name nickname text-center align-middle"
                          style="text-align: center"
                        >
                          {{ chat.senderName
                          }}
                        </p>
                      </b-col>
                    </b-row>
                  </div>
                </div>
                <div
                  v-if="chat.senderId === user.id"
                  class="chat-message text-left"
                >
                  <div class="left-bubble">
                    <b-row
                      align-h="start"
                    >
                      <b-col
                        cols="1"
                      >
                        <b-img
                          center
                          thumbnail
                          fluid
                          rounded="circle"
                          v-bind="mainProps"
                          :src="getUserAvatar"
                          alt=""
                          @error="onImgUserError"
                        />
                        <p
                          class="msg-name nickname text-center align-middle"
                          style="text-align: center"
                        >
                          {{ chat.senderName }}
                        </p>
                      </b-col>
                      <b-col
                        class="ml-auto"
                      >
                        <div class="bubble-left mt-1">
                          <p class="text-wrap px-3 py-1 max-lines">
                            {{ chat.content }}
                          </p>
                        </div>
                        <div>
                          <span class="msg-date px-3 ">{{ chat.createdAt }}</span>
                        </div>
                      </b-col>
                    </b-row>
                  </div>
                </div>
              </b-list-group-item>
            </b-list-group>
          </div>
        </b-col>
      </b-row>
      <div class="card-footer text-center">
        <b-button
          variant="outline-secondary"
          class="btn"
          @click="goToUserFriend(user.id)"
        >
          {{ $t('common.btn.back') }}
        </b-button>
      </div>
    </b-card>
  </div>
</template>

<script>
export default {
  name: 'MessageDetail',
  props: {
    message: {
      type: [Object, Array],
      default: () => {

      }
    },
    user: {
      type: [String, Object],
      default: ''
    },
    partner: {
      type: [String, Object],
      default: ''
    }
  },
  data () {
    return {
      previewProps: { width: 150, height: 150, class: 'm1' },
      mainProps: { width: 75, height: 75, class: 'm1' },
      mainPropCall: { width: 30, height: 30, class: 'm1' },
      chats: this.message || [],
      imgPartnerError: false,
      imgUserError: false,
      url: '/img/avatar.jpeg'
    }
  },
  computed: {
    /**
       * getPartnerAvatar
       * @returns {string}
       */
    getPartnerAvatar () {
      if (this.partner.avatar_url !== null) {
        return (this.imgPartnerError) ? this.url : this.partner.avatar_url
      }
      return this.url
    },
    /**
       * getUserAvatar
       * @returns {string}
       */
    getUserAvatar () {
      if (this.user.avatar_url !== null) {
        return (this.imgUserError) ? this.url : this.user.avatar_url
      }
      return this.url
    }

  },
  created () {
    console.log('chats: ', this.chats)
  },
  methods: {
    onImgPartnerError () {
      this.imgPartnerError = true
    },
    onImgUserError () {
      this.imgUserError = true
    },
    onSubmit () {
      return null
    },
    isURL (str) {
      let prefix = str.slice(0, 4)
      return prefix === 'http'
    },
    goToUserFriend (id) {
      location.href = this.route('admin.user.show', [id, 1])
    }
  }
}
</script>

<style scoped>

  .chat-box {
    height: 600px;
    width: 100%;
    overflow: scroll;
  }

  .chat-item {
    border: none;
  }

  .chat-status {
    min-height: 49px;
  }

  .chat-status .chat-date {
    display: block;
    font-size: 10px;
    font-style: italic;
    /*color: #999999;*/
    height: 15px;
    left: 10%;
    right: 10%;
  }

  .chat-status .chat-content-center {
    padding: 5px 10px;
    /*background-color: #e1e1f7;*/
    border-radius: 6px;
    font-size: 12px;
    color: #555;
    height: 34px;
    left: 10%;
    right: 10px;
  }

  .chat-message {
    width: 80%;
    min-height: 40px;
    border: none;
    opacity: 100%;
  }

  .chat-message .right-bubble {
    position: relative;
    border-top-left-radius: 0.4em;
    border-bottom-right-radius: 0.4em;
    border-bottom-left-radius: 0.4em;
    /*padding: 5px 10px 10px;*/
    left: 25%;
  }

  .chat-message .bubble-right {
    background: #99ff99;
    border-radius: 16px;
    vertical-align: top;
    display: inline-flex;
  }

  .chat-message .right-bubble span.msg-name {
    font-size: 12px;
    font-weight: bold;
    /*color: green;*/
    display: block;
  }

  .chat-message .right-bubble span.msg-date {
    font-size: 10px;
    display: block;
  }

  .chat-message .left-bubble {
    position: relative;
    border-top-right-radius: .4em;
    border-bottom-left-radius: .4em;
    border-bottom-right-radius: .4em;
    /*padding: 5px 10px 10px;*/
  }

  .chat-message .bubble-left {
    background: #99ccff;
    border-radius: 16px;
    vertical-align: top;
    display: inline-flex;
  }

  .chat-message .left-bubble span.msg-name {
    font-size: 12px;
    font-weight: bold;
    /*color: blue;*/
    display: block;
  }

  .chat-message .left-bubble span.msg-date {
    font-size: 10px;
    display: block;
  }

  footer.sticky-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    background-image: linear-gradient(141deg, #0fb8ad 0%, #1fc8db 51%, #2cb5e8 75%);
    border-top: solid 1px #efefef;
  }

  div.sticky-header {
    height: 25px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    background-image: linear-gradient(141deg, #0fb8ad 0%, #1fc8db 51%, #2cb5e8 75%);
    border-top: solid 1px #efefef;
  }

  .max-lines {
    display: block;/* or inline-block */
    text-overflow: ellipsis;
    word-wrap: break-word;
    overflow: hidden;
    max-width: 790px;
    line-height: 1.8em;
  }
  .nickname {
    text-align: center;
    text-overflow: ellipsis;
    max-width: 100px;
    max-height: 3.2em;
    overflow:hidden;
  }

</style>
