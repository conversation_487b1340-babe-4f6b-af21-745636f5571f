<template>
  <!-- gender = male -->
  <b-card
    :header="$t('models.user.detail.present_point.user-present-point',{nickname: user.nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-row
      class="mb-5 mt-2"
      align-h="start"
    >
      <!-- input -->
      <b-col cols="4">
        <b-form-input
          v-model="filterPresentPoint.text"
          size="sm"
          placeholder="飲み会ID/会員ID"
        />
      </b-col>
      <!-- 生年月日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>日付</p>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromPresentDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromPresentDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toPresentDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toPresentDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <!-- search -->
      <b-col
        cols="1"
        style="text-align: right"
        class="ml-1"
      >
        <b-button
          size="sm"
          variant="outline-primary"
          @click="searchPresentPoint"
        >
          <b-icon icon="search" />
        </b-button>
      </b-col>
    </b-row>
    <b-row
      class="mt-2"
      align-h="center"
    />
    <b-table
      striped
      hover
      :fields="fieldPresentPoints"
      :items="filterPresentPointItems"
      :per-page="perPagePresentPoint"
      :current-page="currentPagePresentPoint"
    >
      <template v-slot:cell(index)="data">
        {{ data.index + 1 + (currentPagePresentPoint - 1) * perPagePresentPoint }}
      </template>
    </b-table>
    <div class="card-body">
      <v-pagination
        v-model="currentPagePresentPoint"
        :total="totalPresentPoint"
        :per-page="perPagePresentPoint"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import VPagination from '@/components/common/molecules/VPagination'
import moment from 'moment'

export default {
  name: 'DetailUserPresentPoint',
  components: { VPagination },
  props: {
    presentPoint: {
      type: Array,
      default: () => {}
    },
    user: {
      type: [Object, Array],
      default: () => {}
    }
  },
  data () {
    return {
      toPresentDate: null,
      fromPresentDate: null,
      currentPagePresentPoint: 1,
      perPagePresentPoint: 15,
      filterPresentPoint: {
        text: null,
        datetime: null
      },
      appliedFilterPresentPoint: {
        text: null,
        datetime: null
      },
      fieldPresentPoints: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user.detail.present_point.created_at')
        },
        {
          key: 'partyId',
          label: this.$t('models.user.detail.present_point.party_id')
        },
        {
          key: 'memberIdReceive',
          label: this.$t('models.user.detail.present_point.member_id_receive')
        },
        {
          key: 'userReceive',
          label: this.$t('models.user.detail.present_point.user-receive')
        },
        {
          key: 'point',
          label: this.$t('models.user.detail.present_point.point')
        }
      ]
    }
  },
  computed: {
    /**
             * filter Present Point Items
             */
    filterPresentPointItems () {
      let resultPresentPointItems = [...this.presentPoint]
      for (let i = 0; i < resultPresentPointItems.length; i++) {
        if (resultPresentPointItems[i].createdAt) {
          resultPresentPointItems[i].presentDateConvert = this.dateConvertToNumber(resultPresentPointItems[i].createdAt)
        }
      }

      resultPresentPointItems = this.filterPresentDay(resultPresentPointItems, this.appliedFilterPresentPoint.fromPresentDate, this.appliedFilterPresentPoint.toPresentDate)

      if (this.appliedFilterPresentPoint.text) {
        let text = this.appliedFilterPresentPoint.text
        resultPresentPointItems = resultPresentPointItems.filter(item => item.partyId == text || item.memberIdReceive == text)
      }
      if (this.appliedFilterPresentPoint.datetime) {
        resultPresentPointItems = resultPresentPointItems.filter(item => item.createdAtConvert == this.appliedFilterPresentPoint.datetime)
      }

      return resultPresentPointItems
    },
    /**
       * totalPresentPoint
       */
    totalPresentPoint () {
      return this.filterPresentPointItems.length
    }
  },
  methods: {
    /**
             * search Present Point
             */
    searchPresentPoint () {
      // convert from present date
      this.filterPresentPoint.fromPresentDate = this.dateConvertToNumber(this.fromPresentDate)
      // convert to present date
      this.filterPresentPoint.toPresentDate = this.dateConvertToNumber(this.toPresentDate)

      this.appliedFilterPresentPoint = { ...this.filterPresentPoint }
    },
    /**
       * date Convert To Number
       * @param datetime
       * @returns {number}
       */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')

        datetime = year + '' + month + '' + date
        return parseInt(datetime)
      }
      return null
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterPresentDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.presentDateConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.presentDateConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.presentDateConvert >= fromDay && item.presentDateConvert <= toDay)
      }
      return resultItem
    }
  }
}
</script>

<style scoped>

</style>
