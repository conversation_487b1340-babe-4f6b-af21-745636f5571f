<template>
  <b-card
    :header="$t('models.user.detail.user_block.user-block-title',{nickname: nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-table
      striped
      hover
      :fields="fieldUserBlocks"
      :items="userBlock"
      :per-page="perPageUserBlock"
      :current-page="currentPageUserBlock"
    >
      <template v-slot:cell(index)="data">
        {{ data.index + 1 + (currentPageUserBlock - 1) * perPageUserBlock }}
      </template>
    </b-table>
    <div class="card-body">
      <v-pagination
        v-model="currentPageUserBlock"
        :total="totalUserBlock"
        :per-page="perPageUserBlock"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import VPagination from '@/components/common/molecules/VPagination'
export default {
  name: 'DetailUserBlock',
  components: { VPagination },
  props: {
    userBlock: {
      type: Array,
      default: () => {}
    },
    nickname: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      currentPageUserBlock: 1,
      perPageUserBlock: 15,
      fieldUserBlocks: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user_block.field.created_at')
        },
        {
          key: 'blockUserStatus',
          label: this.$t('models.user_block.field.block_user_status')
        },
        {
          key: 'blockUserName',
          label: this.$t('models.user_block.field.block_user_name')
        }

      ]
    }
  },
  computed: {
    /** Tab User Block */
    totalUserBlock: {
      get: function () {
        return this.userBlock.length
      }
    }
  },
  methods: {

  }
}
</script>

<style scoped>

</style>
