<template>
  <b-card
    :header="$t('models.user.detail.friend.user-friend', {nickname: user.nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-row
      class="mt-2"
      align-h="center"
    >
      <b-col
        cols="4"
      >
        <b-form-input
          v-model="filterFriend.text"
          size="sm"
          placeholder="飲み会ID/会員ID/ニックネーム"
        />
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterFriend.gender"
          size="sm"
          :options="$root.lazyOption.genderOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              性別
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterFriend.status"
          size="sm"
          :options="$root.lazyOption.userStatusOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              ステータス
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="1"
        class="ml-3"
        style="text-align: right"
      >
        <b-button
          size="sm"
          variant="outline-primary"
          @click="searchFriend"
        >
          <b-icon icon="search" />
        </b-button>
      </b-col>
    </b-row>
    <b-row
      class="mt-2"
      align-h="center"
    >
      <!-- 生年月日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>生年月日</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromBirthday"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromBirthday"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toBirthday"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toBirthday"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-row
      class="mt-3"
      align-h="center"
    >
      <!-- 登録日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>登録日</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromRegisterDay"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromRegisterDay"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toRegisterDay"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toRegisterDay"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-row
      class="mt-3"
      align-h="center"
    >
      <!-- 退会日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>撤退日</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromWithdrawDay"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromWithdrawDay"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toWithdrawDay"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toWithdrawDay"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-row
      class="mt-3 mb-2"
      align-h="between"
    >
      <b-col
        cols="6"
        style="text-align: left"
      >
        <b-button-group>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByMale"
          >
            男性
          </b-button>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByFeMale"
          >
            女性
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <b-table
      striped
      hover
      :fields="fieldFriends"
      :items="filterFriendItems"
      :per-page="perPageFriend"
      :current-page="currentPageFriend"
      :sort-by.sync="sortBy"
      :sort-desc.sync="sortDesc"
      style="text-align: center;vertical-align: middle"
    >
      <template v-slot:cell(index)="data">
        {{ data.index + 1 + (currentPageFriend-1) * perPageFriend }}
      </template>
      <template v-slot:head(actions)="row">
        <i class="far fa-envelope fa-2x" />
      </template>
      <template v-slot:head(detail)="row">
        <i class="fas fa-equals" />
      </template>
      <template
        v-slot:cell(actions)="row"
        style="text-align: center"
      >
        <b-button
          v-if="row.item.hasMessage"
          size="sm"
          class="btn"
          variant="link"
          @click="goToMessage(row.item.userId, row.item.friendId)"
        >
          <i class="far fa-envelope fa-2x" />
        </b-button>
      </template>
      <template v-slot:cell(detail)="row">
        <b-button
          size="sm"
          variant="link"
          class="btn "
          @click="goToProfile(row.item.friendId)"
        >
          <i class="fas fa-equals" />
        </b-button>
      </template>
    </b-table>
    <div class="card-body">
      <v-pagination
        v-model="currentPageFriend"
        :total="totalFriend"
        :per-page="perPageFriend"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import moment from 'moment'
import VPagination from '@/components/common/molecules/VPagination'
export default {
  name: 'DetailUserFriend',
  components: { VPagination },
  props: {
    friend: {
      type: Array,
      default: () => {}
    },
    user: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      totalFriend: 0,
      friendDate: null,
      fromBirthday: null,
      toBirthday: null,
      fromRegisterDay: null,
      toRegisterDay: null,
      fromWithdrawDay: null,
      toWithdrawDay: null,
      currentPageFriend: 1,
      perPageFriend: 15,
      sortBy: 'genderName',
      sortDesc: true,
      filterFriend: {
        text: null,
        gender: null,
        status: null
      },
      appliedFilterFriend: {
        text: null,
        gender: null,
        status: null
      },
      fieldFriends: [
        {
          key: 'index',
          label: this.$t('models.user.field.index'),
          tdClass: 'action-size'
        },
        {
          key: 'partyId',
          label: this.$t('models.user.detail.friend.partyId')
        },
        {
          key: 'memberId',
          label: this.$t('models.user.detail.friend.userId')
        },
        {
          key: 'statusName',
          label: this.$t('models.user.detail.friend.status')
        },
        {
          key: 'genderName',
          label: this.$t('models.user.field.gender')
        },
        {
          key: 'nickname',
          label: this.$t('models.user.field.nickname')
        },
        {
          key: 'birthday',
          label: this.$t('models.user.field.birthday')
        },
        {
          key: 'registerAt',
          label: this.$t('models.user.field.register_day')
        },
        {
          key: 'withdrawAt',
          label: this.$t('models.user.field.withdraw_day')
        },
        {
          key: 'actions',
          label: 'msg'
        },
        {
          key: 'detail',
          label: '='
        }
      ]
    }
  },
  computed: {
    /**
         * filter Friend Items
         */
    filterFriendItems () {
      let resultFriendItems = [...this.friend]
      // convert date data
      for (let i = 0; i < resultFriendItems.length; i++) {
        if (resultFriendItems[i].birthday) {
          resultFriendItems[i].birthdayConvert = this.dateConvertToNumber(resultFriendItems[i].birthday)
        }
        if (resultFriendItems[i].registerAt) {
          resultFriendItems[i].registerAtConvert = this.dateConvertToNumber(resultFriendItems[i].registerAt)
        }
        if (resultFriendItems[i].withdrawAt) {
          resultFriendItems[i].withdrawAtConvert = this.dateConvertToNumber(resultFriendItems[i].withdrawAt)
        }
      }
      // search text
      if (this.appliedFilterFriend.text) {
        let text = this.appliedFilterFriend.text
        resultFriendItems = resultFriendItems.filter(item => item.partyId == text || item.memberId == text || ((item.nickname) ? item.nickname.includes(text) == true : false))
      }

      resultFriendItems = this.filterBirthday(resultFriendItems, this.appliedFilterFriend.fromBirthday, this.appliedFilterFriend.toBirthday)
      resultFriendItems = this.filterRegisterDay(resultFriendItems, this.appliedFilterFriend.fromRegisterDay, this.appliedFilterFriend.toRegisterDay)
      resultFriendItems = this.filterWithdrawDay(resultFriendItems, this.appliedFilterFriend.fromWithdrawDay, this.appliedFilterFriend.toWithdrawDay)

      // search with gender
      if (this.appliedFilterFriend.gender) {
        resultFriendItems = resultFriendItems.filter(item => item.gender == this.appliedFilterFriend.gender)
      }
      // search with status
      if (this.appliedFilterFriend.status) {
        resultFriendItems = resultFriendItems.filter(item => item.status == this.appliedFilterFriend.status)
      }
      this.totalFriend = resultFriendItems.length
      return resultFriendItems
    }
  },
  methods: {
    /**
       * search Friend
       */
    searchFriend () {
      // convert from birthday
      this.filterFriend.fromBirthday = this.dateConvertToNumber(this.fromBirthday)
      // convert to birthday
      this.filterFriend.toBirthday = this.dateConvertToNumber(this.toBirthday)

      // convert from register day
      this.filterFriend.fromRegisterDay = this.dateConvertToNumber(this.fromRegisterDay)
      // convert to register day
      this.filterFriend.toRegisterDay = this.dateConvertToNumber(this.toRegisterDay)

      // convert from withdraw day
      this.filterFriend.fromWithdrawDay = this.dateConvertToNumber(this.fromWithdrawDay)
      // convert to withdraw day
      this.filterFriend.toRegisterDay = this.dateConvertToNumber(this.toRegisterDay)

      this.appliedFilterFriend = { ...this.filterFriend }
    },
    /**
         * goToProfile
         */
    goToProfile (id) {
      location.href = this.route('admin.user.show', [id, 0])
    },
    /**
     * go To Message
     */
    goToMessage (userId, partnerId) {
      location.href = this.route('admin.user.message.show', [userId, partnerId])
    },
    /**
       * date Convert To Number
       * @param datetime
       * @returns {number|null}
       */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')
        if (date > 0 && date < 10) {
          date = '0' + date
        }
        if (month > 0 && month < 10) {
          month = '0' + month
        }
        datetime = year + '' + month + '' + date
        return parseInt(datetime)
      }
      return null
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterBirthday (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.birthdayConvert >= fromDay && item.birthdayConvert <= toDay)
      }
      return resultItem
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterRegisterDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.registerAtConvert >= fromDay && item.registerAtConvert <= toDay)
      }
      return resultItem
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterWithdrawDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.withdrawAtConvert >= fromDay && item.withdrawAtConvert <= toDay)
      }
      return resultItem
    },
    /**
       * sort By Male
       */
    sortByMale () {
      this.sortDesc = true
    },
    /**
       * sort By FeMale
       */
    sortByFeMale () {
      this.sortDesc = false
    }

  }
}
</script>

<style scoped>

</style>
