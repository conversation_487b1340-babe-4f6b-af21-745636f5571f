<template>
  <b-card>
    <b-table-simple
      bordered
      small
    >
      <b-tbody class="custom-table-feedback">
        <!-- contact ID -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            受信ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactId }}
          </b-td>
        </b-tr>

        <!-- Contact Member ID-->
        <b-tr>
          <b-th
            class="text-center border"
          >
            会員ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactMemberId }}
          </b-td>
        </b-tr>

        <!-- Status -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            ステータス
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
            :style="{color : reportStatus === 1 ? 'red' : 'black'}"
          >
            {{ contactStatusName }}
          </b-td>
        </b-tr>

        <!-- Date -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            日付
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactAt }}
          </b-td>
        </b-tr>

        <!-- Report Item -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            お問合せ項目
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ contactDivName }}
          </b-td>
        </b-tr>

        <!-- content -->
        <b-tr>
          <b-td
            class="text-left border"
            colspan="7"
          >
            {{ content }}
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <!-- Button Back -->
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        @click="goToUserContactList(userId)"
      >
        {{ $t('common.btn.back') }}
      </b-button>
      <b-button
        v-show="!hasFeedback"
        variant="outline-primary"
        class="btn"
        @click="goToFeedback(contactId)"
      >
        {{ $t('common.btn.feedback') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import TabUserIndex from '@/constant/tab_user_index'
export default {
  name: 'UserContactDetail',
  props: {
    userContact: {
      type: [Object, Array],
      default: () => ({
        //
      })
    },
    userId: {
      type: [String, Number],
      default: null
    },
    gender: {
      type: Number,
      default: null
    }
  },
  data () {
    const { contactId, contactMemberId, contactStatusName, contactAt, contactDivName, content, hasFeedback, nicknameUserContact, genderId } = this.userContact
    return {
      contactId,
      contactMemberId,
      nicknameUserContact,
      contactStatusName,
      contactAt,
      contactDivName,
      content,
      hasFeedback,
      genderId
    }
  },
  methods: {
    /**
       * goToUserContactList
       *
       * @param userId
       */
    goToUserContactList (userId) {
      if (this.gender === 1) {
        location.href = this.route('admin.user.show', [userId, TabUserIndex.MALE.TAB_CONTACT])
      } else {
        location.href = this.route('admin.user.show', [userId, TabUserIndex.FEMALE.TAB_CONTACT])
      }
    },
    /**
       * goToFeedback
       *
       * @param contactId
       */
    goToFeedback (contactId) {
      location.href = this.route('admin.feedback_contact.create') + '?user_id=' + this.userId + '&gender=' + this.gender + '&contact_id=' + contactId
    }
  }
}
</script>

<style scoped>
  .custom-table-feedback th {
    text-align: center;
    width: 350px;
    vertical-align: middle;
  }

  .custom-table-feedback td {
    text-align: center;
    vertical-align: middle;
  }

  .custom-btn-send-now {
    width: 120px;
    text-align: center;
    vertical-align: middle;
  }
</style>
