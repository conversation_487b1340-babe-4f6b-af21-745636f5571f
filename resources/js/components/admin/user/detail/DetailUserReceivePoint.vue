<template>
  <!-- gender = female -->
  <b-card
    :header="$t('models.user.detail.receive_point.user-receive-point',{nickname: user.nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-row
      class="mb-5 mt-2"
      align-h="start"
    >
      <!-- input -->
      <b-col cols="4">
        <b-form-input
          v-model="filterReceivePoint.text"
          size="sm"
          placeholder="飲み会ID/会員ID"
        />
      </b-col>
      <!-- 生年月日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>日付</p>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromReceiveDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromReceiveDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toReceiveDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toReceiveDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <!-- search -->
      <b-col
        cols="1"
        style="text-align: right"
        class="ml-1"
      >
        <b-button
          size="sm"
          variant="outline-primary"
          @click="searchReceivePoint"
        >
          <b-icon icon="search" />
        </b-button>
      </b-col>
    </b-row>
    <b-row
      class="mt-2"
      align-h="center"
    />
    <b-table
      striped
      hover
      :fields="fieldReceivePoint"
      :items="filterReceivePointItems"
      :per-page="perPageReceivePoint"
      :current-page="currentPageReceivePoint"
    >
      <template v-slot:cell(index)="data">
        {{ data.index + 1 + (currentPageReceivePoint - 1) * perPageReceivePoint }}
      </template>
    </b-table>
    <div class="card-body">
      <v-pagination
        v-model="currentPageReceivePoint"
        :total="totalReceivePoint"
        :per-page="perPageReceivePoint"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import VPagination from '@/components/common/molecules/VPagination'
import moment from 'moment'

export default {
  name: 'DetailUserReceivePoint',
  components: { VPagination },
  props: {
    receivePoint: {
      type: Array,
      default: () => {}
    },
    user: {
      type: [Object, Array],
      default: () => {}
    }
  },
  data () {
    return {
      toReceiveDate: null,
      fromReceiveDate: null,
      currentPageReceivePoint: 1,
      perPageReceivePoint: 15,
      filterReceivePoint: {
        text: null
      },
      appliedFilterReceivePoint: {
        text: null
      },
      fieldReceivePoint: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user.detail.receive_point.created_at')
        },
        {
          key: 'partyId',
          label: this.$t('models.user.detail.receive_point.party_id')
        },
        {
          key: 'memberIdPresent',
          label: this.$t('models.user.detail.receive_point.member_id_present')
        },
        {
          key: 'userPresent',
          label: this.$t('models.user.detail.receive_point.user_present')
        },
        {
          key: 'point',
          label: this.$t('models.user.detail.receive_point.point_present')
        }
      ]
    }
  },
  computed: {
    /**
             * filterReceivePointItems
             */
    filterReceivePointItems () {
      let resultReceivePointItems = [...this.receivePoint]
      for (let i = 0; i < resultReceivePointItems.length; i++) {
        if (resultReceivePointItems[i].createdAt) {
          resultReceivePointItems[i].receiveDateConvert = this.dateConvertToNumber(resultReceivePointItems[i].createdAt)
        }
      }
      resultReceivePointItems = this.filterReceiveDay(resultReceivePointItems, this.appliedFilterReceivePoint.fromReceiveDate, this.appliedFilterReceivePoint.toReceiveDate)

      if (this.appliedFilterReceivePoint.text) {
        let text = this.appliedFilterReceivePoint.text
        resultReceivePointItems = resultReceivePointItems.filter(item => item.partyId == text || item.memberIdPresent == text)
      }
      if (this.appliedFilterReceivePoint.datetime) {
        resultReceivePointItems = resultReceivePointItems.filter(item => item.createdAtConvert == this.appliedFilterReceivePoint.datetime)
      }
      return resultReceivePointItems
    },
    /**
       * totalReceivePoint
       */
    totalReceivePoint () {
      return this.filterReceivePointItems.length
    }

  },
  methods: {
    /**
             * search Present Point
             */
    searchReceivePoint () {
      // convert from present date
      this.filterReceivePoint.fromReceiveDate = this.dateConvertToNumber(this.fromReceiveDate)
      // convert to present date
      this.filterReceivePoint.toReceiveDate = this.dateConvertToNumber(this.toReceiveDate)

      this.appliedFilterReceivePoint = { ...this.filterReceivePoint }
    },
    /**
             * date Convert To Number
             * @param datetime
             * @returns {null|number}
             */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')
        datetime = year + '' + month + '' + date
        return parseInt(datetime)
      }
      return null
    },
    /**
             *
             * @param resultItem
             * @param fromDay
             * @param toDay
             * @returns {*}
             */
    filterReceiveDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.receiveDateConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.receiveDateConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.receiveDateConvert >= fromDay && item.receiveDateConvert <= toDay)
      }
      return resultItem
    }
  }
}
</script>

<style scoped>

</style>
