<template>
  <b-card
    :header="$t('models.user.detail.user_report.user-report',{nickname: user.nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-row
      class="mt-2"
      align-h="center"
    >
      <b-col
        cols="4"
      >
        <b-form-input
          v-model="filterReport.text"
          size="sm"
          type="text"
          placeholder="フリーキーワード"
        />
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterReport.reportDiv"
          size="sm"
          :options="$root.lazyOption.reportDivOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              お問合せ
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filterReport.reportStatus"
          size="sm"
          :options="$root.lazyOption.reportStatusOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              ステータス
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="1"
        style="text-align: right"
      >
        <b-button
          size="sm"
          variant="outline-primary"
          class="sm"
          @click="searchReport"
        >
          <b-icon
            icon="search"
            aria-hidden="true"
          />
        </b-button>
      </b-col>
    </b-row>
    <b-row
      class="mt-3"
      align-h="center"
    >
      <!-- 生年月日 -->
      <b-col
        cols="1"
        style="text-align: center"
        class="custom-col-search"
      >
        <p>通報日</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="fromReportDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="fromReportDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
      <b-col
        style="text-align: center"
        class="custom-col-search"
        cols="1"
      >
        <p>~</p>
      </b-col>
      <b-col
        cols="2.5"
        class="custom-col-search"
      >
        <b-input-group>
          <b-datepicker
            v-model="toReportDate"
            size="sm"
            left
            button-only
            locale="ja"
          />
          <b-input-group-append>
            <b-form-input
              v-model="toReportDate"
              size="sm"
              type="text"
              placeholder="登録日を入力してください"
              style="width: 180px"
            />
          </b-input-group-append>
        </b-input-group>
      </b-col>
    </b-row>
    <b-row
      class="mt-3 mb-3"
      align-h="between"
    >
      <b-col
        cols="6"
        style="text-align: left"
      >
        <b-button-group>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByNewDate"
          >
            新しい日付
          </b-button>
          <b-button
            size="sm"
            variant="outline-secondary"
            style="width: 120px; text-align: center"
            @click="sortByOldDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
    </b-row>
    <b-table-simple
      :per-page="perPageUserReport"
      :current-page="currentPageUserReport"
      style="text-align: center"
    >
      <b-thead>
        <b-tr>
          <b-th>
            {{ $t('models.user.field.index') }}
          </b-th>
          <b-th>
            {{ $t('models.user.detail.user_report.report-id') }}
          </b-th>
          <b-th>
            日付
          </b-th>
          <b-th>
            {{ $t('models.user.detail.user_report.report-status') }}
          </b-th>
          <b-th>
            {{ $t('models.user.detail.user_report.report-warning') }}
          </b-th>
          <b-th>
            {{ $t('models.user.detail.user_report.report-div') }}
          </b-th>
          <b-th>
            <i class="far fa-envelope fa-2x" />
          </b-th>
          <b-th>
            <i class="fas fa-equals fa-1x" />
          </b-th>
        </b-tr>
      </b-thead>
      <b-tbody
        v-for="(row, index) in items"
        :key="index"
      >
        <b-tr
          v-if="row.hasFeedback"
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            {{ index + 1 + (currentPageUserReport - 1) * perPageUserReport }}
          </b-td>
          <b-td class="border">
            {{ row.sendId }}
          </b-td>
          <b-td class="border">
            {{ row.reportAt }}
          </b-td>
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            <p>{{ row.reportStatusName }}</p>
          </b-td>
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            {{ row.reportWarningName }}
          </b-td>
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            {{ row.reportDivName }}
          </b-td>
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          />
          <b-td
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToUserReportDetail(row.sendId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
        <b-tr
          v-if="row.hasFeedback"
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td class="border">
            {{ row.feedbackId }}
          </b-td>
          <b-td class="border">
            {{ row.feedbackAt }}
          </b-td>
          <b-td class="border">
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToFeedbackDetail(row.feedbackId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
        <!-- else -->
        <b-tr
          v-else
          :style="{'background-color' : (isEven(index)===true) ? '#f6f6f6' : ''}"
        >
          <b-td
            rowspan="2"
            style="vertical-align: middle; text-align: center"
            class="border"
          >
            {{ index + 1 + (currentPageUserReport - 1) * perPageUserReport }}
          </b-td>
          <b-td class="border">
            {{ row.sendId }}
          </b-td>
          <b-td class="border">
            {{ row.createdAt }}
          </b-td>
          <b-td class="border">
            <b-form-select
              v-model="row.reportStatus"
              name="reportStatus"
              :style="{color : row.reportStatus === 1 ? 'red' : 'black'}"
              style="width: 120px"
              size="sm"
              :options="options"
              @change="updateReportStatus(row)"
            />
          </b-td>
          <b-td class="border">
            {{ row.reportWarningName }}
          </b-td>
          <b-td class="border">
            {{ row.reportDivName }}
          </b-td>
          <b-td class="border">
            <b-button
              size="sm"
              class="mr-1 btn"
              variant="link"
              @click="goToFeedback(row.sendId)"
            >
              <i class="far fa-envelope fa-2x" />
            </b-button>
          </b-td>
          <b-td class="border">
            <b-button
              variant="link"
              size="sm"
              class="mr-1 btn"
              @click="goToUserReportDetail(row.sendId)"
            >
              <i class="fas fa-equals fa-1x" />
            </b-button>
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <div class="card-body">
      <v-pagination
        v-model="currentPageUserReport"
        :total="totalUserReport"
        :per-page="perPageUserReport"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import TabUserIndex from '@/constant/tab_user_index'
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'
import VPagination from '@/components/common/molecules/VPagination'
import moment from 'moment'

export default {
  name: 'DetailUserReport',
  components: { VPagination },
  props: {
    userReport: {
      type: [Object, Array],
      default: () => {}
    },
    user: {
      type: [Object, Array],
      default: () => {
      }
    }
  },
  data () {
    return {
      fromReportDate: null,
      toReportDate: null,
      currentPageUserReport: 1,
      perPageUserReport: 5,
      filterReport: {
        text: null,
        datetime: null,
        reportDiv: null,
        reportStatus: null
      },
      appliedFilterReport: {
        text: null,
        datetime: null,
        reportDiv: null,
        reportStatus: null
      },
      fieldUserReports: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'sendId',
          label: this.$t('models.user.detail.user_report.report-id')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user.detail.user_report.created_at')
        },
        {
          key: 'reportStatusName',
          label: this.$t('models.user.detail.user_report.report-status')
        },
        {
          key: 'reportWarningName',
          label: this.$t('models.user.detail.user_report.report-warning')
        },
        {
          key: 'reportDivName',
          label: this.$t('models.user.detail.user_report.report-div')
        },
        {
          key: 'msg',
          label: 'msg'
        },
        {
          key: 'actions',
          label: this.$t('models.user.detail.user_report.detail_info')
        }

      ],
      options: [
        { value: 1, text: '未対応' },
        { value: 3, text: '対応不可' }
      ]
    }
  },
  computed: {
    /** Tab User Report */
    totalUserReport: {
      get: function () {
        return this.filterReportItems.length
      }
    },
    /**
       * filter Report Items
       */
    filterReportItems () {
      let resultReportItems = [...this.userReport]
      for (let i = 0; i < resultReportItems.length; i++) {
        if (resultReportItems[i].createdAt) {
          resultReportItems[i].reportDateConvert = this.dateConvertToNumber(resultReportItems[i].createdAt)
        }
      }
      // filter report day
      resultReportItems = this.filterReportDay(resultReportItems, this.appliedFilterReport.fromReportDate, this.appliedFilterReport.toReportDate)

      if (this.appliedFilterReport.reportStatus) {
        resultReportItems = resultReportItems.filter(item => item.reportStatus == this.appliedFilterReport.reportStatus)
      }
      if (this.appliedFilterReport.reportDiv) {
        resultReportItems = resultReportItems.filter(item => item.reportDiv == this.appliedFilterReport.reportDiv)
      }
      if (this.appliedFilterReport.text) {
        let text = this.appliedFilterReport.text
        resultReportItems = resultReportItems.filter(item => item.sendId == text || item.feedbackId == text || item.content.includes(text))
      }
      if (this.appliedFilterReport.datetime) {
        resultReportItems = resultReportItems.filter(item => item.createdAtConvert == this.appliedFilterReport.datetime)
      }

      return resultReportItems
    },
    /**
       * items
       */
    items () {
      let results = []
      let resultReportItems = this.filterReportItems
      let k = 0
      if (this.currentPageUserReport) {
        let length = resultReportItems.length
        if (length <= this.perPageUserReport) {
          results = resultReportItems
        } else {
          let currentPage = this.currentPageUserReport
          let from = (currentPage - 1) * this.perPageUserReport
          let target = currentPage * this.perPageUserReport
          if (target >= length) {
            for (let i = from; i < length; i++) {
              results[k] = resultReportItems[i]
              k = k + 1
            }
          } else {
            for (let i = from; i < target; i++) {
              results[k] = resultReportItems[i]
              k = k + 1
            }
          }
        }
      }
      return results
    }
  },

  methods: {
    fetchPage (currentPage) {
      let resultReportItems = this.filterReportItems()
      let results = []
      let count = resultReportItems.length
      let from = (currentPage - 1) * this.perPageUserReport
      let target = currentPage * this.perPageUserReport
      let to = 0;
      (target > count) ? to == count : to == target
      let k = 0
      for (let i = from; i < to; i++) {
        results[k] = resultReportItems[i]
        k = k + 1
      }
      return results
    },
    /**
       * sort By New Date
       */
    sortByNewDate () {
      this.userReport = this.userReport.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
    },
    /**
       * sort By Old Date
       */
    sortByOldDate () {
      this.userReport = this.userReport.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
    },

    /**
       * search Report
       */
    searchReport () {
      // convert from report date
      this.filterReport.fromReportDate = this.dateConvertToNumber(this.fromReportDate)
      // convert to report date
      this.filterReport.toReportDate = this.dateConvertToNumber(this.toReportDate)

      this.appliedFilterReport = { ...this.filterReport }
    },
    /**
       * date Convert To Number
       * @param datetime
       * @returns {number}
       */
    dateConvertToNumber (datetime) {
      if (datetime) {
        let d = new Date(datetime)
        let year = moment(d).format('YYYY')
        let month = moment(d).format('MM')
        let date = moment(d).format('DD')
        datetime = year + '' + month + '' + date
        return parseInt(datetime)
      }
      return null
    },
    /**
       *
       * @param resultItem
       * @param fromDay
       * @param toDay
       * @returns {*}
       */
    filterReportDay (resultItem, fromDay, toDay) {
      // search from
      if (fromDay) {
        resultItem = resultItem.filter(item => item.reportDateConvert >= fromDay)
      }
      // search to
      if (toDay) {
        resultItem = resultItem.filter(item => item.reportDateConvert <= toDay)
      }
      // search and
      if (fromDay && toDay) {
        resultItem = resultItem.filter(item => item.reportDateConvert >= fromDay && item.reportDateConvert <= toDay)
      }
      return resultItem
    },
    /**
       * goToUserDetail
       * @param userId
       */
    goToUserDetail (userId) {
      location.href = this.route('admin.user.show', [userId, 0])
    },
    /**
     * @param sendId
     */
    goToFeedback (sendId) {
      location.href = this.route('admin.feedback_report.create') + '?user_id=' + this.user.id + '&gender=' + this.user.gender + '&report_id=' + sendId
    },
    /**
 * goToUserReportDetail
     * @param sendId
 */
    goToUserReportDetail (sendId) {
      location.href = this.route('admin.detail_user_report.show') + '?user_id=' + this.user.id + '&gender=' + this.user.gender + '&report_id=' + sendId
    },
    /**
 * goToFeedbackDetail
     * @param feedbackId
 */
    goToFeedbackDetail (feedbackId) {
      location.href = this.route('admin.feedback_report.show', [feedbackId]) + '?user_id=' + this.user.id + '&gender=' + this.user.gender
    },
    /**
       * updateReportStatus
       *
       * @param data
       * @returns {Promise<void>}
       */
    async updateReportStatus (data) {
      try {
        await axios.post(this.route('admin.user_report.update', data.sendId), { 'report_status': data.reportStatus })
        if (this.user.gender === 1) {
          location.href = this.route('admin.user.show', [this.user.id, TabUserIndex.MALE.TAB_REPORT])
        } else {
          location.href = this.route('admin.user.show', [this.user.id, TabUserIndex.FEMALE.TAB_REPORT])
        }
      } catch (e) {
        this.$nextTick(() => {
          throw new Error(extractLaravelError(e))
        })
      }
    },
    /**
       *
       * @param number
       * @returns {string}
       */
    goToPartyDetail () {
      //
    },
    isEven (value) {
      if (value % 2 === 0) { return true } else { return false }
    }
  }
}
</script>

<style scoped>

</style>
