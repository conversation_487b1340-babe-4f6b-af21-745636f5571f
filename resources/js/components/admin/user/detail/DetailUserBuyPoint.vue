<template>
  <b-card
    :header="$t('models.user.detail.buy_point.user-buy-point',{nickname: nickname})"
    header-text-variant="black"
    align="left"
    class="mt-3"
  >
    <b-table
      striped
      hover
      :fields="fieldBuyPoints"
      :items="buyPoint"
      :per-page="perPageBuyPoint"
      :current-page="currentPageBuyPoint"
    >
      <template v-slot:cell(index)="data">
        {{ data.index + 1 + (currentPageBuyPoint - 1) * perPageBuyPoint }}
      </template>
    </b-table>
    <div class="card-body">
      <v-pagination
        v-model="currentPageBuyPoint"
        :total="totalBuyPoint"
        :per-page="perPageBuyPoint"
      />
    </div>
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn btn-default"
        :href="route('admin.user.index')"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import VPagination from '@/components/common/molecules/VPagination'
export default {
  name: 'DetailUserBuyPoint',
  components: { VPagination },
  props: {
    buyPoint: {
      type: Array,
      default: () => {}
    },
    nickname: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      currentPageBuyPoint: 1,
      perPageBuyPoint: 15,
      fieldBuyPoints: [
        {
          key: 'index',
          label: this.$t('models.user.field.index')
        },
        {
          key: 'created_at',
          label: this.$t('models.user.detail.buy_point.created_at')
        },
        {
          key: 'point',
          label: this.$t('models.user.detail.buy_point.point')
        },
        {
          key: 'price',
          label: this.$t('models.user.detail.buy_point.price')
        }

      ]
    }
  },
  computed: {
    /** Tab Buy Point */
    totalBuyPoint: {
      get: function () {
        return this.buyPoint.length
      }
    }
  },
  methods: {

  }
}
</script>

<style scoped>

</style>
