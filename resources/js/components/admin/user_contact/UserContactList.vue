<template>
  <div>
    <b-row style="margin-bottom: 1rem; padding: 0 15px">
      <b-col
        cols="5"
        class="custom-col-search"
      >
        <b-form-input
          v-model="filter.text"
          size="sm"
          type="text"
          placeholder="お問合せID/会員ID/ニックネーム"
          @keyup.enter="search"
        />
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filter.genderUser"
          size="sm"
          :options="$root.lazyOption.genderOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              性別
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filter.contactDiv"
          size="sm"
          :options="$root.lazyOption.contactDivOption"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              お問合せ
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col
        cols="2"
        class="custom-col-search"
      >
        <b-form-select
          v-model="filter.contactStatus"
          size="sm"
          :options="$root.lazyOption.contactState"
        >
          <template #first>
            <b-form-select-option
              :value="null"
            >
              ステータス
            </b-form-select-option>
          </template>
        </b-form-select>
      </b-col>
      <b-col>
        <b-button
          variant="outline-info"
          class="mb-2"
          size="sm"
          @click="search"
        >
          <b-icon
            icon="search"
            aria-hidden="true"
          />
        </b-button>
      </b-col>
    </b-row>
    <b-row style="margin-bottom: 1rem">
      <b-col cols="6">
        <b-button-group>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortOldDate"
          >
            新しい日付
          </b-button>
          <b-button
            squared
            variant="outline-secondary"
            size="sm"
            style="width: 120px"
            @click="sortNewDate"
          >
            古い日付
          </b-button>
        </b-button-group>
      </b-col>
      <b-col style="text-align: right">
        <b-button
          squared
          variant="outline-success"
          size="sm"
          @click="downloadContactCSV"
        >
          <i class="fas fa-download" />
          ＣＳＶダウンロード
        </b-button>
      </b-col>
    </b-row>
    <div class="card">
      <b-table
        striped
        hover
        bordered
        empty-text="データが選択されていません。"
        :fields="fields"
        :items="filterItems"
        :per-page="perPage"
        :current-page="currentPage"
        table-class="custom-table"
      >
        <template v-slot:head(msg)="row">
          <i class="far fa-envelope fa-2x" />
        </template>
        <template v-slot:head(actions)="row">
          <i class="fas fa-equals fa-1x" />
        </template>
        <template v-slot:cell(memberId)="row">
          <b-button
            variant="link"
            @click="gotoUserDetail(row.item.userId)"
          >
            {{ row.item.memberId }}
          </b-button>
        </template>
        <template v-slot:cell(contactStatusName)="row">
          <div
            v-if="row.item.contactStatus === 1"
            style="color: #ff0000"
          >
            {{ row.item.contactStatusName }}
          </div>
          <div v-else>
            {{ row.item.contactStatusName }}
          </div>
        </template>
        <template v-slot:cell(msg)="row">
          <b-button
            variant="link"
            class="btn"
            @click="goToSendContact(row.item.userId, row.item.id, row.item.genderId)"
          >
            <i
              class="far fa-envelope fa-2x"
            />
          </b-button>
        </template>
        <template v-slot:cell(actions)="row">
          <b-button
            variant="link"
            size="sm"
            class="btn"
            @click="goToDetail(row.item)"
          >
            <i class="fas fa-equals fa-1x" />
          </b-button>
        </template>
      </b-table>
      <div class="card-footer">
        <div>
          <div class="level-left">
            {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
          </div>
          <b-pagination
            v-if="filterItems.length > perPage"
            v-model="currentPage"
            :total-rows="total"
            :per-page="perPage"
            aria-controls="chat"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'UserContactList',
  props: {
    userContact: {
      type: [Object, Array],
      default: function () {
        return {
        }
      }
    }
  },
  data () {
    return {
      userContacts: this.userContact || [],
      perPage: 15,
      currentPage: 1,
      filter: {
        genderUser: null,
        contactDiv: null,
        contactStatus: null,
        text: null
      },
      appliedFilter: {
        genderUser: null,
        contactDiv: null,
        contactStatus: null,
        text: null
      },
      fields: [
        {
          key: 'id',
          label: this.$t('models.user_contact.field.id')
        },
        {
          key: 'memberId',
          label: this.$t('models.user_contact.field.user_id')
        },
        {
          key: 'gender',
          label: this.$t('models.user_contact.field.gender')
        },
        {
          key: 'contactStatusName',
          label: this.$t('models.user_contact.field.user_status')
        },
        {
          key: 'createdAt',
          label: this.$t('models.user_contact.field.contact_at')
        },
        {
          key: 'contactDivName',
          label: this.$t('models.user_contact.field.contact_div')
        },
        {
          key: 'msg',
          label: 'msg'
        },
        {
          key: 'actions',
          label: '=',
          tdClass: 'action-size'
        }
      ]
    }
  },
  computed: {
    filterItems () {
      let resultItems = [...this.userContacts]
      if (this.appliedFilter.genderUser) {
        resultItems = resultItems.filter(item => item.genderId == this.appliedFilter.genderUser)
      }
      if (this.appliedFilter.contactDiv) {
        resultItems = resultItems.filter(item => item.contactDiv == this.appliedFilter.contactDiv)
      }
      if (this.appliedFilter.contactStatus) {
        resultItems = resultItems.filter(item => item.contactStatus == this.appliedFilter.contactStatus)
      }
      if (this.appliedFilter.text) {
        resultItems = resultItems.filter(item => item.nicknameUser.toUpperCase().includes(this.appliedFilter.text.toUpperCase()) || item.id == this.appliedFilter.text || item.memberId == this.appliedFilter.text)
      }
      return resultItems
    },
    total () {
      return this.filterItems.length
    },
    from () {
      if (this.filterItems.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
    }
  },
  mounted () {
    this.search()
  },
  methods: {
    /**
       *
       */
    downloadContactCSV () {
      location.href = this.route('admin.user_contact.download_csv')
    },
    /**
       * goToDetail
       *
       * @param userContact
       */
    goToDetail (userContact) {
      location.href = this.route('admin.user_contact.show', userContact.id)
    },
    /**
       * gotoUserDetail
       *
       * @param userId
       */
    gotoUserDetail (userId) {
      location.href = this.route('admin.user.show', [userId, 0])
    },
    /**
     * goToSendContact
     */
    goToSendContact (userId, id, gender) {
      location.href = this.route('admin.feedback_contact.create') + '?user_id=' + userId + '&contact_id=' + id + '&gender=' + gender
    },
    search () {
      this.appliedFilter = { ...this.filter }
    },
    /**
       * sortOldDate
       */
    sortOldDate () {
      this.userContacts = this.userContacts.sort(function (a, b) {
        return new Date(b.createdAt) - new Date(a.createdAt)
      })
    },
    /**
       * sortNewDate
       */
    sortNewDate () {
      this.userContacts = this.userContacts.sort(function (a, b) {
        return new Date(a.createdAt) - new Date(b.createdAt)
      })
    }
  }
}
</script>
