<template>
  <div class="card-body">
    <!-- Contact Div Field -->
    <v-radio
      v-model="contactDiv"
      :validation-filed="$t('models.user_contact.field.contact_div')"
      validation-rules=""
      :label="$t('models.user_contact.field.contact_div')"
      :options="$root.lazyOption.contactDivOption"
      name="contact_div"
    />

    <!-- Body Field -->
    <v-input
      v-model="body"
      :validation-filed="$t('models.user_contact.field.body')"
      validation-rules="max:2000"
      type="text"
      :label="$t('models.user_contact.field.body')"
      name="body"
      maxlength="2000"
    />

    <!-- Contact Status Field -->
    <v-input
      v-model="contactStatus"
      :validation-filed="$t('models.user_contact.field.contact_status')"
      validation-rules="max:2000"
      type="text"
      :label="$t('models.user_contact.field.body')"
      name="contact_status"
      maxlength="2000"
    />
  </div>
</template>

<script>
import VRadio from '@/components/common/molecules/VRadio'
import VInput from '@/components/common/molecules/VInput'
export default {
  name: 'UserContactForm',
  components: { VRadio, VInput },
  props: {
    userContact: {
      type: Object,
      default: () => ({
        contactDiv: null,
        body: null
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    const { contactDiv, contactDivName, body, contactStatus } = this.userContact
    return {
      contactDiv,
      contactDivName,
      contactStatus,
      body
    }
  }
}
</script>
