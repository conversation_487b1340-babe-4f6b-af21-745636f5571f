<template>
  <div class="card">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.user_contact.store')"
      >
        <user-contact-form
          :user-contact="userContact"
        />
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.user_contact.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.save') }}
          </button>
        </div>
      </v-form>
    </ValidationObserver>
  </div>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import UserContactForm from '@/components/admin/user_contact/common/UserContactForm'
export default {
  name: 'UserContactCreate',
  components: { UserContactForm, VForm, ValidationObserver },
  props: {
    userContact: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>
