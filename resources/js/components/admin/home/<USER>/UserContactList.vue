<template>
  <div class="card">
    <b-table-simple
      hover
      striped
    >
      <b-thead align="center">
        <b-th colspan="7">
          {{ $t('models.home.screen_name.contact') }}
        </b-th>
      </b-thead>
      <b-tbody>
        <b-tr
          v-for="(data, index) in paginatedItems"
          :key="index"
          class="align-middle"
          @click="goToDetail"
        >
          <b-td>
            {{ data.id }}
          </b-td>
          <b-td align="center">
            <div
              v-if="data.contactDiv === 3"
              class="custom-contact-status"
            >
              通報
            </div>
          </b-td>
          <b-td>
            {{ data.createdAt }}
          </b-td>
          <b-td>
            {{ data.contactDivName }}
          </b-td>
          <b-td>
            {{ data.gender }}
          </b-td>
          <b-td>
            {{ data.memberId }}
          </b-td>
          <b-td>
            {{ data.nicknameUser }}
          </b-td>
        </b-tr>
        <b-tr v-if="userContacts.length === 0">
          <b-td
            align="center"
            colspan="5"
          >
            データが選択されていません。
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <div class="card-footer">
      <div class="level-left">
        {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
      </div>
      <b-pagination
        v-if="userContacts.length > perPage"
        v-model="currentPage"
        :total-rows="total"
        :per-page="perPage"
        @change="onPageChanged"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserContactList',
  props: {
    userContact: {
      type: Array,
      default: () => {}
    }
  },
  data () {
    return {
      userContacts: this.userContact,
      paginatedItems: this.userContact,
      perPage: 10,
      currentPage: 1
    }
  },
  computed: {
    total () {
      return this.userContacts.length
    },
    from () {
      if (this.userContacts.length === 0) {
        return 0
      } else {
        return (this.currentPage - 1) * this.perPage + 1
      }
    },
    to () {
      return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
    }
  },
  mounted () {
    this.paginate(this.perPage, 0)
  },
  methods: {
    paginate (pageSize, pageNumber) {
      let itemsToParse = this.userContacts
      this.paginatedItems = itemsToParse.slice(
        pageNumber * pageSize, (pageNumber + 1) * pageSize
      )
    },
    onPageChanged (page) {
      this.paginate(this.perPage, page - 1)
    },
    goToDetail () {
      location.href = this.route('admin.user_contact.index')
    }
  }
}
</script>

<style scoped>
  .custom-contact-status {
    background-color: #ff0000;
    color: #ffffff;
  }
</style>
