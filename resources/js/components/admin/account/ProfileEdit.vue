<template>
  <b-card border-variant="info">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.profile.update', id)"
        method="put"
      >
        <div class="card-body">
          <v-input
            v-model="loginId"
            :validation-filed="$t('models.account.field.login_id')"
            validation-rules="max:45"
            type="text"
            :label="$t('models.account.field.login_id')"
            name="login_id"
            maxlength="45"
          />
          <v-input
            v-model="email"
            :validation-filed="$t('models.account.field.email')"
            validation-rules="required|email"
            type="text"
            :label="$t('models.account.field.email')"
            name="email"
            maxlength="100"
          />
          <v-input
            v-model="accountName"
            :validation-filed="$t('models.account.field.account_name')"
            validation-rules="max:45"
            type="text"
            :label="$t('models.account.field.account_name')"
            name="account_name"
            maxlength="45"
          />
          <!-- change password-->
          <div class="row border-bottom mt-3">
            <label class="col-lg-2 col-4 col-form-label">{{ $t('models.account.field.password') }}</label>
            <div class="col-form-detail">
              <a
                class="btn btn-info ml-3 mb-3"
                :href="route('admin.profile.password.form', id)"
              >{{ $t('common.btn.change-password') }}</a>
            </div>
          </div>
        </div>
        <!-- /.card-body -->
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.account.show', id)"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.save') }}
          </button>
        </div>
      </v-form>
    </ValidationObserver>
  </b-card>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import VInput from '@/components/common/molecules/VInput'
export default {
  name: 'ProfileEdit',
  components: { VInput, VForm, ValidationObserver },
  props: {
    account: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    const { accountName, email, loginId, id } = this.account
    return {
      id,
      accountName,
      email,
      loginId
    }
  },
  methods: {
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>
