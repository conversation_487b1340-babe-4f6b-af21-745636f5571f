<template>
  <b-card border-variant="info">
    <ValidationObserver v-slot="{ failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.account.store')"
      >
        <account-form
          :account="account"
        />
        <div class="card-footer text-center">
          <a
            class="btn btn-default"
            :href="route('admin.account.index')"
          >
            {{ $t('common.btn.back') }}
          </a>
          <button
            type="button"
            :disabled="failed"
            class="btn btn-info"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.save') }}
          </button>
        </div>
      </v-form>
    </ValidationObserver>
  </b-card>
</template>

<script>
import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import AccountForm from '@/components/admin/account/common/AccountForm'

export default {
  name: 'AccountCreate',
  components: { AccountForm, VForm, ValidationObserver },
  props: {
    account: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    submitForm () {
      this.$refs['form'].$el.submit()
    }
  }
}
</script>

<style scoped>

</style>
