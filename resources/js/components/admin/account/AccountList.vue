<template>
  <div>
    <b-card
      class="box"
    >
      <div class="row">
        <div class="col-10">
          <b-form-input
            v-model="newKeyword"
            :placeholder="$t('common.keyword')"
          />
        </div>
        <div class="col-2">
          <button
            class="btn btn-info"
            @click="search"
          >
            {{ $t('common.btn.search') }}
          </button>
        </div>
      </div>
    </b-card>
    <b-card>
      <b-table
        striped
        hover
        :fields="fields"
        :items="accounts"
        :per-page="perPage"
        :current-page="currentPage"
        @row-clicked="goToDetail"
      >
        <template v-slot:cell(index)="data">
          {{ data.index + 1 + (currentPage-1) * perPage }}
        </template>
        <template v-slot:cell(actions)="row">
          <b-button
            size="sm"
            class="mr-1 btn btn-info"
            @click="goToEdit(row.item.id)"
          >
            {{ $t('common.btn.edit') }}
          </b-button>
          <b-button
            size="sm"
            class="mr-1 btn btn-danger"
            @click="deleteUser(row.item.id)"
          >
            {{ $t('common.btn.delete') }}
          </b-button>
        </template>
      </b-table>
      <div class="card-footer">
        <div>
          <div class="level-left">
            {{ $t('common.pagination_display', { total: total, first: from, last: to }) }}
          </div>
          <b-pagination
            v-model="currentPage"
            :total-rows="total"
            :per-page="perPage"
            aria-controls="chat"
          />
        </div>
      </div>
    </b-card>
  </div>
</template>

<script>
import axios from 'axios'
import { extractLaravelError } from '@/utils/helpers/promise'

export default {
  name: 'AccountList',
  props: {
    keyword: {
      type: String,
      default: null
    },
    account: {
      type: [Object, Array],
      default: () => {
      }
    }
  },
  data () {
    return {
      accounts: this.account || [],
      newKeyword: this.keyword,
      perPage: 5,
      currentPage: 1,
      fields: [
        {
          key: 'index',
          label: this.$t('models.account.field.index')
        },
        {
          key: 'loginId',
          label: this.$t('models.account.field.login_id')
        },
        {
          key: 'email',
          label: this.$t('models.account.field.email')
        },
        {
          key: 'accountName',
          label: this.$t('models.account.field.name')
        },
        {
          key: 'created_at',
          label: this.$t('models.account.field.created_at')
        },
        {
          key: 'actions',
          label: this.$t('common.action')
        }
      ]
    }
  },
  computed: {
    total: {
      get: function () {
        return this.accounts.length
      },
      set: function (newVal) {
      }
    },
    from: {
      get: function () {
        if (this.accounts.length === 0) {
            return (this.currentPage - 1) * this.perPage + 1
        } else {
          return (this.currentPage - 1) * this.perPage + 1
        }
      }
    },
    to: {
      get: function () {
        return (this.currentPage * this.perPage < this.total) ? this.currentPage * this.perPage : this.total
      }
    }
  },
  methods: {
    goToDetail (id) {
      location.href = this.route('admin.account.show', id)
    },
    goToEdit (id) {
      location.href = this.route('admin.profile.edit', id)
    },
    search () {
      location.href = this.route('admin.account.index') + '?keyword=' + this.newKeyword
    },
    deleteUser (userId) {
      this.$eventHub.$emit('open-delete-modal', async () => {
        try {
          await axios.delete(this.route('admin.account.delete', userId))
          location.href = this.route('admin.account.index')
        } catch (e) {
          this.$nextTick(() => {
            throw new Error(extractLaravelError(e))
          })
        }
      })
    }

  }
}
</script>

<style scoped>

</style>
