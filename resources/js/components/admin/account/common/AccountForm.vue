<template>
  <div class="card-body">
    <v-input
      v-model="loginId"
      :validation-filed="$t('models.account.field.login_id')"
      validation-rules="required|max:100"
      type="text"
      :label="$t('models.account.field.login_id')"
      :placeholder="$t('common.placeholder.username')"
      name="login_id"
      maxlength="100"
    />
    <v-input
      v-model="email"
      :validation-filed="$t('models.account.field.email')"
      validation-rules="required|email"
      type="text"
      :label="$t('models.account.field.email')"
      :placeholder="$t('common.placeholder.email')"
      name="email"
      maxlength="100"
    />
    <v-input
      v-if="!isEdit"
      v-model="password"
      :validation-filed="$t('models.account.field.password')"
      validation-rules="required|min:8|max:45|alpha_num"
      type="password"
      :label="$t('models.account.field.password')"
      :placeholder="$t('common.placeholder.password')"
      name="password"
      maxlength="45"
    />
    <v-input
      v-model="accountName"
      :validation-filed="$t('models.account.field.account_name')"
      validation-rules="required|max:100"
      type="text"
      :label="$t('models.account.field.account_name')"
      :placeholder="$t('common.placeholder.account_name')"
      name="account_name"
    />
  </div>
</template>

<script>

import VInput from '@/components/common/molecules/VInput'

export default {
  name: 'AccountForm',
  components: { VInput },
  props: {
    account: {
      type: Object,
      default: () => ({
        loginId: null,
        email: null,
        password: null,
        accountName: null
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    const { email, password, loginId, accountName } = this.account
    return {
      email,
      password,
      loginId,
      accountName
    }
  }
}
</script>

<style scoped>

</style>
