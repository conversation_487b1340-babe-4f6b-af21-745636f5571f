<template>
  <div class="card">
    <ValidationObserver v-slot="{failed, handleSubmit }">
      <v-form
        ref="form"
        class="form-horizontal"
        :action="route('admin.feedback_report.store' ,{user_id : userId,gender: gender})"
      >
        <report-feedback-form
          :user-report="userReport"
          :user-id="userId"
        />
        <div class="card-footer text-center">
          <b-button
            variant="outline-secondary"
            class="btn"
            @click="goToUserReport(userId)"
          >
            {{ $t('common.btn.back') }}
          </b-button>
          <button
            class="btn btn-outline-primary"
            :disabled="failed"
            @click="handleSubmit(submitForm)"
          >
            {{ $t('common.btn.feedback') }}
          </button>
        </div>
      </v-form>
      <v-modal
        title="お知ら作成"
        ok-text="送る"
        :handle-submitting="submitForm"
      >
        <p
          class="my-4"
        >
          お知ら作成?
        </p>
      </v-modal>
    </ValidationObserver>
  </div>
</template>

<script>

import { ValidationObserver } from 'vee-validate'
import VForm from '@/components/common/molecules/VForm'
import ReportFeedbackForm from '@/components/admin/feedback_report/common/FeedbackReportForm'
import VModal from '@/components/common/molecules/VModal'
import TabUserIndex from '@/constant/tab_user_index'

export default {
  name: 'ReportFeedbackCreate',
  components: { ReportFeedbackForm, VModal, VForm, ValidationObserver },
  props: {
    userReport: {
      type: [Object, Array],
      default: () => {
      }
    },
    userId: {
      type: Number,
      default: () => {
      }
    },
    gender: {
      type: Number,
      default: null
    }
  },
  data () {
    const { genderId } = this.userReport
    return {
      genderId
    }
  },
  methods: {
    openCommonModal () {
      // this.$bvModal.show('common-modal')
    },
    submitForm () {
      this.$refs['form'].$el.submit()
    },
    goToUserReport (id) {
      if (this.gender === 1) {
        location.href = this.route('admin.user.show', [id, TabUserIndex.MALE.TAB_REPORT])
      } else {
        location.href = this.route('admin.user.show', [id, TabUserIndex.FEMALE.TAB_REPORT])
      }
    }
  }
}
</script>

<style scoped>
  .custom-table-feedback th {
    text-align: center;
    width: 350px;
    vertical-align: middle;
  }

  .custom-table-feedback td {
    text-align: center;
    vertical-align: middle;
  }

  .custom-btn-send-now {
    width: 120px;
    text-align: center;
    vertical-align: middle;
  }
</style>
