<template>
  <b-card>
    <b-table-simple
      bordered
      small
    >
      <b-tbody class="custom-table-feedback">
        <!-- Reported Date -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            送信日
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ feedbackAt }}
          </b-td>
        </b-tr>

        <!-- Send ID -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            送信ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ feedbackId }}
          </b-td>
        </b-tr>

        <!-- Receive ID -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            受信ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ sendId }}
          </b-td>
        </b-tr>

        <!-- Reported Member ID-->
        <b-tr>
          <b-th
            class="text-center border"
          >
            通報した会員/ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            <b-button
              variant="link"
              @click="goToProfile(confirmedUserId)"
            >
              {{ nicknameConfirmedUser + ' / 会員ID: ' + confirmedMemberId }}
            </b-button>
          </b-td>
        </b-tr>
        <!-- Confirmed Member ID-->
        <b-tr>
          <b-th
            class="text-center border"
          >
            確認会員/ID
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            <b-button
              variant="link"
              @click="goToProfile(reportedUserId)"
            >
              {{ nicknameReportedUser + ' / 会員ID: ' + reportedMemberId }}
            </b-button>
          </b-td>
        </b-tr>

        <!-- Drinking Party group -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            飲み会グループ
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            <div>
              <!-- Male -->
              <b-row>
                <b-col>
                  男性：
                  <span
                    v-for="(userInfo, index) in memberIsMale"
                    :key="index"
                  >
                    <span style="padding-right: 20px">
                      {{ userInfo.nickname + ' 会員ID '+ userInfo.memberId }}
                    </span>
                  </span>
                </b-col>
              </b-row>

              <!-- Female -->
              <b-row>
                <b-col>
                  女性：
                  <span
                    v-for="(userInfo, index) in memberIsFemale"
                    :key="index"
                  >
                    <span style="padding-right: 20px">
                      {{ userInfo.nickname + ' 会員ID '+ userInfo.memberId }}
                    </span>
                  </span>
                </b-col>
              </b-row>
            </div>
          </b-td>
        </b-tr>
        <!-- Report Date and Drinking party date: time-->
        <b-tr>
          <b-th
            class="text-center border"
          >
            通報日/飲み会日：時間
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            <b-button
              variant="link"
            >
              <span
                style="padding-right: 20px"
              >
                通報日：{{ reportDate }}
              </span>
              <span
                style="padding-right: 20px"
              >
                飲み会日：{{ timePartyStart }}
              </span>
              <span
                style="padding-right: 20px"
              >
                飲み会時間 {{ partyTime }}分
              </span>
              <span
                style="padding-right: 20px"
              >
                {{ '/  該当時間 ' + ' ：'+ timeReport }}
              </span>
              <span>
                飲み会リンク
              </span>
            </b-button>
          </b-td>
        </b-tr>

        <!-- status -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            ステータス
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
            :style="{color : reportStatus === 1 ? 'red' : 'black'}"
          >
            {{ reportStatusName }}
          </b-td>
        </b-tr>

        <!-- Warning -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            警告
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ warningName }}
          </b-td>
        </b-tr>

        <!-- Report Date -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            お問合せ日付
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ reportDate }}
          </b-td>
        </b-tr>

        <!-- Report Item -->
        <b-tr>
          <b-th
            class="text-center border"
          >
            通報項目
          </b-th>
          <b-td
            class="text-center border"
            colspan="4"
          >
            {{ reportDivName }}
          </b-td>
        </b-tr>

        <!-- report content -->
        <b-tr>
          <b-td
            class="text-left border"
            colspan="7"
          >
            {{ reportContent }}
          </b-td>
        </b-tr>

        <!-- feedback content -->
        <b-tr>
          <b-td
            class="text-left border"
            colspan="7"
          >
            {{ feedbackContent }}
          </b-td>
        </b-tr>
      </b-tbody>
    </b-table-simple>
    <!-- Card Footer Button back -->
    <div class="card-footer text-center">
      <b-button
        variant="outline-secondary"
        class="btn"
        @click="goToUserReportList(userId)"
      >
        {{ $t('common.btn.back') }}
      </b-button>
    </div>
  </b-card>
</template>

<script>
import TabUserIndex from '@/constant/tab_user_index'
export default {
  name: 'FeedbackReportDetail',
  props: {
    feedback: {
      type: [Object, Array],
      default: () => ({
        title: null,
        body: null,
        notificationDiv: null
      })
    },
    userId: {
      type: [String, Number],
      default: null
    },
    gender: {
      type: Number,
      default: null
    }
  },
  data () {
    const {
      sendId, feedbackId, sentAt, reportedMemberId, confirmedMemberId, nicknameReportedUser, nicknameConfirmedUser, reportContent, feedbackContent,
      reportedUserId, confirmedUserId, memberIsMale, memberIsFemale, reportDate, timePartyStart, partyTime,
      timeReport, reportStatusName, reportStatus, reportDivName, warningName, feedbackAt, genderId
    } = this.feedback
    return {
      reportedMemberId,
      confirmedMemberId,
      nicknameReportedUser,
      nicknameConfirmedUser,
      reportedUserId,
      confirmedUserId,
      memberIsMale,
      memberIsFemale,
      reportDate,
      timePartyStart,
      partyTime,
      timeReport,
      reportStatusName,
      reportStatus,
      reportDivName,
      sendId,
      feedbackId,
      reportContent,
      feedbackContent,
      sentAt,
      warningName,
      feedbackAt,
      genderId
    }
  },
  methods: {
    /**
             * goToUserReportList
             *
             * @param userId
             */
    goToUserReportList (userId) {
      if (this.gender === 1) {
        location.href = this.route('admin.user.show', [userId, TabUserIndex.MALE.TAB_REPORT])
      } else {
        location.href = this.route('admin.user.show', [userId, TabUserIndex.FEMALE.TAB_REPORT])
      }
    },
    /**
       * goToProfile
       *
       * @param id
       */
    goToProfile (id) {
      location.href = this.route('admin.user.show', [id, 0])
    }
  }
}
</script>

<style scoped>
  .custom-table-feedback th {
    text-align: center;
    width: 350px;
    vertical-align: middle;
  }

  .custom-table-feedback td {
    text-align: center;
    vertical-align: middle;
  }

  .custom-btn-send-now {
    width: 120px;
    text-align: center;
    vertical-align: middle;
  }
</style>
