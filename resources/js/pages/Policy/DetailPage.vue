<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { useI18n } from "@/composables/useI18n.ts";
import { PencilSquareIcon } from "@heroicons/vue/24/solid";
import ButtonLink from "@/components/common/shared/ButtonLink.vue";
import { route } from "ziggy-js";
import { DetailPrivacyProps } from "@/types/privacy.ts";

defineProps<DetailPrivacyProps>();
const { t } = useI18n();
</script>

<template>
  <Head :title="t('models/privacy.title')"/>
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/privacy.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div>
      <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
        <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div class="w-full">
            <div class="grid grid-cols-1 gap-4 lg:gap-7">
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">{{ t('models/privacy.field.title') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ privacy.title }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">{{ t('models/privacy.field.body') }}</p>
                <div class="text-sm font-medium text-gray-800 whitespace-pre-line">
                  <div v-html="privacy.body"></div>
                </div>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">{{ t('models/privacy.field.isPublic') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ privacy.isPublic ? 'Yes' : 'No' }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">{{ t('common.createdAt') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ privacy.createdAt }}</p>
              </div>
              <div>
                <p class="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">{{ t('common.updatedAt') }}</p>
                <p class="text-sm font-medium text-gray-800">{{ privacy.updatedAt }}</p>
              </div>
            </div>
          </div>
          <ButtonLink :href="route('admin.privacy.edit', privacy.id)" class="edit-button">
            <PencilSquareIcon class="w-5 h-5"/>
            {{ t('common.btn.edit') }}
          </ButtonLink>
        </div>
      </div>
      <div class="flex justify-end">
        <ButtonLink size="sm" variant="outline" :href="route('admin.privacy.index')">{{ t('common.btn.back') }}</ButtonLink>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
