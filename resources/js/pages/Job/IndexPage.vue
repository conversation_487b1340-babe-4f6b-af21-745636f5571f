<script setup lang="ts">
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import {
  EyeIcon,
  PencilSquareIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  ChevronUpIcon,
  ChevronDownIcon,
} from '@heroicons/vue/24/solid';
import VPagination from '@/components/common/shared/VPagination.vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import Button from '@/components/common/shared/Button.vue';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';
import { route } from 'ziggy-js';
import { useI18n } from '@/composables/useI18n.ts';
import { JobType, ListJobProps } from '@/types/job.ts';
import { computed, ref } from 'vue';
import ConfirmModal from '@/components/common/shared/ConfirmModal.vue';
import JobDetailPreview from '@/pages/Job/common/JobDetailPreview.vue';
import DatePicker from '@/components/common/shared/DatePicker.vue';

defineProps<ListJobProps>();

const { t } = useI18n();
const isModalDelete = ref(false);
const isModalDuplicate = ref(false);
const selectedJob = ref<JobType | null>(null);
const isSearchVisible = ref(true);

function toggleSearchForm() {
  isSearchVisible.value = !isSearchVisible.value;
}

const searchForm = useForm({
  title: '',
  prefecture: '',
  time_start: '',
  time_end: '',
  is_filled: '',
});
const prefectureOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  ...(window as any).prefectureOptions,
]);
const isFilledOptions = computed(() => [
  { label: t('common.field.all'), value: '' },
  { label: t('models/job.is_filled.not_ended'), value: 0 },
  { label: t('models/job.is_filled.ended'), value: 1 },
]);

function submitSearch() {
  searchForm.get(route('admin.job.index'), {
    preserveState: true,
    preserveScroll: true,
  });
}

function resetSearch() {
  router.get(route('admin.job.index'));
}

function openModalDelete(job: JobType) {
  selectedJob.value = job;
  isModalDelete.value = true;
}

function deleteJob() {
  if (confirm(t('common.confirm.delete'))) {
    isModalDelete.value = false;
    router.delete(route('admin.job.delete', selectedJob.value?.id));
  }
}

function openModalDuplicate(job: JobType) {
  selectedJob.value = job;
  isModalDuplicate.value = true;
}

function duplicateJob() {
  isModalDuplicate.value = false;
  router.post(route('admin.job.duplicate', selectedJob.value?.id));
}
</script>

<template>
  <Head title="Jobs" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.index') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-5 mb-6 border border-gray-200 rounded-2xl lg:p-6">
      <div class="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div class="w-full">
          <div class="flex items-center justify-between mb-6">
            <h4 class="text-lg font-semibold text-gray-800">
              {{ t('common.field.search') }}
            </h4>
            <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleSearchForm">
              <ChevronUpIcon v-if="isSearchVisible" class="w-5 h-5" />
              <ChevronDownIcon v-else class="w-5 h-5" />
            </Button>
          </div>
          <form @submit.prevent="submitSearch" v-show="isSearchVisible" class="transition-all duration-300">
            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-7">
              <v-input class="col-span-2" :label="t('models/job.field.title')" v-model="searchForm.title" />
              <div class="col-span-1">
                <label class="mb-1.5 block text-sm font-medium text-gray-700">{{
                  t('models/job.searchField.time')
                }}</label>
                <div class="grid grid-cols-2 gap-4 items-center">
                  <DatePicker type="time" v-model="searchForm.time_start" />
                  <DatePicker type="time" v-model="searchForm.time_end" />
                </div>
              </div>
              <v-select
                :label="t('models/job.field.prefecture')"
                v-model="searchForm.prefecture"
                :options="prefectureOptions"
              />
              <v-select
                :label="t('models/job.field.is_filled')"
                v-model="searchForm.is_filled"
                :options="isFilledOptions"
              />
            </div>
            <div class="flex items-center justify-center gap-2 mt-6">
              <Button size="sm" variant="outline" @click="resetSearch">
                {{ t('common.btn.reset') }}
              </Button>
              <Button size="sm" variant="primary" type="submit">
                {{ t('common.btn.search') }}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <div class="overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="flex items-center justify-end mb-6">
          <ButtonLink size="sm" variant="primary" :href="route('admin.job.create')">
            {{ t('common.btn.create') }}
          </ButtonLink>
        </div>
        <div class="overflow-hidden border rounded-lg">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.id') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.title') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.prefecture') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.job_time') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.type') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.quantity') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('models/job.field.is_filled') }}
                </th>
                <th scope="col" class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                  {{ t('common.field.action') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="job in jobs.data" :key="job.id" class="border-t border-gray-100">
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ job.id }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ job.title }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ job.prefecture }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">
                    {{ `${job.timeStart} - ${job.timeEnd}` }}
                  </span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ job.typeName }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{ `${job.countApplied}/${job.quantity}` }}</span>
                </td>
                <td class="border-t">
                  <span class="px-5 py-4 sm:px-6">{{
                    job.isFilled ? t('models/job.is_filled.ended') : t('models/job.is_filled.not_ended')
                  }}</span>
                </td>
                <td class="px-5 py-4 sm:px-6">
                  <div class="flex text-center">
                    <DocumentDuplicateIcon class="size-5 mr-2 cursor-pointer" @click="openModalDuplicate(job)" />
                    <Link :href="route('admin.job.show', job.id)"><eye-icon class="size-5 mr-2" /></Link>
                    <Link :href="route('admin.job.edit', job.id)"><pencil-square-icon class="size-5 mr-2" /></Link>
                    <trash-icon class="size-5 mr-2 text-red-600 cursor-pointer" @click="openModalDelete(job)" />
                  </div>
                </td>
              </tr>
            </tbody>
            <tr v-if="jobs.data.length === 0">
              <td class="px-6 py-4 border-t" colspan="6">{{ t('common.noResult') }}</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="mt-2" v-if="jobs.data.length > 0">
        <v-pagination :paginator="jobs.paginator" />
      </div>
    </div>
  </div>
  <ConfirmModal
    v-model="isModalDelete"
    :title="t('common.delete')"
    size="md"
    @close="isModalDelete = false"
    :saveButtonText="t('common.btn.delete')"
    saveButtonVariant="danger"
    @save="deleteJob()"
  >
    <JobDetailPreview :job="selectedJob" />
  </ConfirmModal>

  <ConfirmModal
    v-model="isModalDuplicate"
    :title="t('common.duplicate')"
    size="lg"
    @close="isModalDuplicate = false"
    :saveButtonText="t('common.btn.save')"
    saveButtonVariant="primary"
    @save="duplicateJob()"
  >
    <JobDetailPreview :job="selectedJob" />
  </ConfirmModal>
</template>

<style scoped></style>
