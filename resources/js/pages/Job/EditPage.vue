<script setup lang="ts">
import { EditJobProps, JobFormType } from '@/types/job.ts';
import { useI18n } from '@/composables/useI18n.ts';
import { ref } from 'vue';
import { Head, InertiaForm, useForm } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import JobForm from '@/pages/Job/common/JobForm.vue';
import Modal from '@/components/common/Modal.vue';
import { useValidation } from '@/composables/useValidation';

const props = defineProps<EditJobProps>();

const { t } = useI18n();
const deletedImageIds = ref<number[]>([]);

const {
  form: formData,
  isPreview: isPreviewEdit,
  validateBeforePreview,
  setPreview,
} = useValidation<JobFormType>({
  initialValues: {
    recruitment_type: props.job.recruitmentType,
    employer_email: props.job.employerEmail,
    employer_name: props.job.employerName,
    employer_phone_number: props.job.employerPhoneNumber,
    category_id: props.job.categoryId,
    type: props.job.type,
    is_public: props.job.isPublic,
    is_instant: props.job.isInstant,
    thumbnail: null,
    images: [],
    title: props.job.title,
    description: props.job.description,
    benefits: props.job.benefits || '',
    time_start: props.job.timeStart || '',
    time_end: props.job.timeEnd || '',
    age: props.job.age,
    gender: props.job.gender,
    quantity: props.job.quantity,
    certificate_level: props.job.certificateLevel,
    prefecture: props.job.prefecture,
    address: props.job.address,
    salary_type: props.job.salaryType,
    salary: props.job.salary,
    travel_fee_type: props.job.travelFeeType,
    travel_fee: props.job.travelFee,
    expired_at: props.job.expiredAt,
    job_start_at: props.job.jobStartAt,
    // Image management fields
    deleted_image_ids: [],
    existing_images: props.job.images,
  },
  labels: {
    recruitment_type: t('models/job.field.recruitment_type'),
    thumbnail: t('models/job.field.thumbnail'),
    images: t('models/job.field.images'),
    category_id: t('models/job.field.category'),
    employer_email: t('models/job.field.employer_email'),
    employer_name: t('models/job.field.employer_name'),
    employer_phone_number: t('models/job.field.employer_phone_number'),
    type: t('models/job.field.type'),
    prefecture: t('models/job.field.prefecture'),
    address: t('models/job.field.address'),
    title: t('models/job.field.title'),
    description: t('models/job.field.description'),
    benefits: t('models/job.field.benefits'),
    certificate_level: t('models/job.field.certificate_level'),
    time_start: t('models/job.field.time_start'),
    time_end: t('models/job.field.time_end'),
    salary_type: t('models/job.field.salary_type'),
    salary: t('models/job.field.salary'),
    travel_fee_type: t('models/job.field.travel_fee_type'),
    travel_fee: t('models/job.field.travel_fee'),
    age: t('models/job.field.age'),
    gender: t('models/job.field.gender'),
    quantity: t('models/job.field.quantity'),
    expired_at: t('models/job.field.expired_at'),
    job_start_at: t('models/job.field.job_start_at'),
  },
});

const handleImageDelete = (imageId: number) => {
  if (!deletedImageIds.value.includes(imageId)) {
    deletedImageIds.value.push(imageId);
    formData.deleted_image_ids = deletedImageIds.value;
  }
};

const handlePreview = () => {
  validateBeforePreview({
    recruitment_type: ['required'],
    employer_email: ['required', 'email'],
    employer_name: ['required'],
    employer_phone_number: ['required'],
    category_id: ['required'],
    type: ['required'],
    prefecture: ['required'],
    address: ['required'],
    title: ['required'],
    description: ['required'],
    benefits: ['optional'],
    certificate_level: ['required'],
    time_start: ['optional'],
    time_end: ['optional'],
    salary_type: ['required'],
    salary: ['optional'],
    travel_fee_type: ['required'],
    travel_fee: ['optional'],
    age: ['optional'],
    gender: ['optional'],
    quantity: ['required'],
    expired_at: ['required'],
    job_start_at: ['required'],
  });
};

const update = (form: InertiaForm<JobFormType>) => {
  console.log(form);
  setPreview(false);
  // form.deleted_image_ids = deletedImageIds.value;

  form.put(route('admin.job.update', props.job.id), {
    onSuccess: () => {
      deletedImageIds.value = [];
    },
  });
};
</script>

<template>
  <Head :title="t('models/job.screenName.edit')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job.screenName.edit') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <JobForm
      :form-data="formData"
      :categories="props.categories"
      :is-edit="true"
      :existing-thumbnail="props.job.thumbnailUrl"
      :existing-images="props.job.images"
      :deleted-image-ids="deletedImageIds"
      @deleteImage="handleImageDelete"
      @openPreview="handlePreview"
    />
  </div>

  <Modal v-if="isPreviewEdit" @close="setPreview(false)" :title="t('models/job.screenName.edit')">
    <template #body>
      <JobForm
        :form-data="formData"
        :categories="props.categories"
        :is-edit="true"
        :existing-thumbnail="props.job.thumbnailUrl"
        :existing-images="props.job.images"
        :deleted-image-ids="deletedImageIds"
        @deleteImage="handleImageDelete"
        @submit="update"
        @openPreview="handlePreview"
        @close="setPreview(false)"
        :is-preview="true"
      />
    </template>
  </Modal>
</template>

<style scoped></style>
