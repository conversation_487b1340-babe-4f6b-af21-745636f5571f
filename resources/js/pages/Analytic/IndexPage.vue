<script setup lang="ts">
import { Head, usePage } from '@inertiajs/vue3';
import { useI18n } from "@/composables/useI18n";
import FullCalendar from '@fullcalendar/vue3';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { ref, reactive } from "vue";
import type { CalendarOptions, EventInput, DateSelectArg, EventClickArg, EventContentArg, EventApi } from '@fullcalendar/core';
import allLocales from "@fullcalendar/core/locales-all";
import { useLanguage } from "@/composables/useLanguage.ts";
import OnlineUsersCounter from '@/components/OnlineUsersCounter.vue';

const { currentLanguage } = useLanguage();
const { t } = useI18n();
const page = usePage();

// Get initial online users data from props
const onlineUsersData = ref(page.props.onlineUsersData || { total: 0, timestamp: new Date().toISOString() });

const calendarRef = ref<InstanceType<typeof FullCalendar> | null>(null);
const selectedEvent = ref<EventApi | null>(null);
const events = ref<EventInput[]>([
  {
    id: '1',
    title: 'Job 1',
    start: new Date().toISOString().split('T')[0],
    extendedProps: { calendar: 'Success' }
  }
]);

const listLocales = {
  EN: 'en',
  VI: 'vn',
  JP: 'ja',
  ID: 'id',
  MM: 'en',
  NE: 'ne'
};

const locale = listLocales[currentLanguage.value as keyof typeof listLocales] || 'ja';

const handleDateSelect = (selectInfo: DateSelectArg) => {
  console.log('selectInfo', selectInfo);
};
const handleEventClick = (clickInfo: EventClickArg) => {
  const event = clickInfo.event;
  console.log('event', event);
  selectedEvent.value = event;
};
const renderEventContent = (eventInfo: EventContentArg) => {
  const colorClass = `fc-bg-${eventInfo.event.extendedProps.calendar.toLowerCase()}`;
  return {
    html: `
      <div class="event-fc-color flex fc-event-main ${colorClass} p-1 rounded-sm">
        <div class="fc-daygrid-event-dot"></div>
        <div class="fc-event-time">${eventInfo.timeText}</div>
        <div class="fc-event-title">${eventInfo.event.title}</div>
      </div>
    `
  };
};

const calendarOptions = reactive<CalendarOptions>({
  plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin],
  initialView: 'dayGridMonth',
  headerToolbar: {
    left: 'prev,next',
    center: 'title',
    right: 'dayGridMonth,listMonth'
  },
  locales: allLocales,
  locale: locale,
  events: events.value,
  selectable: true,
  select: handleDateSelect,
  eventClick: handleEventClick,
  eventContent: renderEventContent
});
</script>

<template>
  <Head title="Dashboard"/>
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/dashboard.screenName.index') }}
    </h2>
  </div>

  <!-- Online Users Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
    <OnlineUsersCounter :initial-data="onlineUsersData" />
  </div>

  <!-- Calendar -->
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="rounded-2xl border border-gray-200 bg-white">
      <div class="custom-calendar">
        <FullCalendar ref="calendarRef" class="min-h-screen" :options="calendarOptions" />
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
