import { useI18n } from '@/composables/useI18n';
import {
  between,
  confirmed,
  digits,
  email,
  integer,
  length,
  max,
  max_value,
  min,
  min_value,
  not_one_of,
  numeric,
  one_of,
  regex,
  required,
} from '@vee-validate/rules';
import { useForm as useInertiaForm } from '@inertiajs/vue3';
import { ref, watch } from 'vue';
import { useUnsavedChanges } from '@/composables/useUnsavedChanges';

export type ValidationRule = {
  rule: string;
  message?: string;
};

export type ValidationRules = Record<string, (string | ValidationRule)[]>;

export type ValidationLabels = Record<string, string>;

export type UseValidationOptions<T> = {
  initialValues: T;
  labels?: ValidationLabels;
  enableUnsavedChanges?: boolean;
  unsavedChangesOptions?: {
    confirmMessage?: string;
    excludeFields?: string[];
  };
};

// Overloaded function signatures for backward compatibility
export function useValidation<T extends Record<string, any>>(initialValues: T): ReturnType<typeof createValidation<T>>;
export function useValidation<T extends Record<string, any>>(
  options: UseValidationOptions<T>,
): ReturnType<typeof createValidation<T>>;
export function useValidation<T extends Record<string, any>>(
  initialValuesOrOptions: T | UseValidationOptions<T>,
): ReturnType<typeof createValidation<T>> {
  // Handle both old and new function signatures
  const isOptionsObject =
    initialValuesOrOptions && typeof initialValuesOrOptions === 'object' && 'initialValues' in initialValuesOrOptions;

  if (isOptionsObject) {
    const options = initialValuesOrOptions as UseValidationOptions<T>;
    return createValidation(options.initialValues, options.labels, options.enableUnsavedChanges, options.unsavedChangesOptions);
  } else {
    return createValidation(initialValuesOrOptions as T);
  }
}

function createValidation<T extends Record<string, any>>(
  initialValues: T,
  labels?: ValidationLabels,
  enableUnsavedChanges = true,
  unsavedChangesOptions?: {
    confirmMessage?: string;
    excludeFields?: string[];
  }
) {
  const { t } = useI18n();
  const form = useInertiaForm<T>(initialValues);
  const isPreview = ref(false);

  // Setup unsaved changes tracking
  const unsavedChanges = enableUnsavedChanges
    ? useUnsavedChanges(form, unsavedChangesOptions)
    : null;

  // Watch form data changes to update dirty state
  if (unsavedChanges) {
    watch(
      () => form.data(),
      () => {
        unsavedChanges.updateDirtyState();
      },
      { deep: true }
    );
  }

  const parseRule = (ruleStr: string) => {
    const parts = ruleStr.split(':');
    const name = parts[0];
    const params = parts.length > 1 ? parts[1].split(',') : [];
    return { name, params };
  };

  const processRule = (rule: string | ValidationRule) => {
    if (typeof rule === 'string') {
      return { ruleStr: rule, customMessage: undefined };
    } else {
      return { ruleStr: rule.rule, customMessage: rule.message };
    }
  };

  const validateField = (field: string, value: any, rules: (string | ValidationRule)[]) => {
    form.clearErrors(field);

    const getFieldLabel = (fieldName: string) => {
      if (labels && labels[fieldName]) {
        return labels[fieldName];
      }

      return fieldName;
    };

    const fieldLabel = getFieldLabel(field);

    for (const rule of rules) {
      const { ruleStr, customMessage } = processRule(rule);
      const { name, params } = parseRule(ruleStr);
      let isValid = true;
      let errorMessage = '';

      switch (name) {
        case 'required':
          isValid = required(value);
          errorMessage = t('validation.required', { attribute: fieldLabel });
          break;

        case 'email':
          isValid = email(value);
          errorMessage = t('validation.email', { attribute: fieldLabel });
          break;

        case 'same':
          const otherField = params[0];
          isValid = value === form.data()[otherField];
          errorMessage = t('validation.same', {
            attribute: fieldLabel,
            other: getFieldLabel(otherField),
          });
          break;

        case 'min':
          const minLength = parseInt(params[0]);
          isValid = min(value, [minLength]);
          errorMessage = t('validation.min.string', {
            attribute: fieldLabel,
            min: minLength,
          });
          break;

        case 'max':
          const maxLength = parseInt(params[0]);
          isValid = max(value, [maxLength]);
          errorMessage = t('validation.max.string', {
            attribute: fieldLabel,
            max: maxLength,
          });
          break;

        case 'between':
          const minValue = parseInt(params[0]);
          const maxValue = parseInt(params[1]);
          isValid = between(value, [minValue, maxValue]);
          errorMessage = t('validation.between.string', {
            attribute: fieldLabel,
            min: minValue,
            max: maxValue,
          });
          break;

        case 'numeric':
          isValid = numeric(value);
          errorMessage = t('validation.numeric', { attribute: fieldLabel });
          break;

        case 'integer':
          isValid = integer(value);
          errorMessage = t('validation.integer', { attribute: fieldLabel });
          break;

        case 'confirmed':
          const confirmField = `${field}_confirmation`;
          isValid = confirmed(value, form.data()[confirmField]);
          errorMessage = t('validation.confirmed', { attribute: fieldLabel });
          break;

        case 'digits':
          const digitsCount = parseInt(params[0]);
          isValid = digits(value, [digitsCount]);
          errorMessage = t('validation.digits', {
            attribute: fieldLabel,
            digits: digitsCount,
          });
          break;

        case 'regex':
          const pattern = params[0];
          isValid = regex(value, { regex: pattern });
          errorMessage = t('validation.regex', { attribute: fieldLabel });
          break;

        case 'in':
          const allowedValues = params;
          isValid = one_of(value, allowedValues);
          errorMessage = t('validation.in', { attribute: fieldLabel });
          break;

        case 'not_in':
          const disallowedValues = params;
          isValid = not_one_of(value, disallowedValues);
          errorMessage = t('validation.not_in', { attribute: fieldLabel });
          break;

        case 'min_value':
          const minVal = parseFloat(params[0]);
          isValid = min_value(value, [minVal]);
          errorMessage = t('validation.min.numeric', {
            attribute: fieldLabel,
            min: minVal,
          });
          break;

        case 'max_value':
          const maxVal = parseFloat(params[0]);
          isValid = max_value(value, [maxVal]);
          errorMessage = t('validation.max.numeric', {
            attribute: fieldLabel,
            max: maxVal,
          });
          break;

        case 'length':
          const exactLength = parseInt(params[0]);
          isValid = length(value, [exactLength]);
          errorMessage = t('validation.size.string', {
            attribute: fieldLabel,
            size: exactLength,
          });
          break;
      }

      if (!isValid) {
        const finalErrorMessage = customMessage || errorMessage;
        form.setError(field, finalErrorMessage);
        return false;
      }
    }

    return true;
  };

  const validateForm = (validationRules: ValidationRules) => {
    form.clearErrors();

    let isValid = true;
    const formData = form.data();

    for (const [field, rules] of Object.entries(validationRules)) {
      const isFieldValid = validateField(field, formData[field], rules);
      if (!isFieldValid) {
        isValid = false;
      }
    }

    return isValid;
  };

  const validateBeforePreview = (validationRules: ValidationRules) => {
    const isValid = validateForm(validationRules);

    if (isValid) {
      isPreview.value = true;
    }

    return isValid;
  };

  const setPreview = (value: boolean) => {
    isPreview.value = value;
  };

  return {
    form,
    isPreview,
    validateField,
    validateForm,
    validateBeforePreview,
    setPreview,
  };
}
