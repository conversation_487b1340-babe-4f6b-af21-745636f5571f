import Vue from 'vue'
import { extend } from 'vee-validate'
import * as rules from 'vee-validate/dist/rules'
import { configure } from 'vee-validate'
import VueI18n from "vue-i18n";
import ja from "vee-validate/dist/locale/ja.json";

Vue.use(VueI18n)

const i18n = new VueI18n({
  locale: "ja",
  messages: {
    ja: {
      validation: ja.messages
    }
  }
})

Object.keys(rules).forEach(rule => {
  extend(rule, rules[rule])
})

configure({
  defaultMessage: (field, values) => {
    // values._field_ = i18n.t(`fields.${field}`);
    return i18n.t(`validation.${values._rule_}`, values);
  },
  classes: {
    invalid: 'help is-danger',
  }
})
