import { Paginator } from "@/types/paginator.ts";
import { JobCategoryType } from "@/types/job.ts";

export type ListUserProps = {
    author: object;
    users: {
        data: UserType[];
        paginator: Paginator
    };
    categories: JobCategoryType
}

export interface DetailUserProps {
    user: UserType;
}
type UserType = {
    id: number;
    code: string;
    name: string;
    email: string;
    avatarUrl: string;
    healthCertificateUrl: string;
    userStatus: string;
    userStatusName: string;
    nameKana: string;
    nameKanji: string;
    phoneNumber: string;
    gender: string;
    genderName?: string;
    birthday: string;
    nationality: string;
    nationalityName: string;
    hasCertificate: boolean;
    japaneseLevel: string;
    japaneseLevelName?: string;
    arrivalDate: string;
    zipCode: string;
    prefecture: string;
    streetAddress: string;
    townAddress: string;
    trainStationName: string;
    emergencyName: string;
    emergencyRelation: string;
    emergencyRelationName: string;
    emergencyPhoneNumber: string;
    emailVerificationAt: Date;
    identificationAt: Date;
    isDisable: boolean;
    bankType: string;
    bankTypeName: string;
    bankName?: string;
    bankBranch?: string;
    depositType?: string;
    accountName?: string;
    accountNumber?: string;
    frontCardUrl: string;
    backCardUrl: string;
    periodType: string;
    schoolName?: string;
    identificationUrl: string;
    periodOfStay: string;
    periodExpireAt: Date;
    passportUrl: string;
    passportNumber: string;
    passportExpiredAt: Date;
    jobParticipationCount: number
}

export type UserBaseFormType = {
    name: string,
    name_kana: string,
    name_kanji: string,
    phone_number: string,
    gender: string,
    birthday: string,
    nationality: string,
    has_certificate: boolean,
    japanese_level: string,
    arrival_date: string,
    zip_code: string,
    prefecture: string,
    street_address: string,
    town_address: string,
    train_station_name: string,
    emergency_name: string,
    emergency_relation: string,
    emergency_phone_number: string
}

export type UserBankFormType = {
    bank_type: string;
    atm_image?: File;
    bank_name?: string;
    bank_branch?: string;
    account_name?: string;
    account_number?: string;
    deposit_type?: string;
}

export type UserFormType = UserBaseFormType & UserBankFormType & {
    email: string;
    password?: string;
    password_confirmation?: string;
    avatar?: File;
    health_certificate?: File;
    front_card?: File;
    back_card?: File;
    identification?: File;
    period_type: string;
    school_name?: string;
    period_of_stay: string;
    period_expire_at: string;
    passport_image?: File;
    passport_number: string;
    passport_expired_at: string;
};
