import Vue from 'vue'
import EventHub from '@/plugins/EventHub'
import VueI18n from 'vue-i18n'
import '@/validate'
import { csrfToken } from '@/bootstrap'
import Locales from '@/i18n/vue-i18n-locales.generated'
import { Ziggy } from '@/ssr-route/ziggy'
import route from '@/../../vendor/tightenco/ziggy/dist/js/route'
import { BootstrapVue, IconsPlugin } from 'bootstrap-vue'
import { Settings } from 'luxon'
import VueChatScroll from 'vue-chat-scroll/src/vue-chat-scroll'

Settings.defaultLocale = 'ja'

require('./bootstrap')

window.Vue = Vue

Vue.use(EventHub)
Vue.use(VueI18n)
// Install BootstrapVue
Vue.use(BootstrapVue)
// Optionally install the BootstrapVue icon components plugin
Vue.use(IconsPlugin)
Vue.use(VueChatScroll)

const messages = Locales
const i18n = new VueI18n({
  locale: 'ja',
  fallbackLocale: 'ja',
  messages
})

Vue.config.errorHandler = (err, vm, info) => {
  console.error('errorHandler err:', err)
  console.error('errorHandler vm:', vm)
  console.error('errorHandler info:', info)
}

Vue.mixin({
  methods: {
    route: (name, params, absolute) => route(name, params, absolute, Ziggy)
  }
})

Vue.component('message-detail', require('@/components/admin/user/detail/message/MessageDetail').default)

Vue.component('message-boundary', require('@/components/common/molecules/MessageBoundary').default)
Vue.component('error-boundary', require('@/components/common/molecules/ErrorBoundary').default)
Vue.component('global-delete-modal', require('@/components/common/molecules/GlobalDeleteModal').default)

//= ======================== Admin ================================
// Home Top
Vue.component('top-list', require('@/components/admin/home/<USER>').default)

// Profile
Vue.component('profile-detail', require('@/components/admin/account/ProfileDetail').default)
Vue.component('profile-edit', require('@/components/admin/account/ProfileEdit').default)
Vue.component('change-password', require('@/components/admin/account/ChangePassword').default)

// Auth
Vue.component('login-form', require('@/components/admin/auth/LoginForm').default)

// Account
Vue.component('account-list', require('@/components/admin/account/AccountList').default)
Vue.component('account-create', require('@/components/admin/account/AccountCreate').default)

// User
Vue.component('user-list', require('@/components/admin/user/UserList').default)
Vue.component('user-create', require('@/components/admin/user/UserCreate').default)
Vue.component('user-detail', require('@/components/admin/user/UserDetail').default)
Vue.component('user-edit', require('@/components/admin/user/UserEdit').default)

// User Detail
Vue.component('detail-user-profile', require('@/components/admin/user/detail/DetailUserProfile').default)
Vue.component('detail-user-friend', require('@/components/admin/user/detail/DetailUserFriend').default)
Vue.component('detail-user-friend-message', require('@/components/admin/user/detail/DetailUserFriendMessage').default)
Vue.component('detail-user-ticket', require('@/components/admin/user/detail/DetailUserTicket').default)
Vue.component('detail-user-buy-point', require('@/components/admin/user/detail/DetailUserBuyPoint').default)
Vue.component('detail-user-present-point', require('@/components/admin/user/detail/DetailUserPresentPoint').default)
Vue.component('detail-user-receive-point', require('@/components/admin/user/detail/DetailUserReceivePoint').default)
Vue.component('detail-user-block', require('@/components/admin/user/detail/DetailUserBlock').default)
Vue.component('detail-user-report', require('@/components/admin/user/detail/DetailUserReport').default)
Vue.component('detail-user-contact', require('@/components/admin/user/detail/DetailUserContact').default)

// Report Detail
Vue.component('detail-user-report-show', require('@/components/admin/user/detail/report/UserReportDetail').default)

// Contact Detail
Vue.component('detail-user-contact-show', require('@/components/admin/user/detail/contact/UserContactDetail').default)

// Report Feedback
Vue.component('feedback-report-create', require('@/components/admin/feedback_report/FeedbackReportCreate').default)
Vue.component('feedback-report-detail', require('@/components/admin/feedback_report/FeedbackReportDetail').default)

// Contact Feedback
Vue.component('feedback-contact-create', require('@/components/admin/feedback_contact/FeedbackContactCreate').default)
Vue.component('feedback-contact-detail', require('@/components/admin/feedback_contact/FeedbackContactDetail').default)

// SendNotification
Vue.component('notification-list', require('@/components/admin/notification/NotificationList').default)
Vue.component('notification-create', require('@/components/admin/notification/NotificationCreate').default)
Vue.component('notification-detail', require('@/components/admin/notification/NotificationDetail').default)

// Party
Vue.component('party-list', require('@/components/admin/party/PartyList').default)

// User Report
Vue.component('user-report-list', require('@/components/admin/user_report/UserReportList').default)
Vue.component('user-report-detail', require('@/components/admin/user_report/UserReportDetail').default)
Vue.component('user-report-edit', require('@/components/admin/user_report/UserReportEdit').default)

// User Contact
Vue.component('user-contact-list', require('@/components/admin/user_contact/UserContactList').default)
Vue.component('user-contact-detail', require('@/components/admin/user_contact/UserContactDetail').default)
Vue.component('user-contact-create', require('@/components/admin/user_contact/UserContactCreate').default)
Vue.component('user-contact-edit', require('@/components/admin/user_contact/UserContactEdit').default)

// Point Plan
Vue.component('point-plan-list', require('@/components/admin/point_plan/PointPlanList').default)

// Ticket Plan
Vue.component('ticket-plan-list', require('@/components/admin/ticket_plan/TicketPlanList').default)

// User Verification
Vue.component('user-verification-list', require('@/components/admin/user_verification/UserVerificationList').default)

// Guest Contact
Vue.component('guest-contact-list', require('@/components/admin/guest_contact/GuestContactList').default)
Vue.component('guest-contact-detail', require('@/components/admin/guest_contact/GuestContactDetail').default)

// Feedback Guest Contact
Vue.component('feedback-guest-contact-create', require('@/components/admin/feedback_guest_contact/FeedbackGuestContactCreate').default)
Vue.component('feedback-guest-contact-detail', require('@/components/admin/feedback_guest_contact/FeedbackGuestContactDetail').default)

// Party Video Play
Vue.component('party-video-play', require('@/components/admin/party/Video/PartyVideoPlay').default)

// Vue.directive('tinymce', {
//   bind () {
//     tinymce.init({
//       selector: '#tiny-div',
//       theme: 'modern',
//       branding: false
//     })
//   }
// })

window.app = new Vue({
  i18n,
  data: {
    token: csrfToken,
    lazyOption: {}
  }
}).$mount('#app')
