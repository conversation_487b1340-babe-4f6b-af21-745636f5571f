<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ __('common.app-name') }}</title>
    <link rel="icon" href="{{ asset('img/logo.png') }}">
    <link rel="stylesheet" href="{{ mix('css/app.css') }}">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert.min.js"></script>
    <script src="{{ mix('/js/manifest.js') }}" defer></script>
    <script src="{{ mix('/js/vendor.js') }}" defer></script>
    <script src="{{ mix('/js/app.js') }}" defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}" />
</head>
<body style="background-color: #EEEEEE;">
<div id="app">
    <nav class="navbar-fixed-top om-header" role="navigation">
        <div class="header">
            <div class="content-block om-flex">
                <div class="btn-dialog-close icon" style="display: flex; align-items: center">
                    <a href="https://rimoccha.com">
                        <i class="fas fa-angle-left">
                            <span style="font-size: 16px; padding-left: 10px">戻る</span>
                        </i>
                    </a>
                </div>
                <div class="title">パスワード再設定</div>
            </div>
        </div>
    </nav>
    <div class="om-phone-input-container">
        <form method="post" action="">
            @csrf
            <p style="margin-bottom: 3rem; color: #FAAA00">
                パスワード再設定
            </p>
            @if (session('token'))
                <div class="alert alert-danger" role="alert" style="width: 80%; margin: 1rem auto;">
                    {{ session('token') }}
                </div>
            @endif
            <div class="form-group" style="position: relative">
                <input id="password" name="password" class="om-form-text-long text-center" type="password" placeholder="パスワード" value="">
                <i class="fa fa-eye showpwd" id="show-password" onClick="showPwd('password', this)" style="position: absolute;top: 23px;right: 65px;"></i>
            </div>
            <div class="form-group" style="position: relative">
                <input name="password_confirmation" id="password-confirm" class="om-form-text-long text-center" type="password" placeholder="確認用パスワードを入力する" value="">
                <i class="fa fa-eye showpwd" id="show-password" onClick="showPwd('password-confirm', this)" style="position: absolute;top: 23px;right: 65px;"></i>
                <br>
                <span style="color: #FAAA00; font-size: 11px">半角英字8文字以上となります。</span>

                @if ($errors->has('password'))
                    <span class="invalid-feedback" role="alert">
                        {{ $errors->first('password') }}
                    </span>
                @endif
            </div>
            <button type="submit" id="submit-register" class="btn custom-btn-submit custom-btn-apply">
                送信する
            </button>
        </form>
    </div>
</div>
</body>
<style>
    ::placeholder {
        text-align: center;
        color: #FAAA00;
    }
</style>
<script type="text/javascript">
    $(document).ready(function () {
        var submitRegister = $('#submit-register')
        submitRegister[0].disabled = true;
        $('#password, #password-confirm').keyup(function () {
            if($('#password').val().length >= 8 && $('#password-confirm').val().length >= 8) {
                submitRegister[0].disabled = false;
                submitRegister[0].className = 'btn custom-btn-submit custom-btn-available'
            } else {
                submitRegister[0].disabled = true;
                submitRegister[0].className = 'btn custom-btn-submit custom-btn-apply'
            }
        })
    })
    function showPwd(id, el) {
        let x = document.getElementById(id);
        if (x.type === "password") {
            x.type = "text";
            el.className = 'fa fa-eye-slash showpwd';
        } else {
            x.type = "password";
            el.className = 'fa fa-eye showpwd';
        }
    }

</script>
