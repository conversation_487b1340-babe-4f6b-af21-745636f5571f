<?php
?>
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ __('common.app-name') }} | @yield('title')</title>
    <link rel="stylesheet" href="{{ mix('/css/app.css') }}">
    <script src="{{ mix('/js/manifest.js') }}" async defer></script>
    <script src="{{ mix('/js/vendor.js') }}" async defer></script>
    <script src="{{ mix('/js/app.js') }}" async defer></script>
    @yield('append-head')
</head>

<body class="hold-transition login-page">
<div id="app" class="login-box">
    <div class="login-logo">
        <b>Admin</b>
    </div>
    <!-- /.login-logo -->
    <div class="card">
        <div class="card-body login-card-body">
            <error-boundary>
                @include('flash::message')
                @if($errors->any())
                    @include('modal.validation-error')
                @endif
                @yield('content')
            </error-boundary>
        </div>
        <!-- /.login-card-body -->
    </div>
</div>
<!-- /.login-box -->
</body>
</html>
