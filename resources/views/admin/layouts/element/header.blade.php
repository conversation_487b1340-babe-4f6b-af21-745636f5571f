<?php
/**
 * @var \Src\Domain\Admin\Models\Auth\General $author
 */
?>
<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>
    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
        <li class="nav-item dropdown user user-menu">
            <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
                <img src="{{ asset('/img/avatar.jpeg') }}" class="user-image img-circle elevation-2">
                <span class="hidden-xs">{{ $author->getName() ?? $author->getLoginId() }}</span>
            </a>
            <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <!-- User image -->
                <li class="user-header bg-primary">
                    <img src="{{ asset('/img/avatar.jpeg') }}" class="img-circle elevation-2" alt="User Image">
                </li>
                <!-- Menu Footer-->
                <li class="user-footer row">
                    <div class="col-6">
                        <a class="btn btn-block btn-success" href="{{ route('admin.profile.show') }}">{{ __('models/account.screen_name.show') }}</a>
                    </div>
                    <div class="col-6">
                        <a class="btn btn-block btn-danger" href="{{ route('admin.auth.logout') }}">{{ __('common.btn.logout') }}</a>
                    </div>
                </li>
            </ul>
        </li>
    </ul>
</nav>

