<?php
/**
 * @var \Src\Domain\Admin\Models\Account\AccountForm $form
 */
?>
@extends('admin.layouts.default', ['current' => 'account'])

@section('title', __('models/account.screen_name.create'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/account.screen_name.create') }}</h1>
            </div>
        </div>
    </div>

@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <account-create
                    :account="{{ $form->toFormValue() }}"
                />
            </div>
        </div>
    </div>
@endsection
@section('append-body')
    @include('admin.elements.drop_down.gender')
@append
<script>
    import AccountCreate from "@/components/admin/account/AccountCreate";
    export default {
        components: {AccountCreate}
    }
</script>
