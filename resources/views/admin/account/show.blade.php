<?php
    /**
     * @var \Src\Domain\Admin\Models\Account\AccountDetail $account
     */
?>
@extends('admin.layouts.default')

@section('title', __('account.screen_name.show'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/account.screen_name.show') }}</h1>
            </div>
        </div>
    </div>

@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <profile-detail
                    :account="{{ $account->toComponentValue() }}"
                />
            </div>
        </div>
    </div>
@endsection
