@extends('admin.layouts.default', ['current' => 'account'])

@section('title', __('models/account.screen_name.index'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/account.screen_name.index') }}</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <a href="{{ route('admin.account.create') }}" class="btn btn-block btn-success">{{ __('models/account.screen_name.create') }}</a>
                </ol>
            </div>
        </div>
    </div>

@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <account-list
                    :account="{{ $account->toJson() }}"
                    keyword="{{ $keyword }}"
                />
            </div>
        </div>
    </div>
@endsection
