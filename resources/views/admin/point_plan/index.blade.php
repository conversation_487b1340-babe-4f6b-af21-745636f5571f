<?php
/** @var \Illuminate\Pagination\LengthAwarePaginator|\Src\Domain\Admin\Models\PointPlan\PointPlan[] $point_plans */
?>

@extends('admin.layouts.default', ['current' => 'point_plan'])

@section('title', __('models/point_plan.screen_name.index'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/point_plan.screen_name.index') }}</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <a href="{{ route('admin.point_plan.create') }}" class="btn btn-block btn-success">{{ __('models/point_plan.screen_name.create') }}</a>
                </ol>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <point-plan-list
                    :pagination="{{ $paginator->toJson() }}"
                />
            </div>
        </div>
    </div>
@endsection
