<?php
/**
 * @var \Src\Domain\Admin\Models\Notification\NotificationForm $form
 */
?>
@extends('admin.layouts.default',['current' => 'notification_create'])

@section('title', __('models/notification.screen_name.create'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/notification.screen_name.create') }}</h1>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <notification-create
                    :notifications="{{ $form->toFormValue() }}"
                />
            </div>
        </div>
    </div>

@endsection
@section('append-body')
    @include('admin.elements.drop_down.notification_div')
@append
