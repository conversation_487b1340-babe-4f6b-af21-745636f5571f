<?php
/** @var \Illuminate\Pagination\LengthAwarePaginator|\Src\Domain\Admin\Models\UserReport\UserReport[] $user_reports */
?>

@extends('admin.layouts.default', ['current' => 'user_report'])

@section('title', __('models/user_report.screen_name.index'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user_report.screen_name.index') }}</h1>
            </div>
            <div class="col-sm-6">
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <user-report-list
                    :user-report="{{ $userReport->toJson() }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.enum.report_status')
    @include('admin.elements.drop_down.gender')
    @include('admin.elements.enum.report_div')
@append
