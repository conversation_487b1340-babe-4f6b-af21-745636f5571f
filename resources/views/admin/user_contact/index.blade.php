<?php
/** @var \Illuminate\Pagination\LengthAwarePaginator|\Src\Domain\Admin\Models\UserContact\UserContact[] $user_contacts */
?>

@extends('admin.layouts.default', ['current' => 'user_contact'])

@section('title', __('models/user_contact.screen_name.index'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user_contact.screen_name.index') }}</h1>
            </div>
{{--            <div class="col-sm-6">--}}
{{--                <ol class="breadcrumb float-sm-right">--}}
{{--                    <a href="{{ route('admin.user_contact.create') }}" class="btn btn-block btn-success">{{ __('models/user_contact.screen_name.create') }}</a>--}}
{{--                </ol>--}}
{{--            </div>--}}
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <user-contact-list
                    :user-contact="{{ $userContact->toJson() }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.drop_down.gender')
    @include('admin.elements.enum.contact_div')
    @include('admin.elements.enum.contact_state')
@append
