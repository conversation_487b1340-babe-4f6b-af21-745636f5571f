<?php
/** @var \Src\Domain\Admin\Models\User\UserForm $form*/
?>
@extends('admin.layouts.default', ['current' => 'user_contact'])

@section('title', __('models/user_contact.screen_name.create'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user_contact.screen_name.create') }}</h1>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <user-contact-create
                    :user-contact="{{ $form->toFormValue() }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.enum.contact_div')
@append