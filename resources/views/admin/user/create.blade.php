<?php
    /**
     * @var \Src\Domain\Admin\Models\User\UserForm $form
     */
?>
@extends('admin.layouts.default', ['current' => 'user'])

@section('title', __('models/user.screen_name.create'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user.screen_name.create') }}</h1>
            </div>
        </div>
    </div>

@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <user-create
                    :user="{{ $form->toFormValue() }}"
                />
            </div>
        </div>
    </div>
@endsection
@section('append-body')
    @include('admin.elements.drop_down.gender')
@append
