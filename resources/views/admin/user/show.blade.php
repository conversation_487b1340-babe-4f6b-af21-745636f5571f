<?php
    /**
     * @var \Src\Domain\Admin\Models\UserProfile\UserProfileDetail $user
     */
?>
@extends('admin.layouts.default', ['current' => 'user'])

@section('title', __('models/user.screen_name.show'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user.screen_name.show') }}</h1>
            </div>
        </div>
    </div>

@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <user-detail
                    :user="{{ $user->toComponentValue() }}"
                    :tab-index="{{ $index }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.drop_down.gender')
    @include('admin.elements.enum.contact_div')
    @include('admin.elements.enum.contact_state')
    @include('admin.elements.enum.report_div')
    @include('admin.elements.enum.report_status')
    @include('admin.elements.enum.user_status')
    @include('admin.elements.enum.report_type')
@append
