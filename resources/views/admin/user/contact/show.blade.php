@extends('admin.layouts.default', ['current' => 'user'])

@section('title', __('models/user.detail.user_contact.screen_name.show'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/user.detail.user_contact.screen_name.show') }}</h1>
            </div>
        </div>
    </div>
@endsection
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <detail-user-contact-show
                    :user-contact="{{ $user_contact }}"
                    :user-id="{{ $user_id }}"
                    :gender="{{ $gender }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.enum.feedback_status')
@append
