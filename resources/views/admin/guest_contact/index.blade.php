@extends('admin.layouts.default', ['current' => 'guest_contact'])

@section('title', __('models/guest_contact.screen_name.index'))

@section('breadcrumb')
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark">{{ __('models/guest_contact.screen_name.index') }}</h1>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <guest-contact-list
                    :guest-contact="{{ $guestContact->toJson() }}"
                />
            </div>
        </div>
    </div>
@endsection

@section('append-body')
    @include('admin.elements.enum.contact_div')
@append
