<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Src\Domain\Api\Services\Queue\NotificationQueueService;

class NotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public $timeout = 60;

    /**
     * @var NotificationQueueService
     */
    private $notification_queue_service;

    /**
     * @var int
     */
    private $user_id;

    /**
     * @var int
     */
    private $number_unread_notification;


    /**
     * Create a new job instance.
     *
     * NotificationJob constructor.
     * @param int $user_id
     * @param int $number_unread_notification
     */
    public function __construct( int $user_id, int $number_unread_notification)
    {
        $this->user_id = $user_id;
        $this->number_unread_notification = $number_unread_notification;
        $this->notification_queue_service = new NotificationQueueService();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        logger()->info('___JOB_START: sendNewBadge');
        $result = $this->notification_queue_service->sendNewBadge($this->user_id, $this->number_unread_notification);
        if (!$result) {
            logger()->error('___JOB_ERROR: sendNewBadge');
        }

        logger()->info('___JOB_END: sendNewBadge');
    }

}
