<?php

namespace App\Jobs\PartyExtend;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Src\Domain\Api\Services\Queue\PartyExtentQueueService;

class PartyExtendExtendUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public $timeout = 60;

    /**
     * @var int
     */
    private $creator_id;

    /**
     * @var int
     */
    private $party_id;

    /**
     * @var array
     */
    private $agora_token_info;

    /**
     * Create a new job instance.
     *
     * @param int $creator_id
     * @param int $party_id
     * @param array $agora_token
     */
    public function __construct(int $creator_id, int $party_id, array $agora_token)
    {
        $this->creator_id = $creator_id;
        $this->party_id = $party_id;
        $this->agora_token_info = $agora_token;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        logger()->info('___JOB_START: ' . __CLASS__);
        $result = (new PartyExtentQueueService)->extendUser($this->creator_id, $this->party_id, $this->agora_token_info);
        if (!$result) {
            logger()->error('___JOB_ERROR: ' . __CLASS__);
        }

        logger()->info('___JOB_END: ' . __CLASS__);
    }

}
