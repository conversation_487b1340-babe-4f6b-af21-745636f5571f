<?php

namespace App\Jobs\PartyRecord;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Src\Domain\Api\Services\Queue\PartyRecordQueueService;

class PartyRecordStartJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;
    public $timeout = 60;

    /**
     * @var int
     */
    private $party_id;

    /**
     * Create a new job instance.
     *
     * @param int $party_id
     */
    public function __construct( int $party_id)
    {
        $this->party_id = $party_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        logger()->info('___JOB_START: ' . __CLASS__);
        $result = (new PartyRecordQueueService)->startRecord($this->party_id);
        if (!$result) {
            logger()->error('___JOB_ERROR: ' . __CLASS__);
        }

        logger()->info('___JOB_END: ' . __CLASS__);
    }

}
