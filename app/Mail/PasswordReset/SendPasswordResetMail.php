<?php

namespace App\Mail\PasswordReset;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/**
 * Class SendPasswordResetMail
 * @package App\Mail\PasswordReset
 */
class SendPasswordResetMail extends Mailable
{
    use Queueable, SerializesModels;
    public $mailData;

    /**
     * Create a new message instance.
     *
     * @param $mailData
     */
    public function __construct($mailData)
    {
        $this->mailData = $mailData;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this
            ->view('mail.password_reset.send')
            ->subject('[Rimoccha Reset Password] ')
            ->with('mailData', $this->mailData);
    }
}
