<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Src\Enum\ResultCode;
use Src\Exception\APIRuntimeException;
use Src\Utils\Util;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param \Throwable $exception
     * @return void
     *
     * @throws Exception
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param Throwable $exception
     * @return \Illuminate\Http\JsonResponse|Response
     * @throws Throwable
     */
    public function render($request, Throwable $exception)
    {
//        dd('VALIDAE');
        if ($request->is('api/*')) {
            $exception = $this->prepareException($exception);
            $status_code = 500;
            $code  = 0;
//            $code  = Util::getErrorCode('api_errors.main.internal_server_error');

            if ($exception instanceof AuthenticationException) {
                return json_response(ResultCode::TOKEN_INVALID, null,  ['code' => Util::getErrorCode('api_errors.main.unauthorized')
                    , 'message' => Util::getErrorMessage('api_errors.main.unauthorized')]);
            }

            if ($exception instanceof ValidationException) {

                return json_response(ResultCode::ERROR, null,  ['code' =>  Util::getErrorCode('api_errors.main.invalid_input'),
                    'message' =>  Util::getErrorMessage('api_errors.main.invalid_input'), 'errors' => $exception->errors()] );
            }

            if ($exception instanceof APIRuntimeException) {
                return json_response(ResultCode::ERROR, null, ['code' => $exception->getCode(), 'message' => $exception->getMessage()]);
            }

            if (method_exists($exception, 'getStatusCode')) {
                $status_code = $exception->getStatusCode();
            }

            $response['code'] = $status_code;
            switch ($status_code) {
                case 404:
                    $response['code'] = Util::getErrorCode('api_errors.main.data_not_found');
                    $response['message'] = 'Not Found';
                    break;
                case 403:
                    $response['code'] = Util::getErrorCode('api_errors.main.forbidden');
                    $response['message'] = 'Forbidden';
                    break;
                default:
                    $response['code'] = Util::getErrorCode('api_errors.main.internal_server_error');
                    $response['message'] = $exception->getMessage();

                    break;
            }

            return json_response(ResultCode::ERROR, null, $response);
//            return json_response(ResultCode::ERROR, null, $response);
        }

        return parent::render($request, $exception);
    }
}
