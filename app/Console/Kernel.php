<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // If production environment then run schedule
//        if(!config('app.debug', false)) {
            $schedule->command('inspire')->hourly()->onOneServer();
            $schedule->command('passport:purge')->hourly()->onOneServer();
            $schedule->call('Src\Domain\Admin\Services\NotificationService@storeUserNotification')->name('user_notification')->withoutOverlapping()->everyMinute()->onOneServer();

            // Notification to user
            $schedule->command('user_notification:push')->everyMinute()->onOneServer();

            // Verification Code
            $schedule->command('verification_code:delete_invalid')->monthlyOn(1, '00:00')->onOneServer();

            // Party
            $schedule->command('setting_party:delete_timeout')->everyMinute()->onOneServer();
            $schedule->command('party:delete_timeout')->everyMinute()->onOneServer();

            // Refund Point
            $schedule->command('refund_point:monthly')->monthlyOn(1, '01:00')->onOneServer();
//        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
