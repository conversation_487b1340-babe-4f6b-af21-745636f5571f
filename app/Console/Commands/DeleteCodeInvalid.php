<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Src\Domain\Api\Services\VerificationCodeService;

class DeleteCodeInvalid extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verification_code:delete_invalid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete verification code invalid';

    /**
     * @var VerificationCodeService
     */
    private $verification_code_service;

    /**
     * Create a new command instance.
     *
     * @param VerificationCodeService $verification_code_service
     */
    public function __construct(VerificationCodeService $verification_code_service)
    {
        parent::__construct();
        $this->verification_code_service = $verification_code_service;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            logger()->info('Start delete code invalid');
            $this->verification_code_service->deleteCodeInvalid();

        } catch (\Throwable $exception) {
            logger()->info('failed to push notification');
            logger()->error($exception);
        } finally {
            logger()->info('Release lock');
        }

        return 0;
    }
}
