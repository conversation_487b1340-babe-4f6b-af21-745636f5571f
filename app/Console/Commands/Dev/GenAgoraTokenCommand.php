<?php

namespace App\Console\Commands\Dev;

use Illuminate\Console\Command;
use Src\Utils\Agora\GenToken\AgoraGenToken;

class GenAgoraTokenCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agora:token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $channelId = "tu_party_4";
//        $uids = [1111];
        $uids = [3333];
//        $uids = [1,2,3,4,5,6,7,8];
//        $expire_at = now()->addMinutes(PARTY_NUMBER_MINUTE_FOR_ONE_TICKET);
        $expire_at = now()->addMinutes(5000);
        $tokens = AgoraGenToken::genAgoraTokens($channelId, $uids, $expire_at);

        echo 'Tokens:'.json_encode($tokens, JSON_PRETTY_PRINT).PHP_EOL;
        return 0;
    }
}
