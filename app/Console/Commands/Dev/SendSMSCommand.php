<?php

namespace App\Console\Commands\Dev;

use Illuminate\Console\Command;
use Src\Utils\TwilioSMS;

class SendSMSCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twilio:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $toNumber = "+84982596750";
//        $toNumber = "+84973506675"; // Thu
//        $toNumber = "+84961346534"; // Ha
//        $toNumber = "+819027411777"; // Mr Sakai
        TwilioSMS::sendSMS($toNumber, "21212");
        return 0;
    }
}
