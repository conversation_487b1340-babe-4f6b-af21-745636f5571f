<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Src\Domain\Api\Services\SettingParty\DeleteService;

class DeleteSettingPartyTimeout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setting_party:delete_timeout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete setting party timeout';

    /**
     * @var DeleteService
     */
    private $setting_party_service;

    /**
     * Create a new command instance.
     *
     * @param DeleteService $setting_party_service
     */
    public function __construct(DeleteService $setting_party_service)
    {
        parent::__construct();
        $this->setting_party_service = $setting_party_service;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            logger()->info('Start delete_timeout');
            $this->setting_party_service->deleteTimeout();

        } catch (\Throwable $exception) {
            logger()->info('failed to push notification');
            logger()->error($exception);
        } finally {
            logger()->info('Release lock');
        }

        return 0;
    }
}
