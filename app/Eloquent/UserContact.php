<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
* Class UserContact
*
* @package App\Eloquent
*
 * @property int $id
 * @property int $user_id
 * @property int $contact_status
 * @property int $contact_div
 * @property string $body
 * @property string|\Carbon\Carbon $created_at
 * @property string|\Carbon\Carbon $updated_at
 * @property Collection|UserProfile $userProfile
 * @property User $user
 * @property Feedback $feedback
 */
class UserContact extends Model
{
    use SoftDeletes;

    public $fillable = [
        'user_id',
        'contact_status',
        'contact_div',
        'body'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'contact_status' => 'integer',
        'contact_div' => 'integer',
        'body' => 'string'
    ];

    /**
     * @return HasOne
     */
    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class, 'user_id', 'user_id');
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @return MorphOne
     */
    public function feedback(): MorphOne
    {
        return $this->morphOne(Feedback::class,'feedbackable','foreign_table','foreign_id');
    }

}
