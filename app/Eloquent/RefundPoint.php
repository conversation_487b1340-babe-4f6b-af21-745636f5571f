<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class RefundPoint
 *
 * @package App\Eloquent
 *
 * @property int $point_history_id
 * @property int $refund_ratio_id
 * @property int $refund_money
 * @property int $refund_point
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property PointHistory $pointHistory
 */
class RefundPoint extends Model
{
    protected $primaryKey = 'point_history_id';

    protected $fillable = [
        'refund_ratio_id',
        'refund_money',
        'refund_point'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'point_history_id' => 'integer',
        'refund_ratio_id' => 'integer',
        'refund_money' => 'integer',
        'refund_point' => 'integer'
    ];

    /**
     * @return BelongsTo
     */
    public function pointHistory(): BelongsTo
    {
        return $this->belongsTo(PointHistory::class, 'point_history_id');
    }
}
