<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class UserNotification
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $notification_div
 * @property string $foreign_table
 * @property int $foreign_id
 * @property int $party_id
 * @property string $title
 * @property string $body
 * @property string $data
 * @property string|Carbon $notification_at
 * @property boolean $has_action
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Collection|User[] users
 * @property Account|User creator
 */
class UserNotification extends Model
{
    public $fillable = [
        'user_id',
        'notification_div',
        'foreign_table',
        'foreign_id',
        'party_id',
        'title',
        'body',
        'data',
        'notification_at',
        'has_action',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'notification_div' => 'integer',
        'foreign_table' => 'string',
        'foreign_id' => 'integer',
        'party_id' => 'integer',
        'title' => 'string',
        'body' => 'string',
        'data' => 'string',
        'notification_at' => 'datetime',
        'has_action' => 'boolean'
    ];

    /**
     * @return BelongsToMany
     **/
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_notification_pivots')
            ->withTimestamps()->withPivot(['is_read_detail']);

    }

    /**
     * @return HasOne
     **/
    public function creator()
    {
        return $this->hasOne(UserNotificationCreator::class);
    }


}
