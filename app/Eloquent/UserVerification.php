<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserVerification
 *
 * @package App\Eloquent
 *
 * @property int $user_id
 * @property int $identification_type
 * @property int $approval_status
 * @property int $storage_file_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property StorageFile $storageFile
 * @property User $user
 */
class UserVerification extends Model
{
    protected $primaryKey = 'user_id';

    public $fillable = [
        'identification_type',
        'approval_status',
        'storage_file_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'identification_type' => 'integer',
        'approval_status' => 'integer',
        'storage_file_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function storageFile()
    {
        return $this->belongsTo(StorageFile::class, 'storage_file_id');
    }

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
