<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Class Feedback
 *
 * @package App
 *
 * @property int $id
 * @property int $enable_warning
 * @property string $content
 * @property int $send_status
 * @property string|Carbon $send_schedule_at
 * @property string|Carbon $sent_at
 * @property int $feedback_div
 * @property int $creator_id
 * @property string $foreign_table
 * @property int $foreign_id
 * @property int $target_user_id
 * @property string $guest_user_id
 * @property Account $creator
 * @property User $targetUser
 * @property UserReport $report
 * @property UserContact $contact
 * @property UserContact|UserReport $feedbackable
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 * @property string|Carbon $deleted_at
 */
class Feedback extends Model
{
    protected $table = 'feedbacks';

    public $fillable = [
        'enable_warning',
        'content',
        'send_status',
        'send_schedule_at',
        'sent_at',
        'feedback_div',
        'creator_id',
        'foreign_table',
        'foreign_id',
        'target_user_id',
        'guest_user_id',

    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'enable_warning' => 'integer',
        'content' => 'string',
        'send_status' => 'integer',
        'send_schedule_at' => 'datetime',
        'sent_at' => 'datetime',
        'feedback_div' => 'integer',
        'creator_id' => 'integer',
        'foreign_table' => 'string',
        'foreign_id' => 'integer',
        'target_user_id' => 'integer',
        'guest_user_id' => 'string',
    ];

    /**
     * @return BelongsTo
     */
    public function targetUser()
    {
        return $this->belongsTo(User::class,'target_user_id');
    }


    /**
     * @return MorphTo
     */
    public function feedbackable(): MorphTo
    {
        return $this->morphTo(__FUNCTION__,'foreign_table','foreign_id');
    }

    /**
     * @return BelongsTo
     */
    public function creator()
    {
        return $this->belongsTo(Account::class, 'creator_id');
    }

    /**
     * @return BelongsTo
     */
    public function guestContact()
    {
        return $this->belongsTo(GuestContact::class,'foreign_id');
    }

}
