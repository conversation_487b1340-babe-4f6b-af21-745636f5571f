<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class PartyGroup
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $party_id
 * @property int $setting_party_id
 * @property int $gender
 * @property int $gender_partner
 * @property int $from_age
 * @property int $to_age
 * @property int $from_age_partner
 * @property int $to_age_partner
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Party $party
 * @property SettingParty settingParty
 * @property Collection $partyParticipants
 */
class PartyGroup extends Model
{
    public $fillable = [
        'party_id',
        'setting_party_id',
        'gender',
        'gender_partner',
        'from_age',
        'to_age',
        'from_age_partner',
        'to_age_partner'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_id' => 'integer',
        'setting_party_id' => 'integer',
        'gender' => 'integer',
        'gender_partner' => 'integer',
        'from_age' => 'integer',
        'to_age' => 'integer',
        'from_age_partner' => 'integer',
        'to_age_partner' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id');
    }

    /**
     * @return HasMany
     **/
    public function partyParticipants()
    {
        return $this->hasMany(PartyParticipant::class, 'party_group_id');
    }

    /**
     * @return BelongsTo
     **/
    public function settingParty()
    {
        return $this->belongsTo(SettingParty::class, 'setting_party_id');
    }
}
