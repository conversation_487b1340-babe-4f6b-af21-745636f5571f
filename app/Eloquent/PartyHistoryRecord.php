<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PartyHistoryRecord
 *
 * @package App\Eloquent
 *
 * @property int $party_history_id
 * @property string $channel_id
 * @property string $record_token
 * @property string $record_uid
 * @property string $record_resource_id
 * @property string $record_sid
 * @property int $record_status
 * @property int $record_file_id
 *
 * @property StorageFile $recordFile
 *
 */
class PartyHistoryRecord extends Model
{
    /**
     * @var array
     */
    public $fillable = [
        'party_history_id',
        'channel_id',
        'record_token',
        'record_uid',
        'record_resource_id',
        'record_sid',
        'record_status',
        'record_file_id'
    ];
    /**
     * @var array
     */
    protected $casts = [
        'party_history_id' => 'integer',
        'channel_id' => 'string',
        'record_token' => 'string',
        'record_uid' => 'integer',
        'record_resource_id' => 'string',
        'record_sid' => 'string',
        'record_status' => 'integer',
        'record_file_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     */
    public function recordFile()
    {
        return $this->belongsTo(StorageFile::class,'record_file_id');
    }
}
