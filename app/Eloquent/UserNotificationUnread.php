<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserNotificationUnread
 *
 * @package App\Eloquent
 *
 * @property int $user_id
 * @property int $number_unread_notification
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property User $user
 */
class UserNotificationUnread extends Model
{
    protected $primaryKey = 'user_id';

    public $fillable = [
        'number_unread_notification'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'number_unread_notification' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
