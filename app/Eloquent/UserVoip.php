<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class UserVoip
 *
 * @package App\Eloquent
 *
 * @property User $user
 * @property Collection $ticketHistories
 * @property int $user_id
 * @property int $voip_token
 * @property int $endpoint_arn
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserVoip extends Model
{
    protected $primaryKey = 'user_id';

    public $fillable = [
        'voip_token',
        'endpoint_arn'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'voip_token' => 'string',
        'endpoint_arn' => 'string',
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
