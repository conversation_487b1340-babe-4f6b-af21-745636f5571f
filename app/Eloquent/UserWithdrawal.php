<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserWithdrawal
 *
 * @package App\Eloquent
 *
 * @property int $user_id
 * @property int $reason_div
 * @property string|Carbon $withdrawal_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property User $user
 */
class UserWithdrawal extends Model
{
    protected $primaryKey = 'user_id';

    public $fillable = [
        'reason_div',
        'withdrawal_at'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'reason_div' => 'integer',
        'withdrawal_at' => 'datetime'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
