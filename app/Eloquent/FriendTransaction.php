<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class FriendTransaction
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $party_id
 * @property int $send_user_id
 * @property int $receive_user_id
 * @property string $invite_token
 * @property string|Carbon $created_at
 *
 * @property User $user
 * @property User $receiveUser
 */
class FriendTransaction extends Model
{
    const UPDATED_AT = null;

    public $fillable = [
        'party_id',
        'send_user_id',
        'receive_user_id',
        'invite_token',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_id' => 'integer',
        'send_user_id' => 'integer',
        'receive_user_id' => 'integer',
        'invite_token' => 'string'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo
     **/
    public function receiveUser()
    {
        return $this->belongsTo(User::class, 'receive_user_id');
    }

    /**
     * @return BelongsTo
     **/
    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id');
    }
}
