<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class PartyHistoryGroup
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $party_history_id
 * @property int $gender
 * @property int $gender_partner
 * @property int $from_age
 * @property int $to_age
 * @property int $from_age_partner
 * @property int $to_age_partner
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Collection $partyHistoryParticipants
 */
class PartyHistoryGroup extends Model
{
    public $fillable = [
        'party_history_id',
        'gender',
        'gender_partner',
        'from_age',
        'to_age',
        'from_age_partner',
        'to_age_partner'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_history_id' => 'integer',
        'gender' => 'integer',
        'gender_partner' => 'integer',
        'from_age' => 'integer',
        'to_age' => 'integer',
        'from_age_partner' => 'integer',
        'to_age_partner' => 'integer'
    ];

    /**
     * @return HasMany
     **/
    public function partyHistoryParticipants()
    {
        return $this->hasMany(PartyHistoryParticipant::class, 'party_history_group_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function participants()
    {
        return $this->belongsToMany(User::class, 'party_history_participants')->using(PartyHistoryParticipant::class)
            ->withTimestamps()->withPivot(['user_id','party_history_id','party_history_group_id','party_participant_status',
                'started_at','ended_at','expire_at','number_extended','party_time','real_party_time']);
    }
}
