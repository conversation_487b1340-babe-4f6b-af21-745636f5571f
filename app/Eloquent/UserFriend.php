<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserFriend
 *
 * @package App\Eloquent
 *
 * @property Party $party
 * @property User $user
 * @property int $user_id
 * @property int $friend_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserFriend extends Model
{
    public $fillable = [
        'friend_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'friend_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'friend_id','id');
    }
}
