<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Privacy
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $title
 * @property string $body
 * @property string $type
 * @property int $order
 * @property boolean $is_public
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Privacy extends Model
{
    use SoftDeletes;

    /**
     * Name of table
     * @var string
     */
    protected $table = 't_privacy';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'title',
        'body',
        'type',
        'order',
        'is_public',
    ];
}
