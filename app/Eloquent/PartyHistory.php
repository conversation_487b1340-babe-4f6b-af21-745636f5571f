<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class PartyHistory
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $party_id
 * @property string $channel_id
 * @property string $record_token
 * @property string $record_uid
 * @property string $record_resource_id
 * @property string $record_sid
 * @property int $party_type
 * @property int $party_status
 * @property int $invite_member_type
 * @property int $group_type
 * @property int $creator_id
 * @property string|Carbon $match_at
 * @property string|Carbon $start_at
 * @property string|Carbon $expire_at
 * @property string|Carbon $ended_at
 * @property string $party_time
 * @property string $real_party_time
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Party $party
 * @property Collection|PartyHistoryGroup $partyHistoryGroups
 * @property User $creator
 * @property PartyHistoryRecord $partyHistoryRecord
 */
class PartyHistory extends Model
{
    public $fillable = [
        'party_id',
        'channel_id',
        'party_type',
        'invite_member_type',
        'group_type',
        'party_status',
        'creator_id',
        'match_at',
        'start_at',
        'expire_at',
        'ended_at',
        'party_time',
        'real_party_time',
        'record_token',
        'record_uid',
        'record_resource_id',
        'record_status',
        'record_sid',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_id' => 'integer',
        'channel_id' => 'string',
        'party_type' => 'integer',
        'party_status' => 'integer',
        'invite_member_type' => 'integer',
        'group_type' => 'integer',
        'creator_id' => 'integer',
        'match_at' => 'datetime',
        'start_at' => 'datetime',
        'expire_at' => 'datetime',
        'ended_at' => 'datetime',
        'record_token' => 'string',
        'record_uid' => 'string',
        'record_resource_id' => 'string',
        'record_sid' => 'string',
        'record_status' => 'integer',
    ];

    /**
     * @return BelongsTo
     **/
    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function participants()
    {
        return $this->belongsToMany(User::class, 'party_history_participants')
        ->withTimestamps()->withPivot(['user_id','party_history_id','party_history_group_id','party_participant_status',
                'started_at','ended_at','expire_at','number_extended','party_time','real_party_time']);
    }

    /**
     * @return HasMany
     **/
    public function partyHistoryGroups()
    {
        return $this->hasMany(PartyHistoryGroup::class, 'party_history_id');
    }

    /**
     * @return HasOne
     */
    public function partyHistoryRecord()
    {
        return $this->hasOne(PartyHistoryRecord::class,'party_history_id');
    }

}
