<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Database\Factories\AccountFactory;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\Access\Authorizable;

/**
 * Class Account
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $login_id
 * @property string $password
 * @property string $email
 * @property string $name
 * @property int $role_div
 * @property int $remain_login
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $deleted_at
 *
 * @property StorageFile $avatar
 */
class Account extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, SoftDeletes, HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 't_accounts';

    /**
     * The attributes that are mass assignable;
     *
     * @var array
     */
    protected $fillable = [
        'login_id',
        'password',
        'email',
        'name',
        'role_div',
        'remain_login'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token'
    ];

    protected static function newFactory()
    {
        return AccountFactory::new();
    }
}
