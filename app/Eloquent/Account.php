<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Foundation\Auth\Access\Authorizable;

/**
 * Class Account
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $login_id
 * @property string $password
 * @property string $account_name
 * @property string $email
 * @property string $remember_token
 * @property int $usage_status
 *
 * @property Carbon $deleted_at
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class Account extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable;

    /**
     * The attributes that are mass assignable;
     *
     * @var array
     */
    protected $fillable = [
        'login_id',
        'password',
        'email',
        'account_name',
        'usage_status'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];
}
