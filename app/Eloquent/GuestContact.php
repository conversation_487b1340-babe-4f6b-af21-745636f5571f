<?php

namespace App\Eloquent;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class UserContact
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property int $contact_div
 * @property int $contact_status
 * @property string $body
 * @property string|\Carbon\Carbon $created_at
 * @property string|\Carbon\Carbon $updated_at
 */
class GuestContact extends Model
{
    use SoftDeletes;

    public $fillable = [
        'name',
        'email',
        'contact_div',
        'contact_status',
        'body'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'name' => 'string',
        'email' => 'string',
        'contact_div' => 'integer',
        'contact_status' => 'integer',
        'body' => 'string'
    ];

    /**
     * @return HasOne
     */
    public function feedback(): HasOne
    {
        return $this->hasOne(Feedback::class, 'foreign_id');
    }
}
