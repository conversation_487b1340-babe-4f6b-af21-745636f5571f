<?php

namespace App\Eloquent;


use App\UserNotificationPivot;
use Carbon\Carbon;
use Doctrine\DBAL\Query\QueryBuilder;
use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\Access\Authorizable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Src\Enum\UserStatus;

/**
 * Class User
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $member_id
 * @property string $password
 * @property string $login_id
 * @property int $login_id_type
 * @property int $mobile_platform
 * @property int $device_register
 * @property int $method_register
 * @property string $fcm_token
 * @property int $user_status
 * @property bool $is_warning
 * @property bool $web_present_processed
 * @property string $last_access_on
 * @property string|Carbon $registered_at
 * @property string|Carbon $official_member_at
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property Collection|SocialUser[] socialUsers
 * @property Collection|User[] userFriends
 * @property Collection|User[] userBlocks
 * @property Collection|Feedback[] feedback
 * @property UserProfile userProfile
 * @property UserVerification userVerification
 * @property UserPoint userPoint
 * @property Collection|UserContact[] userContacts
 * @property Collection|UserReport[] userReports
 * @property Collection|PointHistory pointHistories
 * @property UserTicket userTicket
 * @property UserVoip userVoip
 * @property UserWithdrawal userWithdrawal
 * @property UserNotificationUnread userNotificationUnread
 * @property UserNotificationPivot userNotificationPivot
 * @property Collection|User[] ticketHistories
 * @property mixed pivot
 *
 * // methods
 * @method Builder|QueryBuilder|static officalMember()
 */
class User extends Model  implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, HasApiTokens, Notifiable, SoftDeletes;

    public $fillable = [
        'member_id',
        'login_id',
        'login_id_type',
        'password',
        'mobile_platform',
        'device_register',
        'method_register',
        'fcm_token',
        'user_status',
        'is_warning',
        'last_access_on',
        'registered_at',
        'official_member_at',
        'web_present_processed'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'member_id' => 'string',
        'login_id' => 'string',
        'login_id_type' => 'integer',
        'mobile_platform' => 'integer',
        'device_register' => 'integer',
        'method_register' => 'integer',
        'fcm_token' => 'string',
        'user_status' => 'integer',
        'is_warning' => 'bool',
        'last_access_on' => 'date',
        'registered_at' => 'datetime',
        'official_member_at' => 'datetime',
        'web_present_processed' => 'bool'
    ];

    //================================= Scope ================================================

    public function scopeOfficalMember($query){
        return $query->where('user_status', UserStatus::OFFICIAL_MEMBER);
    }

    //================================= Relations ================================================

    /**
     * @return HasMany
     **/
    public function socialUsers()
    {
        return $this->hasMany(SocialUser::class, 'user_id');
    }

    /**
     * userFriends relation
     *
     * @return BelongsToMany
     */
    public function userFriends(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'user_friends', 'user_id', 'friend_id')->withTimestamps();
    }

    /**
     * userBlocks relation
     *
     * @return BelongsToMany
     */
    public function userBlocks(): BelongsToMany
    {
        return $this->belongsToMany(__CLASS__, 'user_blocks', 'user_id','block_user_id')
            ->withTimestamps()->withPivot('created_at');
    }

    /**
     * userProfile relation
     *
     * @return HasOne
     */
    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * userVerification
     *
     * @return HasOne
     */
    public function userVerification(): HasOne
    {
        return $this->hasOne(UserVerification::class);
    }


    /**
     * userPoint
     *
     * @return HasOne
     */
    public function userPoint(): HasOne
    {
        return $this->hasOne(UserPoint::class);
    }

    /**
     * pointHistories
     *
     * @return HasMany
     */
    public function pointHistories(): HasMany
    {
        return $this->hasMany(PointHistory::class);
    }

    /**
     * @return BelongsToMany
     **/
    public function messageConversations()
    {
        return $this->belongsToMany(MessageConversation::class, 'message_participants','user_id','message_conversation_id')->withTimestamps();
    }


    /**
     * userTicket
     *
     * @return HasOne
     */
    public function userTicket(): HasOne
    {
        return $this->hasOne(UserTicket::class);
    }

    /**
     * userVoip
     *
     * @return HasOne
     */
    public function userVoip(): HasOne
    {
        return $this->hasOne(UserVoip::class);
    }

    /**
     * userWithdrawal
     *
     * @return HasOne
     */
    public function userWithdrawal(): HasOne
    {
        return $this->hasOne(UserWithdrawal::class);
    }

    /**
     * userNotificationUnread
     *
     * @return HasOne
     */
    public function userNotificationUnread(): HasOne
    {
        return $this->hasOne(UserNotificationUnread::class);
    }

    /**
     * userNotificationPivot
     *
     * @return HasOne
     */
    public function userNotificationPivot(): HasOne
    {
        return $this->hasOne(UserNotificationPivot::class);
    }

    /**
     * ticketHistories
     *
     * @return HasMany
     */
    public function ticketHistories(): HasMany
    {
        return $this->hasMany(TicketHistory::class);
    }

    /**
     * @return HasMany
     */
    public function userContacts(): HasMany
    {
        return $this->hasMany(UserContact::class);
    }

    /**
     * @return HasMany
     **/
    public function userReports()
    {
        return $this->hasMany(UserReport::class, 'user_id');
    }

    /**
     * @return BelongsToMany
     */
    public function userNotifications(): BelongsToMany
    {
        return $this->belongsToMany(UserNotification::class, 'user_notification_pivots', 'user_id', 'user_notification_id')
            ->withTimestamps()->withPivot('is_read_detail');
    }

    /**
     * @return HasMany
     **/
    public function parties()
    {
        return $this->hasMany(Party::class, 'creator_id');
    }

    /**
     * @return HasMany
     */
    public function feedback()
    {
        return $this->hasMany(Feedback::class,'target_user_id');
    }

}
