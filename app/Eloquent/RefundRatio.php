<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class RefundRatio
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property string $month
 * @property float $ratio
 * @property int $criteria_friend_value
 * @property float $criteria_friend_ratio
 * @property int $criteria_party_time_value
 * @property float $criteria_party_time_ratio
 * @property int $criteria_party_number_value
 * @property float $criteria_party_number_ratio
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class RefundRatio extends Model
{
    public $fillable = [
        'user_id',
        'month',
        'ratio',
        'criteria_friend_value',
        'criteria_friend_ratio',
        'criteria_party_time_value',
        'criteria_party_time_ratio',
        'criteria_party_number_value',
        'criteria_party_number_ratio'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'month' => 'string',
        'ratio' => 'float',
        'criteria_friend_value' => 'integer',
        'criteria_friend_ratio' => 'float',
        'criteria_party_time_value' => 'integer',
        'criteria_party_time_ratio' => 'float',
        'criteria_party_number_value' => 'integer',
        'criteria_party_number_ratio' => 'float',
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
