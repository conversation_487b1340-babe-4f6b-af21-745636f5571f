<?php

namespace App\Eloquent;

use Carbon\Carbon;

/**
 * Class PasswordReset
 * @package App\Eloquent
 *
 * @property string $login_id
 * @property string $token
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class PasswordReset extends Model
{
    public $fillable = [
        'login_id',
        'token'
    ];

    public $casts = [
        'login_id' => 'string',
        'token' => 'string'
    ];
}
