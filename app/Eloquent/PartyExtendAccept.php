<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PartyExtendAccept
 *
 * @package App\Eloquent
 *
 * @property int $party_id
 * @property int $user_id
 * @property int $party_group_id
 * @property int $is_extended
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Party $party
 * @property User $user
 * @property PartyGroup $partyGroup
 */
class PartyExtendAccept extends Model
{
    public $fillable = [
        'user_id',
        'party_group_id',
        'is_extended'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'party_id' => 'integer',
        'user_id' => 'integer',
        'party_group_id' => 'integer',
        'is_extended' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo
     **/
    public function partyGroup()
    {
        return $this->belongsTo(PartyGroup::class, 'party_group_id');
    }
}
