<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class MessageConversation
 *
 * @package App\Eloquent
 *
 * @property User $creator
 * @property Collection $deletedMessageConversations
 * @property Collection $messageParticipants
 * @property Collection $messages
 * @property int $id
 * @property int $creator_id
 * @property string $channel_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 * @property string|Carbon $deleted_at
 */
class MessageConversation extends Model
{
    use SoftDeletes;

    public $fillable = [
        'creator_id',
        'channel_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'creator_id' => 'integer',
        'channel_id' => 'string',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function deletedMessageConversations()
    {
        return $this->belongsToMany(User::class, 'deleted_message_conversations')->withPivot('created_at');
    }

    /**
     * @return BelongsToMany
     **/
    public function messageParticipants()
    {
        return $this->belongsToMany(User::class, 'message_participants','message_conversation_id','user_id')
            ->withPivot(['number_unread_message', 'last_message', 'last_message_at'])->withTimestamps();
    }

    /**
     * @return HasMany
     **/
    public function messages()
    {
        return $this->hasMany(Message::class, 'message_conversation_id');
    }
}
