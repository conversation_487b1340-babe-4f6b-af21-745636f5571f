<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * Class UserReport
 *
 * @package App\Eloquent
 *
 * @property Feedback $feedback
// * @property Party $party
 * @property PartyHistory $partyHistory
 * @property User $reportedUser
 * @property User $user
 * @property int $id
 * @property int $party_id
 * @property int $user_id
 * @property int $reported_user_id
 * @property int $report_div
 * @property int $report_status
 * @property string $time_report_div
 * @property string $content
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 * @property
 */
class UserReport extends Model
{
    public $fillable = [
        'party_id',
        'user_id',
        'reported_user_id',
        'report_div',
        'report_status',
        'time_report_div',
        'content'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_id' => 'integer',
        'user_id' => 'integer',
        'reported_user_id' => 'integer',
        'report_div' => 'integer',
        'report_status' => 'integer',
        'time_report_div' => 'string',
        'content' => 'string'
    ];

    /**
     * @return BelongsTo
     */
    public function party()
    {
        return $this->belongsTo(Party::class,'party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function partyHistory()
    {
        return $this->belongsTo(PartyHistory::class, 'party_id', 'party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function reportedUser()
    {
        return $this->belongsTo(User::class, 'reported_user_id');
    }

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return MorphOne
     */
    public function feedback(): MorphOne
    {
        return $this->morphOne(Feedback::class,'feedbackable','foreign_table','foreign_id');
    }
}
