<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class Party
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $channel_id
 * @property int $record_status
 * @property int $party_type
 * @property int $invite_member_type
 * @property int $group_type
 * @property int $party_status
 * @property int $creator_id
 * @property string|Carbon $match_at
 * @property string|Carbon $start_at
 * @property string|Carbon $expire_at
 * @property string|Carbon $ended_at
 * @property string $party_time
 * @property string $real_party_time
 * @property bool $is_matched_extend
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Collection|UserProfile $userProfile
 * @property Collection|PartyGroup $partyGroups
 * @property User $creator
 * @property Collection|User participants
 * @property Collection|User partyExtendAccepts
 */
class Party extends Model
{
    public $fillable = [
        'channel_id',
        'party_type',
        'invite_member_type',
        'group_type',
        'party_status',
        'creator_id',
        'match_at',
        'start_at',
        'expire_at',
        'ended_at',
        'party_time',
        'real_party_time',
        'is_matched_extend'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'channel_id' => 'string',
        'party_type' => 'integer',
        'invite_member_type' => 'integer',
        'group_type' => 'integer',
        'party_status' => 'integer',
        'creator_id' => 'integer',
        'match_at' => 'datetime',
        'start_at' => 'datetime',
        'expire_at' => 'datetime',
        'ended_at' => 'datetime',
        'party_time' => 'string',
        'real_party_time' => 'string',
        'is_matched_extend' => 'boolean'
    ];

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function participants()
    {
        return $this->belongsToMany(User::class, 'party_participants')//->using(PartyParticipant::class)
            ->withTimestamps()->withPivot(['party_id', 'party_group_id', 'party_participant_status', 'agora_channel_uid',
                'started_at', 'ended_at', 'expire_at', 'agora_token', 'number_extended', 'party_time', 'real_party_time','is_extended', 'extended_at']);
    }

    public function partyExtendAcceptUsers()
    {
        return $this->belongsToMany(User::class, 'party_extend_accepts')->withTimestamps()->withPivot(['is_extended', 'party_group_id']);
    }

    public function partyExtendAccepts(){
        return $this->hasMany(PartyExtendAccept::class, 'party_id');
    }

    /**
     * @return HasOne
     */
    public function userProfile(): HasOne
    {
        return $this->hasOne(UserProfile::class, 'user_id', 'creator_id');
    }

    /**
     * @return HasMany
     **/
    public function pointHistories()
    {
        return $this->hasMany(PointHistory::class, 'party_id');
    }

    /**
     * @return HasMany
     **/
    public function partyGroups()
    {
        return $this->hasMany(PartyGroup::class, 'party_id');
    }

    /**
     * @return HasMany
     **/
    public function partyParticipants()
    {
        return $this->hasMany(PartyParticipant::class, 'party_id');
    }
}
