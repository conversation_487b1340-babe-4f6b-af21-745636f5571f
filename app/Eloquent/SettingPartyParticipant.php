<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * Class SettingPartyParticipant
 *
 * @package App\Eloquent
 *
 * @property int $user_id
 * @property int $setting_party_id
 * @property bool $is_creator
 * @property int $status
 * @property int $setting_party_group_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property SettingPartyGroup $settingPartyGroup
 * @property SettingParty $settingParty
 * @property User $user
 */
class SettingPartyParticipant extends Pivot
{
    public $fillable = [
        'setting_party_id',
        'is_creator',
        'status',
        'setting_party_group_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'setting_party_id' => 'integer',
        'is_creator' => 'boolean',
        'status' => 'integer',
        'setting_party_group_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function settingPartyGroup()
    {
        return $this->belongsTo(SettingPartyGroup::class, 'setting_party_group_id');
    }

    /**
     * @return BelongsTo
     **/
    public function settingParty()
    {
        return $this->belongsTo(SettingParty::class, 'setting_party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
