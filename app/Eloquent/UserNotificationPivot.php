<?php

namespace App;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UserNotificationPivot
 *
 * @package App
 *
 * @property int $user_notification_id
 * @property int $user_id
 * @property bool $is_read_detail
 * @property Carbon|string $created_at
 * @property Carbon|string $updated_at
 *
 */
class UserNotificationPivot extends Model
{
    /**
     * @var array
     */
    protected $fillable = [
        'user_notification_id',
        'user_id',
        'is_read_detail'
    ];
    /**
     * @var array
     */
    protected $casts = [
        'user_notification_id' => 'integer',
        'user_id' => 'integer',
        'is_read_detail' => 'bool'
    ];
}
