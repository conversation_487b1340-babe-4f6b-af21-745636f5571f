<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class UserTicket
 *
 * @package App\Eloquent
 *
 * @property User $user
 * @property Collection $ticketHistories
 * @property int $user_id
 * @property int $ticket
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class UserTicket extends Model
{
    protected $primaryKey = 'user_id';

    public $fillable = [
        'ticket'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'ticket' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return HasMany
     **/
    public function ticketHistories()
    {
        return $this->hasMany(TicketHistory::class, 'user_id');
    }
}
