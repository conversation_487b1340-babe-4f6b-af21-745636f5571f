<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class MonthlyRefundPoint
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $user_id
 * @property int $refund_ratio_id
 * @property int $refund_money
 * @property int $refund_point
 * @property int $total_point
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class MonthlyRefundPoint extends Model
{
    public $fillable = [
        'user_id',
        'refund_ratio_id',
        'refund_money',
        'refund_point',
        'total_point'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'user_id' => 'integer',
        'refund_money' => 'string',
        'refund_point' => 'integer',
        'total_point' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo
     **/
    public function refundRatio()
    {
        return $this->belongsTo(RefundRatio::class, 'refund_ratio_id');
    }
}
