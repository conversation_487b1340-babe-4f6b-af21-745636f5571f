<?php

namespace App\Eloquent\Concerns\Job;

use Illuminate\Database\Eloquent\Builder;

trait HasScope
{
    /**
     * Scope a query to only include records where 'is_public' is true.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeIsPublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope a query to only include records where 'is_instant' is true.
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeIsInstant(Builder $query): Builder
    {
        return $query->where('is_instant', true);
    }

    public function scopeJobOpen(Builder $query): Builder
    {
        return $query->where('expired_at', '>', now())
                     ->where('is_filled', false);
    }

    public function scopeIsRecommended(Builder $query, $isRecommended): Builder
    {
        return $query->where('is_recommended', (bool)$isRecommended);
    }
}
