<?php

namespace App\Eloquent;

use Database\Factories\JobImageFactory;
use Database\Factories\JobLevelFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * Class JobImage
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $job_id
 * @property int $image_id
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class JobImage extends Model
{
    use HasFactory;

    protected $table = 't_job_images';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'job_id',
        'image_id',
    ];

    /**
     * @return BelongsTo
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(RecruitmentJob::class, 'job_id');
    }

    /**
     * @return BelongsTo
     */
    public function image(): BelongsTo
    {
        return $this->belongsTo(StorageFile::class, 'image_id');
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return JobImageFactory::new();
    }
}
