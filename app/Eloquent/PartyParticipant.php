<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PartyParticipant
 *
 * @package App\Eloquent
 *
 * @property int $user_id
 * @property int $party_id
 * @property int $party_group_id
 * @property int $party_participant_status
 * @property string $agora_channel_uid
 * @property string|Carbon $started_at
 * @property string|Carbon $ended_at
 * @property string|Carbon $expire_at
 * @property string $agora_token
 * @property int $number_extended
 * @property string $party_time
 * @property string|Carbon $extended_at
 * @property bool $is_extended
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property Party $party
 * @property PartyGroup $partyGroup
 * @property User $user
 */
class PartyParticipant extends Model
{
    public $fillable = [
        'party_id',
        'party_group_id',
        'party_participant_status',
        'agora_channel_uid',
        'started_at',
        'ended_at',
        'expire_at',
        'agora_token',
        'number_extended',
        'party_time',
        'is_extended',
        'extended_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'party_id' => 'integer',
        'party_group_id' => 'integer',
        'party_participant_status' => 'integer',
        'agora_channel_uid' => 'string',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'expire_at' => 'datetime',
        'agora_token' => 'string',
        'number_extended' => 'integer',
        'is_extended' => 'boolean',
        'extended_at' => 'datetime'
    ];

    /**
     * @return BelongsTo
     **/
    public function party()
    {
        return $this->belongsTo(Party::class, 'party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function partyGroup()
    {
        return $this->belongsTo(PartyGroup::class, 'party_group_id');
    }

    /**
     * @return BelongsTo
     **/
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
