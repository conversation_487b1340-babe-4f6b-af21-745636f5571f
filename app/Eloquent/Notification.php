<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Notification
 *
 * @package App\Eloquent
 *
 * @property Account $creator
 * @property UserNotificationCreator[] $userNotificationCreator
 * @property int $id
 * @property int $notification_div
 * @property string $title
 * @property string $body
 * @property int $send_status
 * @property string|Carbon $send_schedule_at
 * @property string|Carbon $sent_at
 * @property int $creator_id
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 */
class Notification extends Model
{
    public $fillable = [
        'notification_div',
        'title',
        'body',
        'send_status',
        'send_schedule_at',
        'sent_at',
        'creator_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'notification_div' => 'integer',
        'title' => 'string',
        'body' => 'string',
        'send_status' => 'integer',
        'send_schedule_at' => 'datetime',
        'sent_at' => 'datetime',
        'creator_id' => 'integer',
    ];

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(Account::class, 'creator_id');
    }

    /**
     * @return HasMany
     */
    public function userNotificationCreator(): HasMany
    {
        return $this->hasMany(UserNotificationCreator::class,'foreign_id');
    }
}
