<?php

namespace App\Eloquent;

use App\Eloquent\Concerns\Notification\HasRelationship;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * Class Notification
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property string $type
 * @property string $category
 * @property string $title
 * @property string $body
 * @property int $job_id
 * @property string|Carbon $start_notification_at
 * @property string|Carbon $end_notification_at
 * @property string|Carbon $deleted_at
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * @property Job $job
 * @property NotificationUser[] $notificationUsers
 * @property User[] $users
 */
class Notification extends Model
{
    use SoftDeletes, HasRelationship;

    protected $table = 't_notifications';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'type',
        'category',
        'title',
        'body',
        'job_id',
        'start_notification_at',
        'end_notification_at',
    ];
}
