<?php

namespace App\Eloquent;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class SettingPartyGroup
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $setting_party_id
 * @property bool $is_owner_setting_party
 * @property int $creator_id
 * @property int $group_type
 * @property string|Carbon $created_at
 * @property string|Carbon $updated_at
 *
 * relations
 * @property SettingParty $settingParty
 * @property User $creator
 * @property Collection $settingPartyParticipants
 */
class SettingPartyGroup extends Model
{
//    const UPDATED_AT = null;
    public $fillable = [
        'setting_party_id',
        'is_owner_setting_party',
        'creator_id',
        'group_type'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'setting_party_id' => 'integer',
        'is_owner_setting_party' => 'boolean',
        'creator_id' => 'integer',
        'group_type' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function settingParty()
    {
        return $this->belongsTo(SettingParty::class, 'setting_party_id');
    }

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @return HasMany
     **/
    public function settingPartyParticipants()
    {
        return $this->hasMany(SettingPartyParticipant::class, 'setting_party_group_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function participants()
    {
        return $this->belongsToMany(User::class, 'setting_party_participants')->using(SettingPartyParticipant::class)
            ->withTimestamps()->withPivot(['status', 'is_creator', 'setting_party_group_id']);
    }
}
