<?php

namespace App\Eloquent;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Src\Enum\SettingPartyParticipantStatus;
use Src\Enum\SettingPartyStatus;

/**
 * Class SettingParty
 *
 * @package App\Eloquent
 *
 * @property int $id
 * @property int $party_type
 * @property int $invite_member_type
 * @property int $group_type
 * @property int $gender
 * @property int $gender_partner
 * @property int $from_age
 * @property int $to_age
 * @property int $from_age_partner
 * @property int $to_age_partner
 * @property int $status
 * @property string $update_status_at
 * @property int $creator_id
 * @property string|\Carbon\Carbon $created_at
 * @property string|\Carbon\Carbon $updated_at
 *
 * relations
 * @property User $creator
 * @property Collection $participants
 * @property Collection $settingPartyGroups
 */
class SettingParty extends Model
{

    public $fillable = [
        'party_type',
        'invite_member_type',
        'group_type',
        'from_age',
        'to_age',
        'from_age_partner',
        'to_age_partner',
        'gender',
        'gender_partner',
        'status',
        'update_status_at',
        'creator_id'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'party_type' => 'int',
        'invite_member_type' => 'int',
        'group_type' => 'int',
        'from_age' => 'integer',
        'to_age' => 'integer',
        'from_age_partner',
        'to_age_partner',
        'gender' => 'integer',
        'gender_partner' => 'integer',
        'status' => 'integer',
        'update_status_at' => 'datetime',
        'creator_id' => 'integer'
    ];

    /**
     * @return BelongsTo
     **/
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * @return BelongsToMany
     **/
    public function participants()
    {
        return $this->belongsToMany(User::class, 'setting_party_participants')->using(SettingPartyParticipant::class)
            ->withTimestamps()->withPivot(['status', 'is_creator', 'setting_party_group_id']);
    }

    /**
     * @return BelongsToMany
     **/
    public function joinedParticipants()
    {
        return $this->belongsToMany(User::class, 'setting_party_participants')->withTimestamps()
            ->withPivot(['status'])->wherePivot('status', SettingPartyParticipantStatus::JOINED);
    }

    /**
     * @return HasMany
     **/
    public function settingPartyGroups()
    {
        return $this->hasMany(SettingPartyGroup::class, 'setting_party_id');
    }

}
