<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();

        $this->mapWebRoutes();

        //
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        // Admin routing
        Route::prefix('admin')
            ->middleware(['web', 'author:admin'])
            ->namespace('\Src\Domain\Admin\Controllers')
            ->as('admin.')
            ->group(base_path('routes/admin.php'));

        // User routing
        Route::prefix('user')
            ->middleware(['web'])
            ->namespace('\Src\Domain\User\Controllers')
            ->as('user.')
            ->group(base_path('routes/user.php'));

    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
            ->middleware(['api',  'author:user'])
            ->namespace('\Src\Domain\Api\Controllers')
            ->group(base_path('routes/api.php'));
    }
}
