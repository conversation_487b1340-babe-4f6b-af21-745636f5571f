<?php

return [
    'auth' => [
        'login_failed' => [
            'message' => 'Email hoặc mật khẩu không chính xác!',
            'code' => '500',
        ],
        'not_verify_email' => [
            'message' => 'Please verify your email address.',
            'code' => '403',
        ],
        'not_approved' => [
            'message' => 'Please wait for approval admin.',
            'code' => '403',
        ],
    ],
    'prefecture' => [
        'not_found' => [
            'message' => 'Không tìm thấy mã bưu điện.',
            'code' => '404',
        ],
        'required' => [
            'message' => 'Mã bưu điện là bắt buộc.',
            'code' => '400',
        ],
    ],
    'user' => [
        'not_found' => [
            'message' => 'Không tìm thấy người dùng.',
            'code' => '404',
        ],
        'reset_password_exist' => [
            'message' => 'Đường dẫn đặt lại mật khẩu đã được gử<PERSON>, vui lòng kiểm tra email của bạn.',
            'code' => '302'
        ],
        'old_password_not_match' => [
            'message' => 'Mật khẩu mới không được trùng với mật khẩu cũ.',
            'code' => '403'
        ],
        'token_not_found' => [
            'message' => 'Không tìm thấy token.',
            'code' => '404'
        ],
        'token_expired' => [
            'message' => 'Token đã hết hạn.',
            'code' => '410'
        ],
        'birthday_not_match' =>[
            'message' => 'Thông tin đã nhập không hợp lệ.',
            'code' => '403'
        ],
        'link_does_not_exist' => [
            'message' => 'link does not exist',
            'code' => '400'
        ],
    ],
    'job' => [
        'not_found' => [
            'message' => 'Không tìm thấy công việc',
            'code' => '404',
        ],
    ],
    'job_category' => [
        'not_found' => [
            'message' => 'Không tìm thấy danh mục công việc',
            'code' => '404',
        ],
    ],
    'notification' => [
        'not_found' => [
            'message' => 'Notification not found.',
            'code' => '404',
        ],
    ]
];
