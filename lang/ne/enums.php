<?php

use Src\Enums\AccountRole;
use Src\Enums\ApprovalStatus;
use Src\Enums\Boolean;
use Src\Enums\CertificateLevel;
use Src\Enums\DepositType;
use Src\Enums\EmergencyRelation;
use Src\Enums\FAQType;
use Src\Enums\FeeType;
use Src\Enums\FileDiv;
use Src\Enums\Gender;
use Src\Enums\JobType;
use Src\Enums\NotificationType;
use Src\Enums\PeriodType;
use Src\Enums\PolicyType;
use Src\Enums\RecruitmentType;
use Src\Enums\ResidenceCardStatus;
use Src\Enums\UserBankType;
use Src\Enums\UserStatus;

return [
    AccountRole::class => [
        AccountRole::SUPER => 'Super Admin',
        AccountRole::NORMAL => 'Normal Admin',
        AccountRole::READ => 'Read Only',
    ],
    ApprovalStatus::class => [
        ApprovalStatus::WAITING => 'Waiting',
        ApprovalStatus::APPROVED => 'Approved',
        ApprovalStatus::DECLINED => 'Declined',
    ],
    Boolean::class => [
        Boolean::YES => 'Yes',
        Boolean::NO => 'No',
    ],
    DepositType::class => [
        DepositType::NORMAL => 'Normal account',
        DepositType::CURRENT => 'Current account',
        DepositType::FIXED => 'Fixed deposit account',
    ],
    EmergencyRelation::class => [
        EmergencyRelation::FAMILY => 'Family',
        EmergencyRelation::RELATIVE => 'Relative',
        EmergencyRelation::FRIEND => 'Friend',
        EmergencyRelation::SCHOOL => 'School',
    ],
    CertificateLevel::class => [
        CertificateLevel::N1 => 'N1',
        CertificateLevel::N2 => 'N2',
        CertificateLevel::N3 => 'N3',
        CertificateLevel::N4 => 'N4',
        CertificateLevel::N5 => 'N5',
        CertificateLevel::SAME_N1 => 'Same N1',
        CertificateLevel::SAME_N2 => 'Same N2',
        CertificateLevel::SAME_N3 => 'Same N3',
        CertificateLevel::SAME_N4 => 'Same N4',
        CertificateLevel::SAME_N5 => 'Same N5',
    ],
    FeeType::class => [
        FeeType::DAY => 'Day',
        FeeType::HOUR => 'Hour',
    ],
    FileDiv::class => [
        FileDiv::IMAGE => 'Image',
        FileDiv::VIDEO => 'Video',
        FileDiv::DOCUMENT => 'Document',
        FileDiv::OTHER => 'Other',
    ],
    Gender::class => [
        Gender::MALE => 'Male',
        Gender::FEMALE => 'Female',
    ],
    JobType::class => [
        JobType::REGULAR => 'Regular',
        JobType::SPOT => 'Spot',
    ],
    NotificationType::class => [
        NotificationType::PUBLIC => 'Public',
        NotificationType::SYSTEM => 'System',
        NotificationType::JOB => 'Job',
    ],
    RecruitmentType::class => [
        RecruitmentType::ADMIN => 'Admin',
        RecruitmentType::AUTO => 'Auto',
    ],
    UserBankType::class => [
        UserBankType::CASH => 'Cash',
        UserBankType::BANK => 'Bank',
    ],
    UserStatus::class => [
        UserStatus::INITIAL => 'Registered',
        UserStatus::VERIFIED => 'Verified',
        UserStatus::APPROVED => 'Approved',
        UserStatus::DISABLED => 'Disabled',
    ],
    ResidenceCardStatus::class => [
        ResidenceCardStatus::WAITING => 'Waiting',
        ResidenceCardStatus::DECLINED => 'Declined',
        ResidenceCardStatus::APPROVED => 'Approved',
        ResidenceCardStatus::RENEW => 'Renew',
    ],
    PeriodType::class => [
        PeriodType::STUDENT => 'Student',
        PeriodType::FAMILY => 'Family',
        PeriodType::SPECIFIC_ACTIVITY => 'Specific Activity',
        PeriodType::PERMANENT_RESIDENT => 'Permanent Resident',
        PeriodType::SPOUSE_OF_JAPANESE => 'Spouse of Japanese',
        PeriodType::SPOUSE_OF_PERMANENT_RESIDENT => 'Spouse of Permanent Resident',
        PeriodType::LONG_TERM_RESIDENT => 'Long Term Resident',
        PeriodType::OTHER => 'Other',
    ],
    FAQType::class => [
        FAQType::HOW_TO_USE => 'How to use',
        FAQType::REGISTRATION => 'Registration',
        FAQType::SEARCH => 'Search',
        FAQType::APPLY => 'Apply',
        FAQType::CANCEL_TROUBLE => 'Cancel trouble',
        FAQType::REVIEW_PENALTY => 'Review penalty',
        FAQType::PAYMENT => 'Payment',
        FAQType::BUG_CONTACT => 'Bug contact',
        FAQType::ANNOUNCEMENT => 'Announcement',
    ],
    PolicyType::class => [
        PolicyType::TERMS => 'Terms',
        PolicyType::PRIVACY => 'Privacy',
    ]
];
