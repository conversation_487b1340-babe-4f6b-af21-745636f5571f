<?php

return [
    'auth' => [
        'login_failed' => [
            'message' => 'इमेल वा पासवर्ड गलत छ!',
            'code' => '500',
        ],
        'not_verify_email' => [
            'message' => 'Please verify your email address.',
            'code' => '403',
        ],
        'not_approved' => [
            'message' => 'Please wait for approval admin.',
            'code' => '403',
        ],
    ],
    'prefecture' => [
        'not_found' => [
            'message' => 'Zipcode not found.',
            'code' => '404',
        ],
        'required' => [
            'message' => 'Zipcode is required.',
            'code' => '400',
        ],
    ],
    'user' => [
        'not_found' => [
            'message' => 'User not found.',
            'code' => '404',
        ],
        'reset_password_exist' => [
            'message' => 'Link reset password has sent, please check your email.',
            'code' => '302'
        ],
        'old_password_not_match' => [
            'message' => 'Old password not match.',
            'code' => '403'
        ],
        'token_not_found' => [
            'message' => 'Token not found.',
            'code' => '404'
        ],
        'token_expired' => [
            'message' => 'Token expired.',
            'code' => '410'
        ],
        'birthday_not_match' =>[
            'message' => 'The entered information is invalid.',
            'code' => '403'
        ],
        'link_does_not_exist' => [
            'message' => 'link does not exist',
            'code' => '400'
        ],
    ],
    'job' => [
        'not_found' => [
            'message' => 'Job not found.',
            'code' => '404',
        ],
        'unable_to_retrieve_job_information' => [
            'message' => 'Unable to retrieve job information',
            'code' => '500'
        ],
    ],
    'job_category' => [
        'not_found' => [
            'message' => 'Job category not found.',
            'code' => '404',
        ],
    ],
    'notification' => [
        'not_found' => [
            'message' => 'Notification not found.',
            'code' => '404',
        ],
    ]
];
