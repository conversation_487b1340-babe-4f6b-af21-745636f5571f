<?php

namespace Src\Exception;

use Throwable;

class APIRuntimeException extends \RuntimeException
{
    public function __construct(string $path_message, array $data_message = [])
    {
        $message = __($path_message.'.message', $data_message);
        $code = (int)__($path_message.'.code');
//        dd($message, $code, __($path_message.'.code'));
        parent::__construct($message, $code);
    }
}
