<?php

namespace Src\Domain;

use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Src\Traits\Models\Arrayable;

/**
 * Class FormModel
 * @package Src\Domain
 */
class FormModel
{
    use Arrayable;

    /**
     * @param $key
     * @return mixed
     */
    public function getFormValue($key)
    {
        $values = $this->toArray();
        return Arr::get($values, $key);
    }

    /**
     * @return false|string
     */
    public function toFormValue()
    {
        $values = $this->toArrayForVue();
        return json_encode($values);
    }

    /**
     * castFields
     * null
     *
     * param
     * int, float, string, bool, array, date, datetime, int[]
     *
     * @var array ['name' => 'string']
     */
    protected $fields = [];

    /**
     * castFields
     *
     * @param array $input
     * @param null $eloquent
     * @return array
     */
    protected function castFields(array $input, $eloquent = null): array
    {
        foreach ($this->fields as $name => $type) {
            $input[$name] = isset($input[$name]) ? $this->cast($type, $input[$name]) : optional($eloquent)[$name];
        }
        return $input;
    }

    /**
     * cast
     *
     * @param string $type
     * @param $value
     * @return array|bool|Carbon|float|int|string|null
     */
    private function cast(string $type, $value)
    {
        if (null === $value) {
            return null;
        }

        switch ($type) {
            case 'int':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'string':
                return (string)$value;
            case 'bool':
                return (bool)$value;
            case 'array':
                return (array)$value;
            case 'date':
            case 'datetime':
                if ($value instanceof Carbon) {
                    return $value;
                }
                if (is_string($value)) {
                    return Carbon::parse($value);
                }
                return null;
            case 'int[]':
                return array_map(static function ($value) {
                    return (int)$value;
                }, (array)$value);
            case 'file':
                if ($value instanceof UploadedFile) {
                    return $value;
                }
                return null;
            default:
                return null;
        }
    }

}
