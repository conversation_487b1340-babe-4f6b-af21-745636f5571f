<?php

namespace Src\Domain\Api\Models\SettingParty;

use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\FormModel;
use Src\Enum\Age;
use Src\Enum\PartyType;
use Src\Enum\SettingPartyStatus;

/**
 * Class SettingPartyForm
 *
 * @package Src\Domain\Admin\Models\SettingParty
 */
class SettingPartyForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $group_type;

    /**
     * @var int
     */
    protected $from_age_partner;

    /**
     * @var int
     */
    protected $to_age_partner;

    /**
     * @var int
     */
    protected $creator_id;

    /**
     * @var array
     */
    protected $invite_user_ids;

    /**
     * SettingPartyForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->group_type = array_get_int($input, 'group_type');
        $this->from_age_partner = array_get_int($input, 'from_age_partner');
        $this->to_age_partner = array_get_int($input, 'to_age_partner');
        $this->creator_id = array_get_int($input, 'creator_id');
        $this->invite_user_ids = array_get_array($input, 'invite_user_ids');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->creator_id;
    }

    /**
     * @return int
     */
    public function getNumberPerson(): int
    {
        return count($this->getInviteUserIds()) + 1;
    }

    /**
     * @return array
     */
    public function getInviteUserIds(): ?array
    {
        return $this->invite_user_ids ?? [];
    }

    public function createAttributes(AuthorInterface $creator, int $invite_member_type, int $min_age, int $max_age, int $party_type): array
    {
//        dd($party_type,  $this->getGroupType());
        $group_type = $party_type === PartyType::NEW_PARTY ? $this->getGroupType() : ceil((count($this->invite_user_ids) + 1) / 2);
        [$from_age_partner, $to_age_partner] = $this->getFromToAgePartner($party_type);

        return [
            'party_type' => $party_type,
            'group_type' => $group_type,
            'from_age_partner' => $from_age_partner,
            'to_age_partner' => $to_age_partner,
            'status' => SettingPartyStatus::WAITING_USER_JOIN,
            'creator_id' => $creator->getId(),
            'gender' => $creator->getUserProfile()->gender_party,
            'gender_partner' => $creator->getUserProfile()->gender_partner,
            'invite_member_type' => $invite_member_type,
            'update_status_at' => now(),
            'from_age' => $min_age,
            'to_age' => $max_age,
        ];
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        if ($this->group_type) {
            $group_type = $this->group_type;
        } else {
            $group_type = ceil((count($this->invite_user_ids) + 1) / 2);
        }

        return $group_type;
    }

    /**
     * @return array
     */
    public function getFromToAgePartner(int $party_type): array
    {
        $from_age_partner = $party_type === PartyType::NEW_PARTY ? $this->getFromAgePartner() : Age::MIN;
        $to_age_partner = $party_type === PartyType::NEW_PARTY ? $this->getToAgePartner() : Age::MAX;

        return [$from_age_partner, $to_age_partner];
    }

    /**
     * @return int
     */
    public function getFromAgePartner(): int
    {
        return $this->from_age_partner;
    }

    /**
     * @return int
     */
    public function getToAgePartner(): int
    {
        return $this->to_age_partner;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createWithOtherAttributes(AuthorInterface $creator, int $invite_member_type, int $min_age, int $max_age): array
    {
        return [
            'party_type' => PartyType::NEW_PARTY,
            'group_type' => $this->getGroupType(),
            'from_age_partner' => $this->getFromAgePartner(),
            'to_age_partner' => $this->getToAgePartner(),
            'status' => SettingPartyStatus::WAITING_USER_JOIN,
            'creator_id' => $creator->getId(),
            'gender' => $creator->getUserProfile()->gender_party,
            'gender_partner' => $creator->getUserProfile()->gender_partner,
            'invite_member_type' => $invite_member_type,
            'update_status_at' => now(),
            'from_age' => $min_age,
            'to_age' => $max_age,
        ];
    }


}
