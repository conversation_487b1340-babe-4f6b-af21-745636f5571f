<?php

namespace Src\Domain\Api\Models\SettingParty;

use App\Eloquent\SettingParty as EloquentSettingParty;
use Exception;
use Src\Domain\Api\Models\SettingPartyGroup\SettingPartyGroup;
use Src\Domain\Api\Models\User\User;
use Src\Enum\InviteMemberType;

/**
 * Class SettingParty
 *
 * @package Src\Domain\Admin\Models\SettingParty
 */
class SettingParty
{
    /**
     * @var EloquentSettingParty
     */
    private $setting_party;

    /**
     * SettingParty constructor.
     *
     * @param EloquentSettingParty $setting_party
     */
    public function __construct(EloquentSettingParty $setting_party)
    {
        $this->setting_party = $setting_party;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toAllParticipants(): array
    {
        if ($this->setting_party->invite_member_type === InviteMemberType::ONLY_FRIEND) {
            return $this->getParticipantWithFriends();
        } else {
            return $this->getParticipantWithOthers();
        }
    }

    /**
     * getParticipantWithFriends
     *
     * @return array
     */
    function getParticipantWithFriends()
    {
        $participants = [];

        foreach ($this->setting_party->participants as $participant) {
            $participants[] = [
                'user' => User::getUserAndProfile($participant),
                'participant_info' => [
                    "status" => optional($participant->pivot)->status,
                    "is_creator" => optional($participant->pivot)->is_creator,
                ]
            ];
        }

        return [
            'setting_party' => $this->toDetailApiResponse(),
            'participants' => $participants
        ];
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'party_type' => $this->getPartyType(),
            'group_type' => $this->getGroupType(),
            'invite_member_type' => $this->getInviteMemberType(),
            'status' => $this->getStatus(),
            'update_status_at' => $this->getUpdateStatusAt(),
            'from_age' => $this->getFromAge(),
            'to_age' => $this->getToAge(),
            'from_age_partner' => $this->getFromAgePartner(),
            'to_age_partner' => $this->getToAgePartner(),
            'creator_id' => $this->getCreatorId(),
            'gender' => $this->getGender(),
            'gender_partner' => $this->getGenderPartner(),
            'created_at' => $this->getCreatedAt()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->setting_party->id;
    }

    /**
     * @return int
     */
    public function getPartyType(): int
    {
        return $this->setting_party->party_type;
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        return $this->setting_party->group_type;
    }

    /**
     * @return int
     */
    public function getInviteMemberType(): int
    {
        return $this->setting_party->invite_member_type;
    }

    /**
     * @return int
     */
    public function getStatus(): int
    {
        return $this->setting_party->status;
    }

    /**
     * @return string
     */
    public function getUpdateStatusAt(): string
    {
        return $this->setting_party->update_status_at;
    }

    /**
     * @return int
     */
    public function getFromAge(): int
    {
        return $this->setting_party->from_age;
    }

    /**
     * @return int
     */
    public function getToAge(): int
    {
        return $this->setting_party->to_age;
    }

    /**
     * @return int
     */
    public function getFromAgePartner(): int
    {
        return $this->setting_party->from_age_partner;
    }

    /**
     * @return int
     */
    public function getToAgePartner(): int
    {
        return $this->setting_party->to_age_partner;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->setting_party->creator_id;
    }

    /**
     * @return int
     */
    public function getGender(): int
    {
        return $this->setting_party->gender;
    }

    /**
     * @return int
     */
    public function getGenderPartner(): int
    {
        return $this->setting_party->gender_partner;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->setting_party->created_at;
    }

    /**
     * getParticipantWithOthers
     *
     * @return array
     * @throws Exception
     */
    function getParticipantWithOthers()
    {
        $participant_groups = [];

        // Other groups
        foreach ($this->setting_party->settingPartyGroups as $settingPartyGroup) {
            $settingPartyGroupData = (new SettingPartyGroup($settingPartyGroup))->toComponentFormValue();
            $paticipants = [];

            foreach ($settingPartyGroup->participants as $participant) {
                $paticipants[] = [
                    'user' => User::getUserAndProfile($participant),
                    'group_type' => optional($participant->pivot->settingPartyGroup)->group_type,
                    'participant_info' => [
                        "status" => optional($participant->pivot)->status,
                        "is_creator" => optional($participant->pivot)->is_creator,
                    ],
                ];

            }

            $settingPartyGroupData['participants'] = $paticipants;
            $participant_groups[] = $settingPartyGroupData;
        }

        return [
            'setting_party' => $this->toDetailApiResponse(),
            'participant_groups' => $participant_groups
        ];
    }

    /*
    function getParticipantWithOthers(){
         $participant_groups = [];
         $other_groups = [];
         $owner_group = [];

         // Owner group
         foreach ($this->setting_party->participants as $participant) {
             if ($participant->pivot->setting_party_group_id === null) {
                 $owner_group[] = [
                     'user' => User::getUserAndProfile($participant),
                     'participant_info' => [
                         "status" => optional($participant->pivot)->status,
                         "is_creator" => optional($participant->pivot)->is_creator,
                     ],
                     'group_type' => optional($participant->pivot->settingPartyGroup)->group_type,
                 ];
             }
         }

         $participant_groups['owner_group'] = $owner_group;

         // Other groups
         foreach ($this->setting_party->participants as $participant) {
             if ($participant->pivot->setting_party_group_id !== null) {
                 $participant_data = [
                     'user' => User::getUserAndProfile($participant),
                     'participant_info' => [
                         "status" => optional($participant->pivot)->status,
                         "is_creator" => optional($participant->pivot)->is_creator,
                     ],
                     'group_type' => optional($participant->pivot->settingPartyGroup)->group_type,
                 ];

                 $other_groups[$participant->pivot->setting_party_id][] = $participant_data;
             }
         }

         foreach ($other_groups as $other_group) {
             $participant_groups['other_groups'][] = $other_group;
         }

         return [
             'setting_party' => $this->toDetailApiResponse(),
             'participant_groups' => $participant_groups
         ];
     }*/


}
