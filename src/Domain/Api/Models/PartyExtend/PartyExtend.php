<?php

namespace Src\Domain\Api\Models\PartyExtend;

use App\Eloquent\PartyExtend as EloquentPartyExtend;

/**
 * Class PartyExtend
 *
 * @package Src\Domain\Admin\Models\PartyExtend
 */
class PartyExtend
{
    /**
     * @var EloquentPartyExtend
     */
    private $party_extend;

    /**
     * PartyExtend constructor.
     *
     * @param EloquentPartyExtend $party_extend
     */
    public function __construct(EloquentPartyExtend $party_extend)
    {
        $this->party_extend = $party_extend;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'party_id' => $this->getPartyId(),
            'creator_id' => $this->getCreatorId(),
            'use_ticket_type' => $this->getPartyExtendType(),
            'is_partner_accepted' => $this->getIsPartnerAccepted()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party_extend->id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->party_extend->party_id;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->party_extend->creator_id;
    }

    /**
     * @return int
     */
    public function getPartyExtendType(): int
    {
        return $this->party_extend->use_ticket_type;
    }

    /**
     * @return bool
     */
    public function getIsPartnerAccepted(): bool
    {
        return $this->party_extend->is_partner_accepted;
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'party_id' => $this->getPartyId(),
            'creator_id' => $this->getCreatorId(),
            'use_ticket_type' => $this->getPartyExtendType(),
            'is_partner_accepted' => $this->getIsPartnerAccepted()
        ];
    }
}
