<?php

namespace Src\Domain\Api\Models\UserReport;

use Exception;
use Src\Domain\FormModel;
use Src\Enum\ReportDiv;
use Src\Enum\ReportStatus;
use Src\Enum\TimeReportDiv;

/**
 * Class UserReportForm
 *
 * @package Src\Domain\Admin\Models\UserReport
 */
class UserReportForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $report_div;

    /**
     * @var string
     */
    protected $content;

    /**
     * @var int
     */
    protected $reported_user_id;

    /**
     * @var int
     */
    protected $party_id;

    /**
     * @var int
     */
    protected $time_report_div;

    /**
     * UserReportForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->reported_user_id = array_get_int($input, 'reported_user_id');
        $this->party_id = array_get_int($input, 'party_id');
        $this->report_div = array_get_int($input, 'report_div');
        $this->content = array_get_string($input, 'content');
        $this->time_report_div = array_get_int($input, 'time_report_div');
    }

    /**
     * create Attributes
     *
     * @return array
     * @throws Exception
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     * @throws Exception
     */
    private function commonAttributes(): array
    {
        return [
            'report_status' => ReportStatus::UNPROCESSED,
            'reported_user_id' => $this->getReportedUserId(),
            'party_id' => $this->getPartyId(),
            'report_div' => $this->getReportDiv(),
            'report_div_name' => ReportDiv::getDescription($this->getReportDiv()),
            'content' => $this->getContent(),
            'time_report_div' => $this->getTimeReportDiv(),
            'time_report_div_name' => TimeReportDiv::getDescription($this->getTimeReportDiv())
        ];
    }

    /**
     * @return int
     */
    public function getReportedUserId(): int
    {
        return $this->reported_user_id;
    }

    /**
     * @return  int
     */
    public function getPartyId(): int
    {
        return $this->party_id;
    }

    /**
     * @return int
     */
    public function getReportDiv(): int
    {
        return $this->report_div;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @return int
     * @throws Exception
     */
    public function getTimeReportDiv(): int
    {
        return $this->time_report_div;
    }

    /**
     * update Attributes
     *
     * @return array
     * @throws Exception
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
