<?php

namespace Src\Domain\Api\Models\UserReport;

use App\Eloquent\UserReport as EloquentUserReport;

/**
 * Class UserReport
 *
 * @package Src\Domain\Admin\Models\UserReport
 */
class UserReport
{
    /**
     * @var EloquentUserReport
     */
    private $user_report;

    /**
     * UserReport constructor.
     *
     * @param EloquentUserReport $user_report
     */
    public function __construct(EloquentUserReport $user_report)
    {
        $this->user_report = $user_report;
    }

    public function getReportStatus(): int
    {
        return $this->user_report->report_status;
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->user_report->created_at;
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->user_report->updated_at;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'reported_user_id' => $this->getReportedUserId(),
            'party_id' => $this->getPartyId(),
            'time_report_div' => $this->getTimeReportDiv(),
            'report_div' => $this->getReportDiv(),
            'content' => $this->getContent()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->user_report->id;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_report->user_id;
    }

    /**
     * @return int
     */
    public function getReportedUserId(): int
    {
        return $this->user_report->reported_user_id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->user_report->party_id;
    }

    /**
     * @return int
     */
    public function getTimeReportDiv(): int
    {
        return $this->user_report->time_report_div;
    }

    /**
     * @return int
     */
    public function getReportDiv(): int
    {
        return $this->user_report->report_div;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->user_report->content;
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return $this->user_report->toArray();
    }
}
