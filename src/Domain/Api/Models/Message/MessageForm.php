<?php

namespace Src\Domain\Api\Models\Message;

use Src\Domain\FormModel;

/**
 * Class MessageForm
 *
 * @package Src\Domain\Admin\Models\Message
 */
class MessageForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var ?int
     */
    protected $partner_id;

    /**
     * @var string
     */
    protected $message;

    /**
     * MessageForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->partner_id = array_get_int($input, 'partner_id');
        $this->sender_id = array_get_int($input, 'sender_id');
        $this->message = array_get_string($input, 'message');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'user_id' => $this->getPartnerId(),
            'message' => $this->getMessage()
        ];
    }

    /**
     * @return int
     */
    public function getPartnerId(): ?int
    {
        return $this->partner_id;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
