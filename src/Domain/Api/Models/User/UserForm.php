<?php

namespace Src\Domain\Api\Models\User;

use Src\Domain\FormModel;
use Src\Enum\DeviceRegister;
use Src\Enum\LoginIdType;
use Src\Enum\MethodRegister;
use Src\Enum\UserStatus;

/**
 * Class UserForm
 * @package Src\Domain\Api\Models\User
 */
class UserForm extends FormModel
{
    /** @var string|null */
    protected $password;
    /** @var string|null */
    protected $login_id;

    /** @var int */
    protected $login_id_type;

    /** @var int|null */
    protected $mobile_platform;

    /** @var string|null */
    protected $fcm_token;

    /** string */
    protected $verify_code;

    protected $fields = [
        'password' => 'string',
        'login_id' => 'string',
        'login_id_type' => 'int',
        'mobile_platform' => 'int',
        'fcm_token' => 'string',
    ];

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->password = $input['password'];
        $this->login_id = $input['login_id'];
        $this->login_id_type = $input['login_id_type'];
        $this->mobile_platform = $input['mobile_platform'];
        $this->fcm_token = $input['fcm_token'];
        $this->verify_code = array_get_string($input, 'verify_code');
    }

    /**
     * @return string
     */
    public function getVerifyCode(): string
    {
        return $this->verify_code;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'user_status' => UserStatus::INITIAL,
            'password' => bcrypt($this->getPassword()),
            'login_id' => $this->getLoginId(),
            'login_id_type' => $this->getLoginIdType(),
            'mobile_platform' => $this->getMobilePlatform(),
            'device_register' => DeviceRegister::APP,
            'method_register' => $this->getLoginIdType() === LoginIdType::EMAIL ? MethodRegister::EMAIL : MethodRegister::PHONE,
            'is_warning' => false,
            'fcm_token' => $this->getfcmToken(),
            'registered_at' => now()
        ];
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return int
     */
    public function getLoginIdType(): int
    {
        return $this->login_id_type;
    }

    /**
     * @return int|null
     */
    public function getMobilePlatform(): ?int
    {
        return $this->mobile_platform;
    }

    /**
     * @return int|null
     */
    public function getfcmToken(): ?int
    {
        return $this->fcm_token;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'login_id' => $this->getLoginId()
        ];
    }
}
