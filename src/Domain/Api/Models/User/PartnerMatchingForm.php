<?php

namespace Src\Domain\Api\Models\User;

use Src\Domain\FormModel;

/**
 * Class PartnerMatchingForm
 * @package Src\Domain\Api\Models\User
 */
class PartnerMatchingForm extends FormModel
{
    /** @var int */
    protected $min_age;
    /** @var int */
    protected $max_age;

    /**
     * PartnerMatchingForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->min_age = $input['min_age'];
        $this->max_age = $input['max_age'];
    }

    /**
     * getMinAge
     *
     * @return string
     */
    public function getMinAge(): string
    {
        return now()->subYears($this->max_age)->format("Y");
    }

    /**
     * getMaxAge
     *
     * @return string
     */
    public function getMaxAge(): string
    {
        return now()->subYears($this->min_age)->format("Y");
    }
}
