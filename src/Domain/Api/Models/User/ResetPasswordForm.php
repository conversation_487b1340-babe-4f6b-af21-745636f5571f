<?php


namespace Src\Domain\Api\Models\User;

use Src\Domain\FormModel;

class ResetPasswordForm extends FormModel
{
    protected $login_id;

    protected $fields = [
        'login_id' => 'string'
    ];

    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->login_id = $input['login_id'];
    }

    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    private function commonAttributes(): array
    {
        return [
            'login_id' => $this->getLoginId()
        ];
    }

    public function getLoginId(): ?string
    {
        return $this->login_id;
    }
}
