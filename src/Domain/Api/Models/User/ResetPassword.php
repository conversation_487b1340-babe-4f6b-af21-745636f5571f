<?php

namespace Src\Domain\Api\Models\User;

use App\Eloquent\PasswordReset as EloquentPasswordReset;

class ResetPassword
{
    /**
     * @var EloquentPasswordReset
     */
    private $password_reset;

    public function __construct(EloquentPasswordReset $password_reset)
    {
        $this->password_reset = $password_reset;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'login_id' => $this->getLoginId(),
            'token' => $this->getToken()
        ];
    }

    public function getLoginId(): ?string
    {
        return $this->password_reset->login_id;
    }

    public function getToken(): ?string
    {
        return $this->password_reset->token;
    }
}
