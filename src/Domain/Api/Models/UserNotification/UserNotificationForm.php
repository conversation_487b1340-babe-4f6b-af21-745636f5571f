<?php

namespace Src\Domain\Api\Models\UserNotification;

use Carbon\Carbon;
use Src\Domain\FormModel;

/**
 * Class UserNotificationForm
 *
 * @package Src\Domain\Admin\Models\UserNotification
 */
class UserNotificationForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $user_id;

    /**
     * @var int
     */
    protected $notification_div;

    /**
     * @var string
     */
    protected $foreign_table;

    /**
     * @var int
     */
    protected $foreign_id;

    /**
     * @var int
     */
    protected $party_id;

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string
     */
    protected $body;

    /**
     * @var string
     */
    protected $data;

    /**
     * @var Carbon
     */
    protected $notification_at;

    /**
     * @var int
     */
    protected $is_read_detail;

    /**
     * UserNotificationForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->user_id = array_get_int($input, 'user_id');
        $this->notification_div = array_get_int($input, 'notification_div');
        $this->foreign_table = array_get_string($input, 'foreign_table');
        $this->foreign_id = array_get_int($input, 'foreign_id');
        $this->party_id = array_get_int($input, 'party_id');
        $this->title = array_get_string($input, 'title');
        $this->body = array_get_string($input, 'body');
        $this->data = array_get_string($input, 'data');
        $this->notification_at = array_get_carbon($input, 'notification_at');
        $this->is_read_detail = array_get_int($input, 'is_read_detail');
    }

    /**
     * createNotificationAttributes
     *
     * @param int $notification_div
     * @param $foreign_table
     * @param $foreign_id
     * @param $notify_payload
     * @param $notify_data
     * @param null $party_id
     * @param bool $has_action
     * @return array
     */
    public static function createNotificationAttributes(int $notification_div, $foreign_table, $foreign_id, $notify_payload, $notify_data, $party_id = null, $has_action = false): array
    {
        return [
            'notification_div' => $notification_div,
            'foreign_table' => $foreign_table,
            'foreign_id' => $foreign_id,
            'party_id' => $party_id,
            'title' => $notify_payload['title'],
            'body' => $notify_payload['body'],
            'data' => json_encode($notify_data),
            'notification_at' => now(),
            'has_action' => $has_action,
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'notification_div' => $this->getNotificationDiv(),
            'foreign_table' => $this->getForeignTable(),
            'foreign_id' => $this->getForeignId(),
            'party_id' => $this->getPartyId(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'data' => $this->getData(),
            'notification_at' => $this->getNotificationAt(),
            'is_read_detail' => $this->getIsReadDetail()
        ];
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * @return int
     */
    public function getNotificationDiv(): int
    {
        return $this->notification_div;
    }

    /**
     * @return string|null
     */
    public function getForeignTable(): ?string
    {
        return $this->foreign_table;
    }

    /**
     * @return int|null
     */
    public function getForeignId(): ?int
    {
        return $this->foreign_id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->party_id;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    /**
     * @return string|null
     */
    public function getData(): ?string
    {
        return $this->data;
    }

    /**
     * @return Carbon
     */
    public function getNotificationAt(): Carbon
    {
        return $this->notification_at;
    }

    /**
     * @return int
     */
    public function getIsReadDetail(): int
    {
        return $this->is_read_detail;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }
}
