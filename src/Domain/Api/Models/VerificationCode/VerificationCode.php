<?php

namespace Src\Domain\Api\Models\VerificationCode;

use App\Eloquent\VerificationCode as EloquentVerificationCode;

/**
 * Class VerificationCode
 *
 * @package Src\Domain\Admin\Models\VerificationCode
 */
class VerificationCode
{
    /**
     * @var EloquentVerificationCode
     */
    private $verification_code;

    /**
     * VerificationCode constructor.
     *
     * @param EloquentVerificationCode $verification_code
     */
    public function __construct(EloquentVerificationCode $verification_code)
    {
        $this->verification_code = $verification_code;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'verify_code' => $this->getVerifyCode()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->verification_code->id;
    }

    /**
     * @return string
     */
    public function getVerifyCode(): string
    {
        return $this->verification_code->verify_code;
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'verify_code' => $this->getVerifyCode()
        ];
    }
}
