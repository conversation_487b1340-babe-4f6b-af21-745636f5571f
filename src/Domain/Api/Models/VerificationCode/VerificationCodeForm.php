<?php

namespace Src\Domain\Api\Models\VerificationCode;

use Src\Domain\FormModel;

/**
 * Class VerificationCodeForm
 *
 * @package Src\Domain\Admin\Models\VerificationCode
 */
class VerificationCodeForm extends FormModel
{

    /**
     * @var string
     */
    protected $login_id;

    /**
     * @var int
     */
    protected $login_id_type;

    /**
     * VerificationCodeForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->login_id = array_get_string($input, 'login_id');
        $this->login_id_type = array_get_int($input, 'login_id_type');
    }

    /**
     * create Attributes
     *
     * @param string $verify_code
     * @return array
     */
    public function createAttributes(string $verify_code): array
    {
        return [
            'verify_code' => $verify_code,
            'login_id' => $this->getLoginId(),
            'login_id_type' => $this->getLoginIdType()
        ];
    }

    /**
     * @return string
     */
    public function getLoginId(): string
    {
        return $this->login_id;
    }

    /**
     * @return int
     */
    public function getLoginIdType(): int
    {
        return $this->login_id_type;
    }


}
