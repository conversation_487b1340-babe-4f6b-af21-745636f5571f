<?php

namespace Src\Domain\Api\Models\SettingPartyGroup;

use App\Eloquent\SettingPartyGroup as EloquentSettingPartyGroup;
use Src\Domain\Model;

/**
 * Class SettingPartyGroup
 *
 * @package Src\Domain\Admin\Models\SettingPartyGroup
 */
class SettingPartyGroup extends Model
{
    /**
     * @var EloquentSettingPartyGroup
     */
    private $setting_party_group;

    /**
     * SettingPartyGroup constructor.
     *
     * @param EloquentSettingPartyGroup $setting_party_group
     */
    public function __construct(EloquentSettingPartyGroup $setting_party_group)
    {
        $this->setting_party_group = $setting_party_group;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'setting_party_id' => $this->getSettingPartyId(),
            'is_owner_setting_party' => $this->getIsOwnerSettingParty(),
            'creator_id' => $this->getCreatorId(),
            'group_type' => $this->getGroupType(),
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->setting_party_group->id;
    }

    /**
     * @return int|null
     */
    public function getSettingPartyId(): ?int
    {
        return $this->setting_party_group->setting_party_id;
    }

    /**
     * @return bool
     */
    public function getIsOwnerSettingParty(): bool
    {
        return $this->setting_party_group->is_owner_setting_party;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->setting_party_group->creator_id;
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        return $this->setting_party_group->group_type;
    }
}
