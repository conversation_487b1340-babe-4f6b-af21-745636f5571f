<?php

namespace Src\Domain\Api\Models\UserContact;

use App\Eloquent\UserContact as EloquentUserContact;

/**
 * Class UserContact
 *
 * @package Src\Domain\Admin\Models\UserContact
 */
class UserContact
{
    /**
     * @var EloquentUserContact
     */
    private $user_contact;

    /**
     * UserContact constructor.
     *
     * @param EloquentUserContact $user_contact
     */
    public function __construct(EloquentUserContact $user_contact)
    {
        $this->user_contact = $user_contact;
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->user_contact->created_at;
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->user_contact->updated_at;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'contact_div' => $this->getContactDiv(),
            'body' => $this->getBody()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->user_contact->id;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_contact->user_id;
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->user_contact->contact_div;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->user_contact->body;
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'contact_div' => $this->getContactDiv(),
            'body' => $this->getBody()
        ];
    }
}
