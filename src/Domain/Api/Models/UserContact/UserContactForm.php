<?php

namespace Src\Domain\Api\Models\UserContact;

use Src\Domain\FormModel;
use Src\Enum\ContactState;

/**
 * Class UserContactForm
 *
 * @package Src\Domain\Admin\Models\UserContact
 */
class UserContactForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $contact_div;

    /**
     * @var string
     */
    protected $body;

    /**
     * UserContactForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->contact_div = array_get_int($input, 'contact_div');
        $this->body = array_get_string($input, 'body');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getContactState(): string
    {
        return ContactState::getDescription(ContactState::NOT_SUPPORTED);
    }

    /**
     * create Attributes
     *
     * @param int $state
     * @return array
     */
    public function createAttributes(int $state): array
    {
        return $this->commonAttributes($state);
    }

    /**
     * common Attributes
     *
     * @param int $state
     * @return array
     */
    private function commonAttributes(int $state): array
    {
        return [
            'contact_div' => $this->getContactDiv(),
            'state' => $state,
            'body' => $this->getBody()
        ];
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->contact_div;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    /**
     * update Attributes
     *
     * @param int $state
     * @return array
     */
    public function updateAttributes(int $state): array
    {
        return $this->commonAttributes($state);
    }

}
