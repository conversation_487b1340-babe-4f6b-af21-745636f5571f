<?php

namespace Src\Domain\Api\Models\PointHistory;

use App\Eloquent\PointHistory as EloquentPointHistory;
use Exception;
use Src\Domain\Api\Models\User\User as UserDetail;

/**
 * Class PointHistory
 *
 * @package Src\Domain\Admin\Models\PointHistory
 */
class PointHistory
{
    /**
     * @var EloquentPointHistory
     */
    private $point_history;

    /**
     * PointHistory constructor.
     *
     * @param EloquentPointHistory $point_history
     */
    public function __construct(EloquentPointHistory $point_history)
    {
        $this->point_history = $point_history;
    }

    /**
     * @return int|null
     */
    public function getReceiveUserId(): ?int
    {
        return $this->point_history->receive_user_id;
    }

    /**
     * @return int|null
     */
    public function getPresentUserId(): ?int
    {
        return $this->point_history->present_user_id;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'point_update_div' => $this->getPointUpdateDiv(),
            'point_plan_id' => $this->getPointPlanId(),
            'party_id' => $this->getPartyId(),
            'point' => $this->getPoint(),
            'money' => $this->getMoney(),
            'old_point' => $this->getOldPoint(),
            'new_point' => $this->getNewPoint(),
            'point_at' => $this->getPointAt(),
            'present_user' => $this->getPresentUser(),
            'receive_user' => $this->getReceiveUser(),
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->point_history->id;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->point_history->user_id;
    }

    /**
     * @return int
     */
    public function getPointUpdateDiv(): int
    {
        return $this->point_history->point_update_div;
    }

    /**
     * @return int|null
     */
    public function getPointPlanId(): ?int
    {
        return $this->point_history->point_plan_id;
    }

    /**
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        return $this->point_history->party_id;
    }

    /**
     * @return int
     */
    public function getPoint(): int
    {
        return $this->point_history->point;
    }

    /**
     * @return int|null
     */
    public function getMoney(): ?int
    {
        return $this->point_history->money;
    }

    /**
     * @return int
     */
    public function getOldPoint(): int
    {
        return $this->point_history->old_point;
    }

    /**
     * @return int
     */
    public function getNewPoint(): int
    {
        return $this->point_history->new_point;
    }

    /**
     * @return string
     */
    public function getPointAt(): string
    {
        return $this->point_history->point_at;
    }

    /**
     * @return array|null
     * @throws Exception
     */
    public function getPresentUser(): ?array
    {
        return UserDetail::getUserAndProfile($this->point_history->presentUser);
    }

    /**
     * @return array|null
     * @throws Exception
     */
    public function getReceiveUser(): ?array
    {
        return UserDetail::getUserAndProfile($this->point_history->receiveUser);
    }

}
