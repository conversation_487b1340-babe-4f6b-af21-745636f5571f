<?php

namespace Src\Domain\Api\Models\PointHistory;

use Carbon\Carbon;
use Src\Domain\FormModel;

/**
 * Class PointHistoryForm
 *
 * @package Src\Domain\Admin\Models\PointHistory
 */
class PointHistoryForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $user_id;

    /**
     * @var int
     */
    protected $point_update_div;

    /**
     * @var int
     */
    protected $point_plan_id;

    /**
     * @var int
     */
    protected $party_id;

    /**
     * @var int
     */
    protected $point;

    /**
     * @var int
     */
    protected $money;

    /**
     * @var int
     */
    protected $old_point;

    /**
     * @var int
     */
    protected $new_point;

    /**
     * @var Carbon
     */
    protected $point_at;

    /**
     * PointHistoryForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->user_id = array_get_int($input, 'user_id');
        $this->point_update_div = array_get_int($input, 'point_update_div');
        $this->point_plan_id = array_get_int($input, 'point_plan_id');
        $this->party_id = array_get_int($input, 'party_id');
        $this->point = array_get_int($input, 'point');
        $this->money = array_get_int($input, 'money');
        $this->old_point = array_get_int($input, 'old_point');
        $this->new_point = array_get_int($input, 'new_point');
        $this->point_at = array_get_carbon($input, 'point_at');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'point_update_div' => $this->getPointUpdateDiv(),
            'point_plan_id' => $this->getPointPlanId(),
            'party_id' => $this->getPartyId(),
            'point' => $this->getPoint(),
            'money' => $this->getMoney(),
            'old_point' => $this->getOldPoint(),
            'new_point' => $this->getNewPoint(),
            'point_at' => $this->getPointAt()
        ];
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * @return int
     */
    public function getPointUpdateDiv(): int
    {
        return $this->point_update_div;
    }

    /**
     * @return int
     */
    public function getPointPlanId(): int
    {
        return $this->point_plan_id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->party_id;
    }

    /**
     * @return int
     */
    public function getPoint(): int
    {
        return $this->point;
    }

    /**
     * @return int
     */
    public function getMoney(): int
    {
        return $this->money;
    }

    /**
     * @return int
     */
    public function getOldPoint(): int
    {
        return $this->old_point;
    }

    /**
     * @return int
     */
    public function getNewPoint(): int
    {
        return $this->new_point;
    }

    /**
     * @return Carbon
     */
    public function getPointAt(): Carbon
    {
        return $this->point_at;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
