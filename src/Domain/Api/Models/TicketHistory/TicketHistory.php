<?php

namespace Src\Domain\Api\Models\TicketHistory;

use App\Eloquent\TicketHistory as EloquentTicketHistory;

/**
 * Class TicketHistory
 *
 * @package Src\Domain\Admin\Models\TicketHistory
 */
class TicketHistory
{
    /**
     * @var EloquentTicketHistory
     */
    private $ticket_history;

    /**
     * TicketHistory constructor.
     *
     * @param EloquentTicketHistory $ticket_history
     */
    public function __construct(EloquentTicketHistory $ticket_history)
    {
        $this->ticket_history = $ticket_history;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'ticket_plan_div' => $this->getTicketPlanDiv(),
            'ticket_update_div' => $this->getTicketUpdateDiv(),
            'buy_situation' => $this->getBuySituation(),
            'party_id' => $this->getPartyId(),
            'ticket_plan_id' => $this->getTicketPlanId(),
            'name' => $this->getName(),
            'money' => $this->getMoney(),
            'normal_money' => $this->getNormalMoney(),
            'ticket' => $this->getTicket(),
            'time_hour' => $this->getTimeHour(),
            'time_minute' => $this->getTimeMinute(),
            'old_ticket' => $this->getOldTicket(),
            'new_ticket' => $this->getNewTicket(),
            'ticket_at' => $this->getTicketAt()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->ticket_history->id;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->ticket_history->user_id;
    }

    /**
     * @return int
     */
    public function getTicketPlanDiv(): int
    {
        return $this->ticket_history->ticket_plan_div;
    }

    /**
     * @return int
     */
    public function getTicketUpdateDiv(): int
    {
        return $this->ticket_history->ticket_update_div;
    }

    /**
     * @return int|null
     */
    public function getBuySituation(): ?int
    {
        return $this->ticket_history->buy_situation;
    }

    /**
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        return $this->ticket_history->party_id;
    }

    /**
     * @return int
     */
    public function getTicketPlanId(): int
    {
        return $this->ticket_history->ticket_plan_id;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->ticket_history->name;
    }

    /**
     * @return int
     */
    public function getMoney(): int
    {
        return $this->ticket_history->money;
    }

    /**
     * @return int
     */
    public function getNormalMoney(): int
    {
        return $this->ticket_history->normal_money;
    }

    /**
     * @return int
     */
    public function getTicket(): int
    {
        return $this->ticket_history->ticket;
    }

    /**
     * @return int
     */
    public function getTimeHour(): int
    {
        return $this->ticket_history->time_hour;
    }

    /**
     * @return int
     */
    public function getTimeMinute(): int
    {
        return $this->ticket_history->time_minute;
    }

    /**
     * @return int
     */
    public function getOldTicket(): int
    {
        return $this->ticket_history->old_ticket;
    }

    /**
     * @return int
     */
    public function getNewTicket(): int
    {
        return $this->ticket_history->new_ticket;
    }

    /**
     * @return string
     */
    public function getTicketAt(): string
    {
        return $this->ticket_history->ticket_at;
    }

}
