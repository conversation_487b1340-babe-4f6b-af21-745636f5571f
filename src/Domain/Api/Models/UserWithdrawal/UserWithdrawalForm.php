<?php

namespace Src\Domain\Api\Models\UserWithdrawal;

use Src\Domain\FormModel;

/**
 * Class UserWithdrawalForm
 *
 * @package Src\Domain\Admin\Models\UserWithdrawal
 */
class UserWithdrawalForm extends FormModel
{

    /**
     * @var int
     */
    protected $reason_div;


    /**
     * UserWithdrawalForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->reason_div = array_get_int($input, 'reason_div');
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'reason_div' => $this->getReasonDiv(),
            'withdrawal_at' => now()
        ];
    }

    /**
     * @return int
     */
    public function getReasonDiv(): int
    {
        return $this->reason_div;
    }

}
