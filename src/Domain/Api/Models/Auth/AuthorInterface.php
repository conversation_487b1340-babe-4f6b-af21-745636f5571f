<?php

namespace Src\Domain\Api\Models\Auth;

use App\Eloquent\User;
use App\Eloquent\UserProfile;
use Laravel\Passport\Token;

/**
 * Interface AuthorInterface
 * @package Src\Domain\Api\Models\Auth
 */
interface AuthorInterface
{
    /**
     * AuthorInterface constructor.
     * @param User $user
     */
    public function __construct(User $user);

    /**
     * @return string
     */
    public function getId(): int;

    /**
     * @return string
     */
    public function getLoginId(): string;

    /**
     * @return null|UserProfile
     */
    public function getUserProfile();

    /**
     * @return int
     */
    public function getGenderPartner(): int;

    /**
     * @return int
     */
    public function getGenderParty(): int;

    /**
     * @return null|Token
     */
    public function getToken(): ?Token;

    /**
     * @return null|Token
     */
    public function getfcmToken(): ?string;

    /**
     * @return int
     */
    public function getPoint(): int;

    /**
     * @return int
     */
    public function getTicket(): int;

    /**
     * @return User
     */
    public function getUser(): User;

}

