<?php

namespace Src\Domain\Api\Models\Auth\Form;

use Src\Domain\FormModel;

/**
 * Class LoginSocialForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class LoginSocialForm extends FormModel
{
    protected $social_token;
    protected $fcm_token;
    protected $mobile_platform;

    /**
     * @var array
     */
    protected $fields = [
        'social_token' => 'string',
        'password' => 'string',
        'fcm_token' => 'string',
        'mobile_platform' => 'string',
    ];

    /**
     * LoginSocialForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $input = $this->castFields($input);
        $this->social_token = $input['social_token'];
        $this->fcm_token = $input['fcm_token'];
        $this->mobile_platform = $input['mobile_platform'];
    }

    /**
     * @return string|null
     */
    public function getSocialToken(): ?string
    {
        return $this->social_token;
    }

    /**
     * @return array
     */
    public function updateDeviceAttributes(): array
    {
        if ($this->getFCMToken()) {
            return [
                'fcm_token' => $this->getFCMToken(),
                'mobile_platform' => $this->getMobilePlatform()
            ];
        }
        return [
            'mobile_platform' => $this->getMobilePlatform()
        ];

    }

    /**
     * @return string|null
     */
    public function getFCMToken(): ?string
    {
        return $this->fcm_token;
    }

    /**
     * @return string|null
     */
    public function getMobilePlatform(): ?string
    {
        return $this->mobile_platform;
    }
}
