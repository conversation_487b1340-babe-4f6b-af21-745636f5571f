<?php

namespace Src\Domain\Api\Models\Auth\Form;

use Src\Domain\FormModel;

/**
 * Class LoginForm
 * @package Src\Domain\Api\Models\Auth\Form
 */
class LoginForm extends FormModel
{
    protected $login_id;
    protected $password;
    protected $fcm_token;
    protected $voip_token;
    protected $mobile_platform;

    /**
     * @var array
     */
    protected $fields = [
        'login_id' => 'string',
        'password' => 'string',
        'fcm_token' => 'string',
        'mobile_platform' => 'int',
    ];

    /**
     * LoginForm constructor.
     *
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $input = $this->castFields($input);
        $this->login_id = $input['login_id'];
        $this->password = $input['password'];
        $this->fcm_token = $input['fcm_token'];
        $this->voip_token = array_get_string($input, 'voip_token');
        $this->mobile_platform = $input['mobile_platform'];
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return string
     */
    public function getVoipToken(): string
    {
        return $this->voip_token;
    }

    /**
     * @return array
     */
    public function updateDeviceAttributes(): array
    {
        $attributes = [
            'mobile_platform' => $this->getMobilePlatform()
        ];

        if ($this->getfcmToken()) {
            $attributes['fcm_token'] = $this->getfcmToken();
        }

        return $attributes;
    }

    /**
     * @return int
     */
    public function getMobilePlatform(): int
    {
        return $this->mobile_platform;
    }

    /**
     * @return string|null
     */
    public function getfcmToken(): ?string
    {
        return $this->fcm_token;
    }

}
