<?php

namespace Src\Domain\Api\Models\PartyHistory;

use App\Eloquent\PartyHistory as EloquentPartyHistory;
use Src\Domain\Api\Models\PartyHistoryGroup\PartyHistoryGroup;
use Src\Domain\Api\Models\User\User;
use Src\Domain\Model;

/**
 * Class PartyHistory
 *
 * @package Src\Domain\Admin\Models\PartyHistory
 */
class PartyHistory extends Model
{
    /**
     * @var EloquentPartyHistory
     */
    private $party_history;

    /**
     * PartyHistory constructor.
     *
     * @param EloquentPartyHistory $party_history
     */
    public function __construct(EloquentPartyHistory $party_history)
    {
        $this->party_history = $party_history;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    function getDetailPartyHistory()
    {
        $participant_groups = [];
        foreach ($this->party_history->partyHistoryGroups as $partyHistoryGroup) {
            $settingPartyGroupData = (new PartyHistoryGroup($partyHistoryGroup))->toComponentFormValue();
            $participants = [];

            foreach ($partyHistoryGroup->participants as $participant) {
                $participants[] = [
                    'user' => User::getUserAndProfile($participant),
                ];
            }

            $settingPartyGroupData['participants'] = $participants;
            $participant_groups[] = $settingPartyGroupData;
        }
        $sorted_groups = collect($participant_groups)->sortBy('gender');

        return [
            'party' => $this->toDetailApiResponse(),
            'participant_group_male' => $sorted_groups[0],
            'participant_group_female' => $sorted_groups[1]
        ];
    }

    /**
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'party_id' => $this->getPartyId(),
            'party_type' => $this->getPartyType(),
            'invite_member_type' => $this->getInviteMemberType(),
            'group_type' => $this->getGroupType(),
            'creator_id' => $this->getCreatorId(),
            'match_at' => $this->getMatchAt(),
            'start_at' => $this->getStartAt(),
            'expire_at' => $this->getExpireAt(),
            'ended_at' => $this->getEndedAt(),
            'party_time' => $this->getPartyTime(),
            'real_party_time' => $this->getRealPartyTime()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party_history->id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->party_history->party_id;
    }

    /**
     * @return int
     */
    public function getPartyType(): int
    {
        return $this->party_history->party_type;
    }

    /**
     * @return int
     */
    public function getInviteMemberType(): int
    {
        return $this->party_history->invite_member_type;
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        return $this->party_history->group_type;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->party_history->creator_id;
    }

    /**
     * @return string
     */
    public function getMatchAt(): string
    {
        return $this->party_history->match_at;
    }

    /**
     * @return string|null
     */
    public function getStartAt(): ?string
    {
        return $this->party_history->start_at;
    }

    /**
     * @return string|null
     */
    public function getExpireAt(): ?string
    {
        return $this->party_history->expire_at;
    }

    /**
     * @return string|null
     */
    public function getEndedAt(): ?string
    {
        return $this->party_history->ended_at;
    }

    /**
     * @return string|null
     */
    public function getPartyTime(): ?string
    {
        return $this->party_history->party_time;
    }

    /**
     * @return string|null
     */
    public function getRealPartyTime(): ?string
    {
        return $this->party_history->real_party_time;
    }

    function getPartyHistories()
    {
        $participant_groups = [];
        foreach ($this->party_history->partyHistoryGroups as $partyHistoryGroup) {
            $settingPartyGroupData = $partyHistoryGroup->only(['id', 'gender']);
            $participants = [];
            foreach ($partyHistoryGroup->participants as $participant) {
                $participants[] = $participant->userProfile->only(['user_id', 'nickname']);
            }

            $settingPartyGroupData['participants'] = $participants;
            $participant_groups[] = $settingPartyGroupData;
        }

        $sorted_groups = collect($participant_groups)->sortBy('gender');

        return [
            'party' => $this->toDetailApiResponse(),
            'participant_group_male' => $sorted_groups[0],
            'participant_group_female' => $sorted_groups[1]
        ];
    }

}
