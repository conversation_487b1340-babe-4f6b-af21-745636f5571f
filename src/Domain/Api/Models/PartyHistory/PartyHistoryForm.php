<?php

namespace Src\Domain\Api\Models\PartyHistory;

use App\Eloquent\Party;
use App\Eloquent\PartyGroup;
use Src\Domain\FormModel;

/**
 * Class PartyHistoryForm
 *
 * @package Src\Domain\Admin\Models\PartyHistory
 */
class PartyHistoryForm extends FormModel
{

    //===================== Create Attribute =============
    /**
     * create Attributes
     *
     * @return array
     */
    public static function createAttributes(Party $party): array
    {
        return [
            'party_id' => $party->id,
            'channel_id' => $party->channel_id,
            'party_type' => $party->party_type,
            'party_status' => $party->party_status,
            'invite_member_type' => $party->invite_member_type,
            'group_type' => $party->group_type,
            'creator_id' => $party->creator_id,
            'match_at' => $party->match_at,
            'start_at' => $party->start_at,
            'expire_at' => $party->expire_at,
            'ended_at' => $party->ended_at,
            'party_time' => $party->party_time,
            'real_party_time' => $party->real_party_time
        ];
    }

    public static function partyHistoryGroupAttributes(PartyGroup $party_group, $party_history_id): array
    {
        return [
            'party_history_id' => $party_history_id,
            'gender' => $party_group->gender,
            'gender_partner' => $party_group->gender_partner,
            'from_age' => $party_group->from_age,
            'to_age' => $party_group->to_age,
            'from_age_partner' => $party_group->from_age_partner,
            'to_age_partner' => $party_group->to_age_partner
        ];
    }

    public static function partyHistoryParticipantAttributes($participant, $party_history_group_id): array
    {
//        dd_with_format($participant->toArray());
        return [
            'party_history_group_id' => $party_history_group_id,
            'party_participant_status' => $participant->party_participant_status,
            'started_at' => $participant->started_at,
            'ended_at' => $participant->ended_at,
            'expire_at' => $participant->expire_at,
            'number_extended' => $participant->number_extended,
            'party_time' => $participant->party_time,
            'real_party_time' => $participant->real_party_time,
        ];
    }

    //===================== Update Attribute =============

    /**
     * create Attributes
     *
     * @return array
     */
    public static function updateAttributes(Party $party): array
    {
        return [
            'party_status' => $party->party_status,
            'expire_at' => $party->expire_at,
            'ended_at' => $party->ended_at,
            'party_time' => $party->party_time,
            'real_party_time' => $party->real_party_time
        ];
    }

    public static function updatePartyHistoryParticipantAttributes($participant): array
    {
        return [
            'party_participant_status' => $participant->pivot->party_participant_status,
            'started_at' => $participant->pivot->started_at,
            'ended_at' => $participant->pivot->ended_at,
            'expire_at' => $participant->pivot->expire_at,
            'number_extended' => $participant->pivot->number_extended,
            'party_time' => $participant->pivot->party_time,
            'real_party_time' => $participant->pivot->real_party_time,
        ];
    }

}
