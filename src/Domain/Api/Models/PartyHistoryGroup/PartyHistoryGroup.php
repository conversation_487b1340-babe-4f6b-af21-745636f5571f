<?php

namespace Src\Domain\Api\Models\PartyHistoryGroup;

use App\Eloquent\PartyHistoryGroup as EloquentPartyHistoryGroup;
use Src\Domain\Model;

/**
 * Class PartyHistoryGroup
 *
 * @package Src\Domain\Admin\Models\PartyHistoryGroup
 */
class PartyHistoryGroup extends Model
{
    /**
     * @var EloquentPartyHistoryGroup
     */
    private $party_history_group;

    /**
     * PartyHistoryGroup constructor.
     *
     * @param EloquentPartyHistoryGroup $party_history_group
     */
    public function __construct(EloquentPartyHistoryGroup $party_history_group)
    {
        $this->party_history_group = $party_history_group;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'party_id' => $this->getPartyHistoryId(),
            'gender' => $this->getGender(),
            'gender_partner' => $this->getGenderPartner(),
            'from_age' => $this->getFromAge(),
            'to_age' => $this->getToAge(),
            'from_age_partner' => $this->getFromAgePartner(),
            'to_age_partner' => $this->getToAgePartner()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party_history_group->id;
    }

    /**
     * @return int
     */
    public function getPartyHistoryId(): int
    {
        return $this->party_history_group->party_history_id;
    }

    /**
     * @return int
     */
    public function getGender(): int
    {
        return $this->party_history_group->gender;
    }

    /**
     * @return int
     */
    public function getGenderPartner(): int
    {
        return $this->party_history_group->gender_partner;
    }

    /**
     * @return int
     */
    public function getFromAge(): int
    {
        return $this->party_history_group->from_age;
    }

    /**
     * @return int
     */
    public function getToAge(): int
    {
        return $this->party_history_group->to_age;
    }

    /**
     * @return int
     */
    public function getFromAgePartner(): int
    {
        return $this->party_history_group->from_age_partner;
    }

    /**
     * @return int
     */
    public function getToAgePartner(): int
    {
        return $this->party_history_group->to_age_partner;
    }
}
