<?php


namespace Src\Domain\Api\Models\UserTicket;

use App\Eloquent\TicketPlan;
use Src\Domain\FormModel;
use Src\Enum\BuySituation;
use Src\Enum\TicketUpdateDiv;

/**
 * Class BuyTicketForm
 * @package Src\Domain\Api\Models\UserTicket
 */
class BuyTicketForm extends FormModel
{
    /** @var int */
    protected $mobile_platform;

    /** @var int */
    protected $ticket_plan_id;

    /** @var int|null */
    protected $party_id;

    /** @var string */
    protected $receipt_data;
    /** @var string */
    protected $package_name;
    /** @var string */
    protected $product_id;
    /** @var string */
    protected $token;

    protected $fields = [
        'ticket_plan_id' => 'int',
        'party_id' => 'int',
        'receipt_data' => 'string',
        'package_name' => 'string',
        'product_id' => 'string',
        'token' => 'string'
    ];

    /**
     * BuyTicketForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->mobile_platform = array_get_int($input, 'mobile_platform');
        $this->ticket_plan_id = $input['ticket_plan_id'];
        $this->party_id = array_get_int($input, 'party_id');
        $this->receipt_data = array_get_string($input, 'receipt_data');
        // Platform Android
        $this->package_name = array_get_string($input, 'package_name');
        $this->product_id = array_get_string($input, 'product_id');
        $this->token = array_get_string($input, 'token');
    }

    /**
     * getMobilePlatform
     *
     * @return int
     */
    public function getMobilePlatform(): int
    {
        return $this->mobile_platform;
    }

    /**
     * getTicketPlanId
     *
     * @return int
     */
    public function getTicketPlanId(): int
    {
        return $this->ticket_plan_id;
    }

    /**
     * getReceiptData
     *
     * @return string
     */
    public function getReceiptData(): string
    {
        return $this->receipt_data;
    }

    /**
     * @return array
     */
    public function createValidateProductForm(): array
    {
        return [
            'package_name' => $this->getPackageName(),
            'product_id' => $this->getProductId(),
            'token' => $this->getToken()
        ];
    }

    /**
     * getPackageName
     *
     * @return string|null
     */
    public function getPackageName(): ?string
    {
        return $this->package_name;
    }

    /**
     * getProductId
     *
     * @return string|null
     */
    public function getProductId(): ?string
    {
        return $this->product_id;
    }

    /**
     * getToken
     *
     * @return string|null
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     * createUserTicketAttribute
     *
     * @param int $new_ticket
     * @return array
     */
    public function createUserTicketAttributes(int $new_ticket): array
    {
        return [
            'ticket' => $new_ticket
        ];
    }

    /**
     * createTicketHistoryAttributes
     *
     * @param TicketPlan $ticket_plan
     * @param int $old_ticket
     * @param int $new_ticket
     * @param int $user_id
     * @return array
     */
    public function createTicketHistoryAttributes(TicketPlan $ticket_plan, int $old_ticket, int $new_ticket, int $user_id): array
    {
        return [
            'user_id' => $user_id,
            'ticket_plan_id' => $ticket_plan->id,
            'ticket_plan_div' => $ticket_plan->ticket_plan_div,
            'ticket_update_div' => TicketUpdateDiv::BUY,
            'party_id' => $this->getPartyId(),
            'buy_situation' => $this->getPartyId() ? BuySituation::BUY_IN_PARTY : BuySituation::BUY_IN_NORMAL,
            'name' => $ticket_plan->name,
            'normal_money' => $ticket_plan->normal_money,
            'money' => $ticket_plan->money,
            'ticket' => $ticket_plan->ticket,
            'old_ticket' => $old_ticket,
            'new_ticket' => $new_ticket,
            'time_hour' => $ticket_plan->time_hour,
            'time_minute' => $ticket_plan->time_minute,
            'ticket_at' => now(),
        ];
    }

    /**
     * getPartyId
     *
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        return $this->party_id;
    }
}
