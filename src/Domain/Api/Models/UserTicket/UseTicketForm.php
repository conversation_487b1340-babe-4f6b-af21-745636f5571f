<?php


namespace Src\Domain\Api\Models\UserTicket;

use Src\Domain\FormModel;
use Src\Enum\BuySituation;
use Src\Enum\TicketUpdateDiv;

/**
 * Class UseTicketForm
 * @package Src\Domain\Api\Models\UserTicket
 */
class UseTicketForm extends FormModel
{

    public static function createHistoryAttributeForUseTicket($party_id, int $old_ticket, int $new_ticket, int $user_id): array
    {
        return [
            'user_id' => $user_id,
            'ticket_plan_id' => null,
            'ticket_plan_div' => null,
            'name' => null,
            'normal_money' => null,
            'money' => null,
            'ticket' => null,
            'time_hour' => null,
            'time_minute' => null,
            'ticket_update_div' => TicketUpdateDiv::USE,
            'party_id' => $party_id,
            'buy_situation' => $party_id ? BuySituation::BUY_IN_PARTY : BuySituation::BUY_IN_NORMAL,
            'old_ticket' => $old_ticket,
            'new_ticket' => $new_ticket,
            'ticket_at' => now(),
        ];
    }
}
