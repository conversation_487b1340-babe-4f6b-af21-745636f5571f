<?php

namespace Src\Domain\Api\Models\UserTicket;

use Src\Domain\FormModel;
use Src\Enum\PresentTicketDiv;

/**
 * Class PresentTicketForm
 * @package Src\Domain\Api\Models\UserTicket
 */
class PresentTicketForm extends FormModel
{
    /** @var int */
    protected $target_user_id;
    /** @var int */
    protected $present_ticket_div;

    /**
     * PresentTicketForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->target_user_id = $input['target_user_id'];
        $this->present_ticket_div = $input['present_ticket_div'];
    }

    /**
     * getReportedUserId
     *
     * @return int
     */
    public function getReportedUserId(): int
    {
        return $this->target_user_id;
    }

    /**
     * createUserTicketAttributes
     *
     * @param int $old_ticket
     * @param int $category
     * @return array
     */
    public function createUserTicketAttributes(int $old_ticket, int $category): array
    {
        /** @var PresentTicketDiv $ticket */
        $ticket = PresentTicketDiv::getTicket($this->getPresentTicketDiv());
        $new_ticket = $old_ticket + $ticket;
        return [
            'ticket' => $new_ticket
        ];
    }

    /**
     * getPresentTicketDiv
     *
     * @return int
     */
    public function getPresentTicketDiv(): int
    {
        return $this->present_ticket_div;
    }

    /**
     * createTicketHistoryAttributes
     *
     * @param int $old_ticket
     * @param int $category
     * @return array
     */
    public function createTicketHistoryAttributes(int $old_ticket, int $category): array
    {
        /** @var PresentTicketDiv $ticket */
        $ticket = PresentTicketDiv::getTicket($this->getPresentTicketDiv());
        $new_ticket = $old_ticket + $ticket;
        return [
            'category' => $category,
            'present_ticket_div' => $this->getPresentTicketDiv(),
            'ticket' => $ticket,
            'old_ticket' => $old_ticket,
            'new_ticket' => $new_ticket,
            'ticket_at' => now()
        ];
    }
}
