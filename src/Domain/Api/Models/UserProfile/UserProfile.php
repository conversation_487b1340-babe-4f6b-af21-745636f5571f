<?php

namespace Src\Domain\Api\Models\UserProfile;

use App\Eloquent\UserProfile as EloquentUserProfile;
use Exception;

/**
 * Class UserProfile
 *
 * @package Src\Domain\Admin\Models\UserProfile
 */
class UserProfile
{
    /**
     * @var EloquentUserProfile
     */
    private $user_profile;

    /**
     * UserProfile constructor.
     * @param EloquentUserProfile|null $user_profile
     */
    public function __construct(?EloquentUserProfile $user_profile)
    {
        $this->user_profile = $user_profile;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_profile->user_id;
    }

    /**
     * @return int
     */
    public function getAvatarId(): int
    {
        return $this->user_profile->avatar_id;
    }

    /**
     * @return string|null
     */
    public function getNickname(): ?string
    {
        return $this->user_profile->nickname;
    }

    /**
     * @return mixed
     */
    public function getBirthday(): ?string
    {
        if ($this->user_profile->birthday) {
            return $this->user_profile->birthday;
        }
        return null;
    }

    /**
     * @return int|null
     * @throws Exception
     */
    public function getAge(): ?int
    {
        return $this->user_profile->age;
    }

    /**
     * @return int|null
     */
    public function getGender(): ?int
    {
        return $this->user_profile->gender;
    }

    /**
     * @return string|null
     */
    public function getOccupation(): ?string
    {
        return $this->user_profile->occupation;
    }

    /**
     * @return string|null
     */
    public function getAvatarUrl(): ?string
    {
        return optional($this->user_profile->storageFile)->file_url;
    }

}
