<?php

namespace Src\Domain\Api\Models\UserProfile;

use App\Eloquent\UserProfile as EloquentUserProfile;
use DateTime;
use Exception;
use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;

/**
 * Class UserProfileForm
 *
 * @package Src\Domain\Admin\Models\UserProfile
 */
class UserProfileForm extends FormModel
{
    /** @var UploadedFile|null */
    protected $avatar;
    /** @var string */
    protected $nickname;
    /** @var string|null */
    protected $birthday;
    /** @var string|null */
    protected $occupation;

    protected $fields = [
        'avatar' => 'file',
        'nickname' => 'string',
        'birthday' => 'string',
        'occupation' => 'string',
    ];

    /**
     * UserProfileForm constructor.
     * @param array $input
     * @param EloquentUserProfile|null $user_profile
     */
    public function __construct(array $input = [], EloquentUserProfile $user_profile = null)
    {
        $input = $this->castFields($input, $user_profile);

        $this->avatar = $input['avatar'];
        $this->nickname = $input['nickname'];
        $this->birthday = $input['birthday'];
        $this->occupation = $input['occupation'];
    }

    /**
     * @return UploadedFile|mixed|null
     */
    public function getAvatar()
    {
        return $this->avatar;
    }

    /**
     * @param $avatar_id
     * @return array
     * @throws Exception
     */
    public function createAttributes($avatar_id): array
    {

        return array_merge($this->commonAttributes(), ['avatar_id' => $avatar_id]);
    }

    /**
     * @return array
     * @throws Exception
     */
    private function commonAttributes(): array
    {
        return [
            'nickname' => $this->getNickname(),
            'birthday' => $this->getBirthday(),
            'occupation' => $this->getOccupation(),
        ];
    }

    /**
     * @return string
     */
    public function getNickname(): string
    {
        return $this->nickname;
    }

    /**
     * @return DateTime
     * @throws Exception
     */
    public function getBirthday()
    {
        return new DateTime($this->birthday);
    }

    /**
     * @return string|null
     */
    public function getOccupation(): ?string
    {
        return $this->occupation;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
