<?php

namespace Src\Domain\Api\Models\UserProfile;

use Src\Domain\FormModel;
use Src\Enum\Gender;

/**
 * Class UserProfileForm
 *
 * @package Src\Domain\Admin\Models\UserProfile
 */
class UserCreateProfileForm extends FormModel
{
    /** int|null*/
    protected $gender;
    /** int|null*/
    protected $gender_partner;

    protected $fields = [
        'gender' => 'int',
        'gender_partner' => 'int',
    ];

    /**
     * UserProfileForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $input = $this->castFields($input);
        $this->gender = $input['gender'];
        $this->gender_partner = $input['gender_partner'];
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'gender' => $this->getG<PERSON>(),
            'gender_partner' => $this->getGenderPartner(),
        ];
    }

    /**
     * @return int|null
     */
    public function getGender(): ?int
    {
        return $this->gender;
    }

    /**
     * @return int|null
     */
    public function getGenderPartner(): ?int
    {
        return $this->gender_partner;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        $gender_party = $this->getGenderPartner() == Gender::MALE ? Gender::FEMALE : Gender::MALE;
        return array_merge($this->commonAttributes(), [
            'gender_party' => $gender_party
        ]);
    }
}
