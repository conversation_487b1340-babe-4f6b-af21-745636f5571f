<?php

namespace Src\Domain\Api\Models\Party;

use App\Eloquent\SettingParty;
use Src\Domain\FormModel;
use Src\Enum\Age;

/**
 * Class PartyGroupForm
 *
 * @package Src\Domain\Admin\Models\Party
 */
class PartyGroupForm extends FormModel
{

    /**
     * create Attributes
     *
     * @return array
     */
    public static function createAttributes(int $party_id, SettingParty $setting_party): array
    {
        return [
            'party_id' => $party_id,
            'setting_party_id' => $setting_party->id,
            'gender' => $setting_party->gender,
            'gender_partner' => $setting_party->gender_partner,
            'from_age' => $setting_party->from_age,
            'to_age' => $setting_party->to_age,
            'from_age_partner' => $setting_party->from_age_partner,
            'to_age_partner' => $setting_party->to_age_partner,
        ];
    }

    public static function createWithFriendAttributes(int $party_id, int $setting_party_id, array $party_group_data): array
    {
        return [
            'party_id' => $party_id,
            'setting_party_id' => $setting_party_id,
            'gender' => $party_group_data['gender'],
            'gender_partner' => $party_group_data['gender_partner'],
            'from_age' => $party_group_data['from_age'],
            'to_age' => $party_group_data['to_age'],
            'from_age_partner' => Age::MIN,
            'to_age_partner' => Age::MAX
        ];
    }

}
