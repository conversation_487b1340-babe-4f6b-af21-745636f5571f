<?php

namespace Src\Domain\Api\Models\Party;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\PartyGroup as EloquentPartyGroup;
use App\Eloquent\User as EloquentUser;
use Src\Domain\Api\Models\PartyGroup\PartyGroup;
use Src\Domain\Api\Models\PartyParticipant\PartyParticipant;
use Src\Domain\Api\Models\User\User;
use Src\Enum\Gender;
use Src\Enum\PartyStatus;
use Src\Utils\Agora\GenToken\AgoraGenToken;

/**
 * Class Party
 *
 * @package Src\Domain\Admin\Models\Party
 */
class Party
{
    /**
     * @var EloquentParty
     */
    private $party;

    /**
     * Party constructor.
     *
     * @param EloquentParty $party
     */
    public function __construct(EloquentParty $party)
    {
        $this->party = $party;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    public function toParticipantJoinedResponse(EloquentParty $party, EloquentUser $participant): array
    {
        /*$party_started = $party->party_status === PartyStatus::STARTED;
        $result = [
            'party_id' => $party->id,
            'party_started' => $party_started
        ];

        if($party_started){
            $agora_token_info = AgoraGenToken::createFormatAgoraTokensResponse($party->channel_id, $participant->id,
                $participant->pivot->agora_token, $participant->pivot->expire_at);
            $result['agora_token_info'] = $agora_token_info;
        }else{
            $result['agora_token_info'] = null;
        }*/


        $party_started = PartyStatus::isPartying($party->party_status);
        $result = [
            'party_id' => $party->id,
            'party_started' => $party_started
        ];
        if ($party_started) {
            $agora_token_info = AgoraGenToken::createFormatAgoraTokensResponse($party->channel_id, $participant->id,
                $participant->pivot->agora_token, $participant->pivot->expire_at);
            $result['agora_token_info'] = $agora_token_info;
        } else {
            $result['agora_token_info'] = null;
        }

        return $result;
    }

    public function getParticipantSplitGroups(int $user_id, array $user_friends): array
    {
        $participants = [];
        $my_info = null;
        $party_groups = [];

        // Get my participant
        foreach ($this->party->partyGroups as $partyGroup) {
            foreach ($partyGroup->partyParticipants as $participant) {
                if ($user_id == $participant->user_id) {
                    $my_info = (new PartyParticipant($participant))->toMyParticipant();
                }
            }
            $party_groups[] = (new PartyGroup($partyGroup))->toComponentFormValue();
        }

        // Sort list participant
        $male_group = $this->party->partyGroups->first(function ($group, $key) {
            /** @var EloquentPartyGroup $group */
            return $group->gender === Gender::MALE;
        });
        $female_group = $this->party->partyGroups->first(function ($group, $key) {
            /** @var EloquentPartyGroup $group */
            return $group->gender === Gender::FEMALE;
        });

        // Male Data
        $male_group_data = [];
        $participants = [];
        foreach ($male_group->partyParticipants as $male_index => $female_participant) {
            // male
            $participants[] = [
                'user' => User::getUserAndProfile($female_participant->user),
                'participant_info' => (new PartyParticipant($female_participant))->toComponentFormValue(),
                'is_friend' => in_array($female_participant->user->id, $user_friends),
            ];
        }

        $male_group_data['participants'] = $participants;

        // Female Data
        $female_group_data = [];
        $participants = [];
        foreach ($female_group->partyParticipants as $male_index => $female_participant) {
            // male
            $participants[] = [
                'user' => User::getUserAndProfile($female_participant->user),
                'participant_info' => (new PartyParticipant($female_participant))->toComponentFormValue(),
                'is_friend' => in_array($female_participant->user->id, $user_friends),
            ];
        }

        $female_group_data['participants'] = $participants;

        return [
            'party' => $this->toDetailApiResponse(),
            'my_participant' => $my_info,
            'party_group_male' => $male_group_data,
            'party_group_female' => $female_group_data,
        ];
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'channel_id' => $this->getChannelId(),
            'party_type' => $this->getPartyType(),
            'group_type' => $this->getGroupType(),
            'invite_member_type' => $this->getInviteMemberType(),
            'party_status' => $this->getPartyStatus(),
            'creator_id' => $this->getCreatorId(),
            'match_at' => $this->getMatchAt(),
            'start_at' => $this->getStartAt(),
            'expire_at' => $this->getExpireAt(),
            'ended_at' => $this->getEndedAt(),
            'party_time' => $this->getPartiedTime(),
            'is_matched_extend' => $this->getIsMatchedExtend(),
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party->id;
    }

    /**
     * @return string
     */
    public function getChannelId(): string
    {
        return $this->party->channel_id;
    }

    /**
     * @return int
     */
    public function getPartyType(): int
    {
        return $this->party->party_type;
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        return $this->party->group_type;
    }

    /**
     * @return int
     */
    public function getInviteMemberType(): int
    {
        return $this->party->invite_member_type;
    }

    /**
     * @return int
     */
    public function getPartyStatus(): int
    {
        return $this->party->party_status;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->party->creator_id;
    }

    /**
     * @return string
     */
    public function getMatchAt(): string
    {
        return $this->party->match_at;
    }

    /**
     * @return string|null
     */
    public function getStartAt(): ?string
    {
        return $this->party->start_at;
    }

    /**
     * @return string|null
     */
    public function getExpireAt(): ?string
    {
        return $this->party->expire_at;
    }

    /**
     * @return string|null
     */
    public function getEndedAt(): ?string
    {
        return $this->party->ended_at;
    }

    /**
     * @return string|null
     */
    public function getPartiedTime(): ?string
    {
        return $this->party->party_time;
    }

    /**
     * @return bool
     */
    public function getIsMatchedExtend(): bool
    {
        return $this->party->is_matched_extend;
    }

    public function getParticipants(int $user_id, array $user_friend_ids): array
    {
        $participants = [];
        $my_info = null;

        // Sort list participant
        $male_group = $this->party->partyGroups->first(function ($group, $key) {
            /** @var EloquentPartyGroup $group */
            return $group->gender === Gender::MALE;
        });
        $female_group = $this->party->partyGroups->first(function ($group, $key) {
            /** @var EloquentPartyGroup $group */
            return $group->gender === Gender::FEMALE;
        });

        // sort staggered
        foreach ($male_group->partyParticipants as $male_index => $male_participant) {
            // male
            $participants[] = [
                'user' => User::getUserAndProfile($male_participant->user),
                'participant_info' => (new PartyParticipant($male_participant))->toComponentFormValue(),
                'is_friend' => in_array($male_participant->user->id, $user_friend_ids),
            ];

            // female
            $female_participant = $female_group->partyParticipants->first(function ($participant, $fe_index) use ($male_index) {
                return $male_index === $fe_index;
            });

            if ($female_participant) {
                $participants[] = [
                    'user' => User::getUserAndProfile($female_participant->user),
                    'participant_info' => (new PartyParticipant($female_participant))->toComponentFormValue(),
                    'is_friend' => in_array($female_participant->user->id, $user_friend_ids),
                ];
            }
        }

        // If list male less than female, add remain female to list
        if($male_group->partyParticipants->count() < $female_group->partyParticipants->count()){
            $from_index = $male_group->partyParticipants->count();
            foreach ($female_group->partyParticipants as $female_index => $female_participant) {
                if($female_index >= $from_index){
                    $participants[] = [
                        'user' => User::getUserAndProfile($female_participant->user),
                        'participant_info' => (new PartyParticipant($female_participant))->toComponentFormValue(),
                        'is_friend' => in_array($female_participant->user->id, $user_friend_ids),
                    ];
                }
            }
        }

        $party_info = array_merge($this->toDetailApiResponse(), [
            'number_male_user' => $male_group->partyParticipants->count(),
            'number_female_user' => $female_group->partyParticipants->count(),
        ]);

        return [
            'party' => $party_info,
            'my_participant' => $my_info,
            'participants' => $participants,
        ];
    }


}
