<?php

namespace Src\Domain\Api\Models\Party;

use App\Eloquent\SettingParty;
use Src\Domain\FormModel;
use Src\Enum\PartyStatus;

/**
 * Class PartyWithFriendForm
 *
 * @package Src\Domain\Admin\Models\SettingParty
 */
class PartyWithFriendForm extends FormModel
{

    /**
     * @var int
     */
    protected $setting_party_id;

    /**
     * PartyForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->setting_party_id = array_get_int($input, 'setting_party_id');
    }


    /**
     * @return int
     */
    public function getSettingPartyId(): int
    {
        return $this->setting_party_id;
    }

    public function createAttributes(SettingParty $creator_setting_party): array
    {
        return [
            'party_type' => $creator_setting_party->party_type,
            'group_type' => $creator_setting_party->group_type,
            'channel_id' => '',
            'invite_member_type' => $creator_setting_party->invite_member_type,
            'party_status' => PartyStatus::MATCHED,
            'match_at' => now(),
            'start_at' => null,
            'expire_at' => null,
            'ended_at' => null,
            'is_matched_extend' => false,
            'creator_id' => $creator_setting_party->creator_id,
        ];
    }
}
