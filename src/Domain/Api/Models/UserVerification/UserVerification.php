<?php

namespace Src\Domain\Api\Models\UserVerification;

use App\Eloquent\UserVerification as EloquentUserVerification;

/**
 * Class UserVerification
 *
 * @package Src\Domain\Admin\Models\UserVerification
 */
class UserVerification
{
    /**
     * @var EloquentUserVerification
     */
    private $user_verification;

    /**
     * UserVerification constructor.
     *
     * @param EloquentUserVerification $user_verification
     */
    public function __construct(EloquentUserVerification $user_verification)
    {
        $this->user_verification = $user_verification;
    }

    /**
     * @return int
     */
    public function getStorageFileId(): int
    {
        return $this->user_verification->storage_file_id;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'identification_type' => $this->getIdentificationType(),
            'approval_status' => $this->getApprovalStatus(),
            'avatar_url' => $this->user_verification->storageFile->file_url,
        ];
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_verification->user_id;
    }

    /**
     * @return int
     */
    public function getIdentificationType(): int
    {
        return $this->user_verification->identification_type;
    }

    /**
     * @return int
     */
    public function getApprovalStatus(): int
    {
        return $this->user_verification->approval_status;
    }
}
