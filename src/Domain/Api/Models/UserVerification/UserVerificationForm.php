<?php

namespace Src\Domain\Api\Models\UserVerification;

use Illuminate\Http\UploadedFile;
use Src\Domain\FormModel;
use Src\Enum\ApprovalStatus;

/**
 * Class UserVerificationForm
 *
 * @package Src\Domain\Admin\Models\UserVerification
 */
class UserVerificationForm extends FormModel
{

    /**
     * @var int
     */
    protected $identification_type;

    /** @var UploadedFile|null */
    protected $verification_image;

    protected $fields = [
        'identification_type' => 'int',
        'verification_image' => 'file'
    ];

    /**
     * UserVerificationForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->identification_type = array_get_int($input, 'identification_type');
        $this->verification_image = $input['verification_image'];
    }

    /**
     * @return UploadedFile|mixed|null
     */
    public function getVerificationImage(): UploadedFile
    {
        return $this->verification_image;
    }

    /**
     * create Attributes
     *
     * @param int $storage_file_id
     * @return array
     */
    public function createAttributes(int $storage_file_id): array
    {
        return array_merge($this->commonAttributes(), ['storage_file_id' => $storage_file_id]);
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'identification_type' => $this->getIdentificationType(),
            'approval_status' => ApprovalStatus::WAIT_APPROVAL
        ];
    }

    /**
     * @return int
     */
    public function getIdentificationType(): int
    {
        return $this->identification_type;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
