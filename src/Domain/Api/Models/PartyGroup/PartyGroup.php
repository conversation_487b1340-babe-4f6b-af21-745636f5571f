<?php

namespace Src\Domain\Api\Models\PartyGroup;

use App\Eloquent\PartyGroup as EloquentPartyGroup;
use Src\Domain\Model;

/**
 * Class PartyGroup
 *
 * @package Src\Domain\Api\Models\PartyGroup
 */
class PartyGroup extends Model
{
    /**
     * @var EloquentPartyGroup
     */
    private $party_group;

    /**
     * PartyGroup constructor.
     *
     * @param EloquentPartyGroup $party_group
     */
    public function __construct(EloquentPartyGroup $party_group)
    {
        $this->party_group = $party_group;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'party_id' => $this->getPartyId(),
            'gender' => $this->getGender(),
            'gender_partner' => $this->getGenderPartner(),
            'from_age' => $this->getFromAge(),
            'to_age' => $this->getToAge(),
            'from_age_partner' => $this->getFromAgePartner(),
            'to_age_partner' => $this->getToAgePartner()
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party_group->id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->party_group->party_id;
    }

    /**
     * @return int
     */
    public function getGender(): int
    {
        return $this->party_group->gender;
    }

    /**
     * @return int
     */
    public function getGenderPartner(): int
    {
        return $this->party_group->gender_partner;
    }

    /**
     * @return int
     */
    public function getFromAge(): int
    {
        return $this->party_group->from_age;
    }

    /**
     * @return int
     */
    public function getToAge(): int
    {
        return $this->party_group->to_age;
    }

    /**
     * @return int
     */
    public function getFromAgePartner(): int
    {
        return $this->party_group->from_age_partner;
    }

    /**
     * @return int
     */
    public function getToAgePartner(): int
    {
        return $this->party_group->to_age_partner;
    }
}
