<?php

namespace Src\Domain\Api\Models\UserPoint;

use Src\Domain\FormModel;
use Src\Enum\PointUpdateDiv;
use Src\Enum\PresentPointDiv;

/**
 * Class ReceivePointForm
 * @package Src\Domain\Api\Models\UserPoint
 */
class ReceivePointForm extends FormModel
{
    /** @var int */
    protected $point_token;

    /**
     * ReceivePointForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->point_token = $input['point_token'];
    }

    /**
     * getPointToken
     *
     * @return int
     */
    public function getPointToken(): string
    {
        return $this->point_token;
    }

    /**
     * createUserPointAttributes
     *
     * @param int $old_point
     * @param int $point_update_div
     * @return array
     */
    public function createUserPointAttributes(int $old_point, int $point_update_div, int $present_point_div): array
    {
        /** @var PresentPointDiv $point */
        $point = PresentPointDiv::getPoint($present_point_div);
        $new_point = ($point_update_div === PointUpdateDiv::PRESENT) ? $old_point - $point : $old_point + $point;
        return [
            'point' => $new_point
        ];
    }

    /**
     * createPointHistoryAttributes
     *
     * @param int $old_point
     * @param int $point_update_div
     * @return array
     */
    public function createPointHistoryAttributes(int $old_point, int $point_update_div, array $common_data): array
    {
        /** @var PresentPointDiv $point */
        $point = PresentPointDiv::getPoint($common_data['present_point_div']);
        $new_point = ($point_update_div === PointUpdateDiv::PRESENT) ? $old_point - $point : $old_point + $point;
        return [
            'point_update_div' => $point_update_div,
            'present_point_div' => $common_data['present_point_div'],
            'present_user_id' => $common_data['present_user_id'],
            'receive_user_id' => $common_data['receive_user_id'],
            'point' => $point,
            'old_point' => $old_point,
            'new_point' => $new_point,
            'point_at' => now()
        ];
    }
}
