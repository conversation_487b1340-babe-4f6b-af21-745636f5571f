<?php

namespace Src\Domain\Api\Models\UserPoint;

use App\Eloquent\PointPlan;
use Src\Domain\FormModel;
use Src\Enum\PointUpdateDiv;

/**
 * Class BuyPointForm
 * @package Src\Domain\Api\Models\UserPoint
 */
class BuyPointForm extends FormModel
{
    /** @var int */
    protected $mobile_platform;

    /** @var int */
    protected $point_plan_id;

    /** @var int|null */
    protected $party_id;

    /** @var string */
    protected $receipt_data;
    /** @var string */
    protected $package_name;
    /** @var string */
    protected $product_id;
    /** @var string */
    protected $token;

    protected $fields = [
        'point_plan_id' => 'int',
        'party_id' => 'int',
        'package_name' => 'string',
        'product_id' => 'string',
        'token' => 'string'
    ];

    /**
     * BuyPointForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->mobile_platform = array_get_int($input, 'mobile_platform');
        // ios
        $this->point_plan_id = array_get_int($input, 'point_plan_id');
        $this->party_id = array_get_int($input, 'party_id');
        $this->receipt_data = array_get_string($input, 'receipt_data');
        // Android
        $this->package_name = array_get_string($input, 'package_name');
        $this->product_id = array_get_string($input, 'product_id');
        $this->token = array_get_string($input, 'token');
    }

    /**
     * getMobilePlatform
     *
     * @return int
     */
    public function getMobilePlatform(): int
    {
        return $this->mobile_platform;
    }

    /**
     * getPointPlanId
     *
     * @return int
     */
    public function getPointPlanId(): int
    {
        return $this->point_plan_id;
    }

    /**
     * getReceiptData
     *
     * @return string
     */
    public function getReceiptData(): string
    {
        return $this->receipt_data;
    }

    /**
     * @return array
     */
    public function createValidateProductForm(): array
    {
        return [
            'package_name' => $this->getPackageName(),
            'product_id' => $this->getProductId(),
            'token' => $this->getToken()
        ];
    }

    /**
     * @return string|null
     */
    public function getPackageName(): ?string
    {
        return $this->package_name;
    }

    /**
     * @return string|null
     */
    public function getProductId(): ?string
    {
        return $this->product_id;
    }

    /**
     * @return string|null
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     * createUserPointAttribute
     *
     * @param $new_point
     * @return array
     */
    public function createUserPointAttributes($new_point): array
    {
        return [
            'point' => $new_point
        ];
    }

    /**
     * createPointHistoryAttribute
     *
     * @param PointPlan $point_plan
     * @param int $old_point
     * @param int $new_point
     * @param int $user_id
     * @return array
     */
    public function createPointHistoryAttributes(PointPlan $point_plan, int $old_point, int $new_point, int $user_id): array
    {
        return [
            'user_id' => $user_id,
            'point_plan_id' => $point_plan->id,
            'point_update_div' => PointUpdateDiv::BUY,
            'party_id' => $this->getPartyId(),
            'point' => $point_plan->point,
            'money' => $point_plan->money,
            'old_point' => $old_point,
            'new_point' => $new_point,
            'point_at' => now(),
        ];
    }

    /**
     * getPartyId
     *
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        return $this->party_id;
    }
}
