<?php

namespace Src\Domain\Api\Models\MessageConversation;

use App\Eloquent\MessageConversation as EloquentMessageConversation;
use Src\Domain\Api\Models\User\User;

/**
 * Class MessageConversation
 *
 * @package Src\Domain\Admin\Models\MessageConversation
 */
class MessageConversation
{
    /**
     * @var EloquentMessageConversation
     */
    private $message_conversation;

    /**
     * MessageConversation constructor.
     *
     * @param EloquentMessageConversation $message_conversation
     */
    public function __construct(EloquentMessageConversation $message_conversation)
    {
        $this->message_conversation = $message_conversation;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toListApiResponse(int $author_id): array
    {
        $result = [
            'id' => $this->getId(),
            'creator_id' => $this->getCreatorId(),
            'channel_id' => $this->getChannelId(),
        ];

        foreach ($this->message_conversation->messageParticipants as $message_participant) {
            if ($author_id === $message_participant->id) {
                $result['my_conversation_info'] = [
                    'number_unread_message' => optional($message_participant->pivot)->number_unread_message,
                    'last_message' => optional($message_participant->pivot)->last_message,
                    'last_message_at' => optional($message_participant->pivot)->last_message_at
                ];
            } else {
                $result['partner'] = User::getProfile($message_participant);
            }
        }

        return $result;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->message_conversation->id;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->message_conversation->creator_id;
    }

    /**
     * @return string
     */
    public function getChannelId(): string
    {
        return $this->message_conversation->channel_id;
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailApiResponse(): array
    {
        return [
            'id' => $this->getId(),
            'creator_id' => $this->getCreatorId(),
            'channel_id' => $this->getChannelId()
        ];
    }
}
