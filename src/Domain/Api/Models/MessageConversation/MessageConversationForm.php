<?php

namespace Src\Domain\Api\Models\MessageConversation;

use Src\Domain\FormModel;

/**
 * Class MessageConversationForm
 *
 * @package Src\Domain\Admin\Models\MessageConversation
 */
class MessageConversationForm extends FormModel
{

    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $creator_id;

    /**
     * @var string
     */
    protected $channel_id;

    /**
     * MessageConversationForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->creator_id = array_get_int($input, 'creator_id');
        $this->channel_id = array_get_string($input, 'channel_id');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'creator_id' => $this->getCreatorId(),
            'channel_id' => $this->getChannelId()
        ];
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->creator_id;
    }

    /**
     * @return string
     */
    public function getChannelId(): string
    {
        return $this->channel_id;
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return $this->commonAttributes();
    }

}
