<?php

namespace Src\Domain\Api\Controllers\TicketHistory;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\TicketHistoryService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\TicketHistory
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param TicketHistoryService $ticket_history_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function index(TicketHistoryService $ticket_history_service): JsonResponse
    {
        $ticket_histories = $ticket_history_service->fetchAll($this->author());
        return json_response(ResultCode::SUCCESS, $ticket_histories);
    }

}
