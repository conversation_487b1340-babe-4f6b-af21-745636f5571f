<?php

namespace Src\Domain\Api\Controllers\UserPoint;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Requests\UserPoint\BuyPointRequest;
use Src\Domain\Api\Requests\UserPoint\PresentPointRequest;
use Src\Domain\Api\Requests\UserPoint\ReceivePointRequest;
use Src\Domain\Api\Services\UserPointService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;
use Throwable;

/**
 * Class UserPointController
 * @package Src\Domain\Api\Controllers\User
 */
class UserPointController extends Controller
{
    /**
     * buyPoint
     *
     * @param BuyPointRequest $request
     * @param UserPointService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function buyPoint(BuyPointRequest $request, UserPointService $service): JsonResponse
    {
        $author = $this->author();
        $result = $service->buyPoint($request->validatedForm(), $author->getId());
        if ($result === false) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * confirmPresentPoint
     *
     * @param PresentPointRequest $request
     * @param UserPointService $service
     * @return JsonResponse
     * @throws AuthenticationException
     * @throws Throwable
     */
    public function confirmPresentPoint(PresentPointRequest $request, UserPointService $service): JsonResponse
    {
        $result = $service->confirmPresentPoint($request->validatedForm(), $this->author());
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);

    }

    /**
     * presentPoint
     *
     * @param ReceivePointRequest $request
     * @param UserPointService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function receivePoint(ReceivePointRequest $request, UserPointService $service): JsonResponse
    {
        $author = $this->author();
        $result = $service->receivePoint($request->validatedForm(), $author->getId());
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * declineReceivePoint
     *
     * @param ReceivePointRequest $request
     * @param UserPointService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function declineReceivePoint(ReceivePointRequest $request, UserPointService $service): JsonResponse
    {
        $author = $this->author();
        $result = $service->declineReceivePoint($request->validatedForm(), $author);
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * getCurrentPoint
     *
     * @param UserPointService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function getCurrentPoint(UserPointService $service): JsonResponse
    {
        $author = $this->author();
        $user = $service->getCurrentPoint($author->getId());
        return json_response(ResultCode::SUCCESS, $user->getCurrentPoint());
    }
}
