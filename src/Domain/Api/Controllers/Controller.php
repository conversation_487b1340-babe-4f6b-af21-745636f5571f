<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Exception\AuthenticationException;

/**
 * Class Controller
 * @package Src\Domain\Api\Controllers
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * author
     *
     * @return AuthorInterface
     * @throws AuthenticationException
     */
    protected function author(): AuthorInterface
    {
        $author = request()->get('author');
        if (null === $author) {
            throw new AuthenticationException('Unauthorized.');
        }
        return $author;
    }
}
