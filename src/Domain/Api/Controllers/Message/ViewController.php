<?php

namespace Src\Domain\Api\Controllers\Message;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\MessageService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\Message
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param MessageService $message_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function index(Request $request, MessageService $message_service): JsonResponse
    {
        $partner_id = $request->input('partner_id');
        $messages = $message_service->fetchAll($this->author(), $partner_id);
        return json_response(ResultCode::SUCCESS, $messages);
    }

    /**
     * show
     *
     * @param MessageService $message_service
     * @param int $message_id
     * @return JsonResponse
     */
    public function show(MessageService $message_service, int $message_id)
    {
        $message = $message_service->findOrFail($message_id);
        return json_response(ResultCode::SUCCESS, $message->toDetailApiResponse());
    }
}
