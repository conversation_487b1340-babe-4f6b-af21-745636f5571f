<?php

namespace Src\Domain\Api\Controllers\UserContact;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\UserContact\UserContact;
use Src\Domain\Api\Requests\UserContact\CreateRequest;
use Src\Domain\Api\Services\UserContactService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\UserContact
 */
class CreateController extends Controller
{
    /**
     * Store
     *
     * @param CreateRequest $request
     * @param UserContactService $user_contact_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function store(CreateRequest $request, UserContactService $user_contact_service): JsonResponse
    {
        $author = $this->author();

        /** @var UserContact $user_contact */
        $user_contact = $user_contact_service->store($request->validatedForm(), $author->getId());
        if (!$user_contact) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $user_contact->toDetailApiResponse());
    }

}
