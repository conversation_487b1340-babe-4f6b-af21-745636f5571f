<?php

namespace Src\Domain\Api\Controllers\VerificationCode;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\VerificationCode\VerificationCode;
use Src\Domain\Api\Requests\VerificationCode\CreateRequest;
use Src\Domain\Api\Services\VerificationCodeService;
use Src\Enum\ResultCode;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\VerificationCode
 */
class CreateController extends Controller
{
    /**
     * Store
     *
     * @param CreateRequest $request
     * @param VerificationCodeService $verification_code_service
     * @return JsonResponse
     */
    public function store(CreateRequest $request, VerificationCodeService $verification_code_service): JsonResponse
    {
        /** @var VerificationCode $result */
        $result = $verification_code_service->store($request->validatedForm());
        if ($result === false) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }
}
