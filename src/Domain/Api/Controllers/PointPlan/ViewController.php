<?php

namespace Src\Domain\Api\Controllers\PointPlan;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\PointPlanService;
use Src\Enum\ResultCode;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\PointPlan
 */
class ViewController extends Controller
{

    /**
     * index
     *
     * @param PointPlanService $point_plan_service
     * @return JsonResponse
     */
    public function index(PointPlanService $point_plan_service): JsonResponse
    {
        $point_plans = $point_plan_service->fetchAll();
        return json_response(ResultCode::SUCCESS, $point_plans);
    }

}
