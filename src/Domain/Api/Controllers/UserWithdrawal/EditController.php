<?php

namespace Src\Domain\Api\Controllers\UserWithdrawal;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\UserWithdrawal\UserWithdrawal;
use Src\Domain\Api\Requests\UserWithdrawal\EditRequest;
use Src\Domain\Api\Services\UserWithdrawalService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\UserWithdrawal
 */
class EditController extends Controller
{
    /**
     * withdrawal
     *
     * @param EditRequest $request
     * @param UserWithdrawalService $user_withdrawal_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function withdrawal(EditRequest $request, UserWithdrawalService $user_withdrawal_service): JsonResponse
    {
        /** @var UserWithdrawal $user_withdrawal */
        $user_withdrawal = $user_withdrawal_service->withdrawal($this->author(), $request->validatedForm());
        if ($user_withdrawal === false) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $user_withdrawal);
    }

}
