<?php

namespace Src\Domain\Api\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Models\Job\JobSearchForm;
use Src\Domain\Api\Services\JobService;
use Src\Enums\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class JobController
 * @package Src\Domain\Api\Controllers
 */
class JobController extends Controller
{
    protected JobService $service;

    public function __construct(JobService $service)
    {
        $this->service = $service;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $form = new JobSearchForm($request->all());
        $result = $this->service->fetchAll($form);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Show
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $result = $this->service->findOrFail($id);

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * List Job Apply
     *
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function listJobUserApplied(): JsonResponse
    {
        $result = $this->service->listJobUserApplied($this->author());

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * Cancel Job Applied
     *
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function cancelJobApplied(): JsonResponse
    {
        $result = $this->service->cancelJobApplied($this->author());

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('api_errors.job.cancel_job_fails'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }
}
