<?php

namespace Src\Domain\Api\Controllers\SettingParty;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\SettingParty\SettingParty;
use Src\Domain\Api\Services\SettingParty\UpdateService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;
use Throwable;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\SettingParty
 */
class EditController extends Controller
{

    /**
     * joinSettingParty
     *
     * @param UpdateService $setting_party_service
     * @param int $setting_party_id
     * @return JsonResponse
     * @throws AuthenticationException
     * @throws Throwable
     */
    public function joinSettingParty(UpdateService $setting_party_service, int $setting_party_id): JsonResponse
    {
        /** @var SettingParty $setting_party */
        $setting_party = $setting_party_service->joinSettingParty($this->author(), $setting_party_id);
        if (!$setting_party) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $setting_party->toDetailApiResponse());
    }

}
