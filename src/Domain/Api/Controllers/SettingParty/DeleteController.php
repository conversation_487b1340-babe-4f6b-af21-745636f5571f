<?php

namespace Src\Domain\Api\Controllers\SettingParty;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\SettingParty\DeleteService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class DeleteController
 * @package Src\Domain\Api\Controllers\SettingParty
 */
class DeleteController extends Controller
{

    /**
     * decline
     *
     * @param DeleteService $setting_party_service
     * @param int $setting_party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function decline(DeleteService $setting_party_service, int $setting_party_id): JsonResponse
    {
        $result = $setting_party_service->decline($setting_party_id, $this->author());
        if ($result == false) {
            return json_response(ResultCode::ERROR, null, __('flash.update.delete'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * delete
     *
     * @param DeleteService $setting_party_service
     * @param int $setting_party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function delete(DeleteService $setting_party_service, int $setting_party_id): JsonResponse
    {
        $result = $setting_party_service->delete($setting_party_id, $this->author());
        if ($result == false) {
            return json_response(ResultCode::ERROR, null, __('flash.update.delete'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }
}
