<?php

namespace Src\Domain\Api\Controllers\SettingParty;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Requests\SettingParty\WaitingRequest;
use Src\Domain\Api\Services\SettingParty\ReadService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\SettingParty
 */
class ViewController extends Controller
{
    /**
     * participant
     *
     * @param Request $request
     * @param ReadService $setting_party_service
     * @param int $setting_party_id
     * @return JsonResponse
     */
    public function participant(Request $request, ReadService $setting_party_service, int $setting_party_id): JsonResponse
    {
        $setting_parties = $setting_party_service->fetchAllParticipants($setting_party_id);
        return json_response(ResultCode::SUCCESS, $setting_parties);
    }

    /**
     * participantWithOther
     *
     * @param Request $request
     * @param ReadService $setting_party_service
     * @param int $setting_party_id
     * @return JsonResponse
     */
    public function participantWithOther(Request $request, ReadService $setting_party_service, int $setting_party_id): JsonResponse
    {
        $setting_parties = $setting_party_service->fetchAllParticipantWithOther($setting_party_id);
        return json_response(ResultCode::SUCCESS, $setting_parties);
    }

    /**
     * listSettingPartyOrPartyOfUser
     *
     * @param Request $request
     * @param ReadService $setting_party_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function listSettingPartyOrPartyOfUser(Request $request, ReadService $setting_party_service): JsonResponse
    {
        $author = $this->author();
        $setting_parties = $setting_party_service->listSettingGroupOfUser($author->getId());
        return json_response(ResultCode::SUCCESS, $setting_parties);
    }

    /**
     * listSettingPartyWaiting
     *
     * @param WaitingRequest $request
     * @param ReadService $setting_party_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function listSettingPartyWaiting(WaitingRequest $request, ReadService $setting_party_service): JsonResponse
    {
        $author = $this->author();
        $setting_parties = $setting_party_service->listGroupWaiting($author, $request->validatedForm());
        return json_response(ResultCode::SUCCESS, $setting_parties);
    }

}
