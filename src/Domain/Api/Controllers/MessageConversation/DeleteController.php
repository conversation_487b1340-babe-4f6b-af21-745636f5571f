<?php

namespace Src\Domain\Api\Controllers\MessageConversation;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\MessageConversationService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class DeleteController
 * @package Src\Domain\Api\Controllers\MessageConversation
 */
class DeleteController extends Controller
{
    /**
     * delete
     *
     * @param MessageConversationService $message_conversation_service
     * @param int $partner_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function delete(Request $request, MessageConversationService $message_conversation_service): JsonResponse
    {
        $partner_id = $request->input('partner_id');
        if (!$message_conversation_service->delete($this->author(), $partner_id)) {
            return json_response(ResultCode::ERROR, null, __('flash.update.delete'));
        }

        return json_response(ResultCode::SUCCESS, null);
    }
}
