<?php

namespace Src\Domain\Api\Controllers\MessageConversation;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\MessageConversationService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\MessageConversation
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param MessageConversationService $message_conversation_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function index(Request $request, MessageConversationService $message_conversation_service): JsonResponse
    {
        $message_conversations = $message_conversation_service->fetchAll($this->author());
        return json_response(ResultCode::SUCCESS, $message_conversations);
    }

}
