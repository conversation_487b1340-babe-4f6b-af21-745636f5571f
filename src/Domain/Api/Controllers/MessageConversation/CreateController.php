<?php

namespace Src\Domain\Api\Controllers\MessageConversation;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\MessageConversation\MessageConversation;
use Src\Domain\Api\Requests\MessageConversation\CreateRequest;
use Src\Domain\Api\Services\MessageConversationService;
use Src\Enum\ResultCode;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\MessageConversation
 */
class CreateController extends Controller
{
    /**
     * Store
     *
     * @param CreateRequest $request
     * @param MessageConversationService $message_conversation_service
     * @return JsonResponse
     */
    public function store(CreateRequest $request, MessageConversationService $message_conversation_service): JsonResponse
    {
        /** @var MessageConversation $message_conversation */
        $message_conversation = $message_conversation_service->store($request->validatedForm());
        if (!$message_conversation) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $message_conversation->toDetailApiResponse());
    }
}
