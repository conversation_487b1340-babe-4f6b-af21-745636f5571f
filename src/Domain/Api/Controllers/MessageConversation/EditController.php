<?php

namespace Src\Domain\Api\Controllers\MessageConversation;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\MessageConversation\MessageConversation;
use Src\Domain\Api\Services\MessageConversationService;
use Src\Enum\ResultCode;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\MessageConversation
 */
class EditController extends Controller
{

    public function readAllMessage(Request $request, MessageConversationService $message_conversation_service): JsonResponse
    {
        $partner_id = $request->input('partner_id');

        /** @var MessageConversation $result */
        $result = $message_conversation_service->readAllMessage($this->author(), $partner_id);
        if ($result === false) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }
}
