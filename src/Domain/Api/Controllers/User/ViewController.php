<?php

namespace Src\Domain\Api\Controllers\User;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Requests\User\CheckExistLoginIdRequest;
use Src\Domain\Api\Services\UserService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\User
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param UserService $service
     * @return JsonResponse
     */
    public function index(Request $request, UserService $service): JsonResponse
    {
        $keyword = $request->input('keyword');
        $paginator = $service->fetchPage($keyword);
        return json_response(ResultCode::SUCCESS, $paginator);
    }

    /**
     * checkExistLoginId
     *
     * @param CheckExistLoginIdRequest $request
     * @param UserService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function checkExistLoginId(CheckExistLoginIdRequest $request, UserService $service): JsonResponse
    {
        $users = $service->checkExistLoginId($request->validatedForm());
        return json_response(ResultCode::SUCCESS, $users);
    }

    /**
     * findUserByMemberId
     *
     * @param UserService $service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function findUserByMemberId(UserService $service, string $member_id): JsonResponse
    {
        $user = $service->findUserByMemberId($this->author(), $member_id);
        return json_response(ResultCode::SUCCESS, $user);
    }


}
