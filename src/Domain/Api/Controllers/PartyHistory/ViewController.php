<?php

namespace Src\Domain\Api\Controllers\PartyHistory;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\PartyHistoryService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\Party
 */
class ViewController extends Controller
{

    /**
     * partyHistories
     *
     * @param PartyHistoryService $party_history_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function partyHistories(PartyHistoryService $party_history_service): JsonResponse
    {
        $parties = $party_history_service->partyHistories($this->author());
        return json_response(ResultCode::SUCCESS, $parties);
    }

    /**
     * partyHistoryDetail
     *
     * @param PartyHistoryService $party_history_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function partyHistoryDetail(PartyHistoryService $party_history_service, int $party_id)
    {
        $party = $party_history_service->findOrFail($this->author(), $party_id);
        return json_response(ResultCode::SUCCESS, $party);
    }

}
