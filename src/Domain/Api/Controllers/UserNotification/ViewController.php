<?php

namespace Src\Domain\Api\Controllers\UserNotification;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\UserNotificationService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\UserNotification
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param UserNotificationService $user_notification_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function index(Request $request, UserNotificationService $user_notification_service): JsonResponse
    {
        $user_notifications = $user_notification_service->fetchAll($this->author());
        return json_response(ResultCode::SUCCESS, $user_notifications);
    }

    /**
     * show
     *
     * @param UserNotificationService $user_notification_service
     * @param int $user_notification_id
     * @return Factory|View
     * @throws AuthenticationException
     */
    public function show(UserNotificationService $user_notification_service, int $user_notification_id)
    {
        $user_notification = $user_notification_service->findOrFail($this->author(), $user_notification_id);
        return json_response(ResultCode::SUCCESS, $user_notification);
    }
}
