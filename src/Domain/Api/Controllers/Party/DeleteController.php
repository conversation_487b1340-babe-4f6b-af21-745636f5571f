<?php

namespace Src\Domain\Api\Controllers\Party;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\Party\DeleteService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class DeleteController
 * @package Src\Domain\Api\Controllers\Party
 */
class DeleteController extends Controller
{

    /**
     * update
     *
     * @param Request $request
     * @param DeleteService $party_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function end(Request $request, DeleteService $party_service, int $party_id): JsonResponse
    {
        /** @var Party $party */
        $party = $party_service->end($this->author(), $party_id);
        if (!$party) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $party->toDetailApiResponse());
    }

    /**
     * delete
     *
     * @param DeleteService $party_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function delete(DeleteService $party_service, int $party_id): JsonResponse
    {
        if (!$party_service->delete($this->author(), $party_id)) {
            return json_response(ResultCode::ERROR, null, __('flash.update.delete'));
        }

        return json_response(ResultCode::SUCCESS, null);
    }

}
