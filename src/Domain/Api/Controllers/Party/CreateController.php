<?php

namespace Src\Domain\Api\Controllers\Party;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Requests\Party\CreateRequest;
use Src\Domain\Api\Requests\Party\CreateWithFriendRequest;
use Src\Domain\Api\Services\Party\CreateService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\Party
 */
class CreateController extends Controller
{
    /**
     * Matching
     *
     * @param CreateRequest $request
     * @param CreateService $party_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function matching(CreateRequest $request, CreateService $party_service): JsonResponse
    {
        /** @var Party $result */
        $result = $party_service->matching($this->author(), $request->validatedForm());

        if ($result === false) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * createWithFriend
     *
     * @param CreateWithFriendRequest $request
     * @param CreateService $party_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function createWithFriend(CreateWithFriendRequest $request, CreateService $party_service): JsonResponse
    {
        /** @var Party $result */
        $result = $party_service->createWithFriend($this->author(), $request->validatedForm());

        if ($result === false) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

}
