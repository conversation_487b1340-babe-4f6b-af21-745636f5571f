<?php

namespace Src\Domain\Api\Controllers\Party;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\Party\UpdateService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\Party
 */
class EditController extends Controller
{

    /**
     * join
     *
     * @param UpdateService $party_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function join(UpdateService $party_service, int $party_id): JsonResponse
    {
        /** @var Party $party */
        $party = $party_service->joinParty($this->author(), $party_id);
        if (!$party) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $party);
    }

    /**
     * declineJoinParty
     *
     * @param UpdateService $party_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function declineJoinParty(UpdateService $party_service, int $party_id): JsonResponse
    {
        /** @var Party $party */
        $party = $party_service->declineJoinParty($this->author(), $party_id);
        if (!$party) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $party);
    }

    /**
     * leave
     *
     * @param UpdateService $party_service
     * @param int $party_id
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function leave(UpdateService $party_service, int $party_id): JsonResponse
    {
        /** @var Party $party */
        $party = $party_service->leave($this->author(), $party_id);
        if (!$party) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }
        return json_response(ResultCode::SUCCESS, $party);
    }
}
