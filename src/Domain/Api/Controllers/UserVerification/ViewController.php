<?php

namespace Src\Domain\Api\Controllers\UserVerification;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\UserVerificationService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\UserVerification
 */
class ViewController extends Controller
{

    /**
     * show
     *
     * @param UserVerificationService $user_verification_service
     * @return Factory|View
     * @throws AuthenticationException
     */
    public function show(UserVerificationService $user_verification_service)
    {
        $result = $user_verification_service->findOrFail($this->author());
        return json_response(ResultCode::SUCCESS, $result);
    }
}
