<?php

namespace Src\Domain\Api\Controllers\UserProfile;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\UserProfileService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\UserProfile
 */
class ViewController extends Controller
{
    /**
     * show
     *
     * @param UserProfileService $user_profile_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function show(UserProfileService $user_profile_service): JsonResponse
    {
        $author = $this->author();
        $result = $user_profile_service->findOrFail($author);
        return json_response(ResultCode::SUCCESS, $result);
    }
}
