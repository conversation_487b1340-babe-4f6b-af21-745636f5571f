<?php

namespace Src\Domain\Api\Controllers\UserProfile;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Requests\UserProfile\UpdateRequest;
use Src\Domain\Api\Services\UserProfileService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class EditController
 * @package Src\Domain\Api\Controllers\UserProfile
 */
class EditController extends Controller
{
    /**
     * update
     *
     * @param UpdateRequest $request
     * @param UserProfileService $user_profile_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function update(UpdateRequest $request, UserProfileService $user_profile_service): JsonResponse
    {
        $author = $this->author();
        [$error, $result] = $user_profile_service->update($request->validated(), $author->getId());
        if ($error) {
            return json_response(ResultCode::ERROR, null, $error);
        }

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.update.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }
}
