<?php

namespace Src\Domain\Api\Controllers\UserReport;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Models\UserReport\UserReport;
use Src\Domain\Api\Requests\UserReport\CreateRequest;
use Src\Domain\Api\Services\UserReportService;
use Src\Enum\ResultCode;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\UserReport
 */
class CreateController extends Controller
{
    /**
     * Store
     *
     * @param CreateRequest $request
     * @param UserReportService $user_report_service
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function store(CreateRequest $request, UserReportService $user_report_service): JsonResponse
    {
        /** @var UserReport $user_report */
        $user_report = $user_report_service->store($this->author(), $request->validatedForm());
        if (!$user_report) {
            return json_response(ResultCode::ERROR, null, __('flash.store.failed'));
        }
        return json_response(ResultCode::SUCCESS, $user_report->toDetailApiResponse());
    }
}
