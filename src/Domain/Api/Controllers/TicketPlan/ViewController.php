<?php

namespace Src\Domain\Api\Controllers\TicketPlan;

use Illuminate\Http\JsonResponse;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Services\TicketPlanService;
use Src\Enum\ResultCode;

/**
 * Class ViewController
 * @package Src\Domain\Api\Controllers\TicketPlan
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param TicketPlanService $ticket_plan_service
     * @return JsonResponse
     */
    public function index(TicketPlanService $ticket_plan_service): JsonResponse
    {
        $ticket_plans = $ticket_plan_service->fetchAll();
        return json_response(ResultCode::SUCCESS, $ticket_plans);
    }

}
