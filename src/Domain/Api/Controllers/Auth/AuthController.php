<?php

namespace Src\Domain\Api\Controllers\Auth;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Src\Domain\Api\Controllers\Controller;
use Src\Domain\Api\Requests\Auth\LoginRequest;
use Src\Domain\Api\Requests\Auth\LoginSocialRequest;
use Src\Domain\Api\Requests\UserProfile\CreateProfileRequest;
use Src\Domain\Api\Services\AuthService;
use Src\Enum\ResultCode;
use Src\Enum\SocialProvider;
use Src\Exception\AuthenticationException;
use Throwable;

/**
 * Class AuthController
 * @package Src\Domain\Api\Controllers\Auth
 */
class AuthController extends Controller
{

    /**
     * login
     *
     * @param LoginRequest $request
     * @param AuthService $service
     * @return JsonResponse
     */
    public function login(LoginRequest $request, AuthService $service): JsonResponse
    {
        $result = $service->login($request->validatedForm());
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.login.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * registerSocial
     *
     * @param LoginSocialRequest $request
     * @param AuthService $service
     * @param $provider
     * @return JsonResponse
     * @throws Throwable
     */
    public function registerSocial(LoginSocialRequest $request, CreateProfileRequest $createProfileRequest, AuthService $service, $provider): JsonResponse
    {

        $result = $service->createSocial($request->validatedForm(), $createProfileRequest->validatedForm(), $provider);
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.login.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }


    /**
     * loginSocial
     *
     * @param LoginSocialRequest $request
     * @param AuthService $service
     * @param $provider
     * @return JsonResponse
     */
    public function loginSocial(LoginSocialRequest $request, AuthService $service, $provider): JsonResponse
    {

        $result = $service->loginSocial($request->validatedForm(), $provider);
        if ($result === null) {
            return json_response(ResultCode::SUCCESS, null);
        }

        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.login.failed'));
        }

        return json_response(ResultCode::SUCCESS, $result);
    }

    /**
     * logout
     *
     * @param Request $request
     * @return JsonResponse
     * @throws AuthenticationException
     */
    public function logout(Request $request, AuthService $service): JsonResponse
    {
        $result = $service->logout($this->author());
        if (!$result) {
            return json_response(ResultCode::ERROR, null, __('flash.login.failed'));
        }
        return json_response(ResultCode::SUCCESS, $result);
    }

}
