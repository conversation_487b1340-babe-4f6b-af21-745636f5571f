<?php

namespace Src\Domain\Api\Docs\Schemas;

use OpenApi\Attributes as OA;

#[OA\Schema(
    schema: 'ListJobItem',
    properties: [
        new OA\Property(property: 'id', type: 'integer'),
        new OA\Property(property: 'thumbnailUrl', type: 'string'),
        new OA\Property(property: 'employerEmail', type: 'string'),
        new OA\Property(property: 'isPublic', type: 'boolean'),
        new OA\Property(property: 'isInstant', type: 'boolean'),
        new OA\Property(property: 'title', type: 'string'),
        new OA\Property(property: 'quantity', type: 'number'),
        new OA\Property(property: 'salary', type: 'number'),
        new OA\Property(property: 'salaryType', type: 'string'),
        new OA\Property(property: 'prefecture', type: 'string'),
        new OA\Property(property: 'isApplication', type: 'boolean'),
        new OA\Property(property: 'isFavorite', type: 'boolean'),
        new OA\Property(property: 'createdAt', type: 'string', format: 'date-time'),
    ]
)]
#[OA\Schema(
    schema: 'JobItem',
    properties: [
        new OA\Property(property: 'id', type: 'number'),
        new OA\Property(property: 'recruitmentType', type: 'string'),
        new OA\Property(property: 'thumbnailId', type: 'string'),
        new OA\Property(property: 'thumbnailUrl', type: 'string'),
        new OA\Property(property: 'employerEmail', type: 'string'),
        new OA\Property(property: 'categoryId', type: 'number'),
        new OA\Property(property: 'categoryName', type: 'string'),
        new OA\Property(property: 'type', type: 'string'),
        new OA\Property(property: 'isPublic', type: 'boolean'),
        new OA\Property(property: 'isInstant', type: 'boolean'),
        new OA\Property(property: 'title', type: 'string'),
        new OA\Property(property: 'description', type: 'string'),
        new OA\Property(property: 'benefits', type: 'string', nullable: true),
        new OA\Property(property: 'timeStart', type: 'string', format: 'time', nullable: true),
        new OA\Property(property: 'timeEnd', type: 'string', format: 'time', nullable: true),
        new OA\Property(property: 'age', type: 'number', nullable: true),
        new OA\Property(property: 'gender', type: 'string'),
        new OA\Property(property: 'genderName', type: 'string'),
        new OA\Property(property: 'quantity', type: 'number'),
        new OA\Property(property: 'certificateLevel', type: 'string'),
        new OA\Property(property: 'prefecture', type: 'string'),
        new OA\Property(property: 'address', type: 'string', nullable: true),
        new OA\Property(property: 'salaryType', type: 'string', nullable: true),
        new OA\Property(property: 'salary', type: 'number', nullable: true),
        new OA\Property(property: 'travelFeeType', type: 'string', nullable: true),
        new OA\Property(property: 'travelFee', type: 'number', nullable: true),
        new OA\Property(property: 'isJobApply', type: 'boolean'),
        new OA\Property(property: 'statusJobApply', type: 'string'),
        new OA\Property(property: 'isFavorite', type: 'boolean'),
        new OA\Property(property: 'recruitStartAt', type: 'string', format: 'date-time'),
        new OA\Property(property: 'recruitExpiredAt', type: 'string', format: 'date-time'),
    ]
)]

#[OA\Schema(
    schema: 'ListJobResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(
                    property: 'data',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/ListJobItem')
                ),
                new OA\Property(
                    property: 'paginator',
                    type: 'array',
                    items: new OA\Items(ref: '#/components/schemas/Paginator')
                ),
            ]
        ),
    ]
)]

#[OA\Schema(
    schema: 'JobDetailResponse',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            properties: [
                new OA\Property(property: 'job', ref: '#/components/schemas/JobItem')
            ],
        ),
    ]
)]

#[OA\Schema(
    schema: 'CancelJobAppliedResponseSuccess',
    properties: [
        new OA\Property(property: 'result_code', type: 'integer', example: 200),
        new OA\Property(
            property: 'result_detail',
            type: 'boolean',
        ),
    ]
)]

class JobSchema {}
