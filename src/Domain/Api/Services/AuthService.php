<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\SocialUser;
use App\Eloquent\User;
use Carbon\Carbon;
use DB;
use Google_Client;
use Illuminate\Database\Eloquent\Builder;
use Lara<PERSON>\Socialite\Contracts\User as ProviderUser;
use Laravel\Socialite\Facades\Socialite;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Auth\Form\LoginForm;
use Src\Domain\Api\Models\Auth\Form\LoginSocialForm;
use Src\Domain\Api\Models\User\User as UserDetail;
use Src\Domain\Api\Models\UserProfile\UserCreateProfileForm;
use Src\Domain\Api\Services\Traits\UserTrait;
use Src\Domain\Constants\Auth;
use Src\Enum\DeviceRegister;
use Src\Enum\Gender;
use Src\Enum\LoginIdType;
use Src\Enum\MethodRegister;
use Src\Enum\MobilePlatform;
use Src\Exception\APIRuntimeException;
use Src\Traits\Utils\FileUpload\FileUploadable;
use Throwable;

/**
 * Class AuthService
 * @package Src\Domain\Api\Services
 */
class AuthService
{
    use FileUploadable;
    use UserTrait;

    /**
     * login
     *
     * @param LoginForm $form
     * @return array|bool
     */
    public function login(LoginForm $form)
    {
//        dd($form->getLoginId(),$form->getPassword());
        $result = false;
        $credentials = [
            'login_id' => $form->getLoginId(),
            'password' => $form->getPassword()
        ];
        if (!auth('user')->attempt($credentials)) {
            logger()->debug('Unauthorized', $form->toArray());
            return $result;
        }

        try {
            $var_this = $this;
            $result = DB::transaction(static function () use ($form, $var_this) {
                /** @var User $user */
                $user = auth('user')->user();
                $token_result = $user->createToken('Personal Access Token');
                $token = $token_result->token;
                $token->expires_at = Carbon::now()->addYears(Auth::TOKEN_EXPIRES_YEAR);
                $token->save();
                if (!$token) {
                    logger()->debug('failed to save token', $form->toArray());
                    return false;
                }

                // Send ticket register by web
                if ($user->device_register === 1) {
                    if ($user->web_present_processed === false && $user->userProfile->gender === Gender::MALE) {
                        $user->userTicket->updateOrThrow(['ticket' => 1]);
                        $user->updateOrThrow(['web_present_processed' => true]);
                    }
                }

                // Create voip arn for ios
                $user_voip = $var_this->checkCreateArnForVoip($user, $form );

                // Update user
                $user->update($form->updateDeviceAttributes());
                logger()->info('success update device token.', $form->toArray());

                $user->refresh();
                return array_merge(UserDetail::getUserAndProfile($user, true), [
                    'api_token' => $token_result->accessToken,
                    'token_type' => 'Bearer',
                    'expires_at' => Carbon::parse($token_result->token->expires_at)->toDateTimeString()
                ], $user_voip);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed login', $form->toArray());
        }
        return $result;
    }

    /**
     * logout
     *
     * @param AuthorInterface $author
     * @return bool|mixed
     */
    public function logout(AuthorInterface $author)
    {
        $result = false;
        try {
            $result = DB::transaction(static function () use ($author) {
                /** @var User $user */
                $user = User::queryModel()->with('userVoip')->findOrFail($author->getId());

                 // Remove voip
                 if($user->mobile_platform === MobilePlatform::IOS && $user->userVoip){
//                     $sns_service = new PushVoipService();
//                     $sns_service->deleteEndpoint($user->userVoip->endpoint_arn);
                     $user->userVoip()->delete();
                     logger()->info('success delete voip info', ['endpoint_arn' => $user->userVoip->endpoint_arn]);
                 }

                logger()->info('success logout.', ['user_id' => $author->getId()]);

                // Revoke token
                $author->getToken()->revoke();

                return ['user_id' => $author->getId()];
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed logout');
        }
        return $result;
    }

    /**
     * createSocial
     *
     * @param LoginSocialForm $form
     * @param UserCreateProfileForm $formProfile
     * @param $provider
     * @return bool|mixed
     * @throws Throwable
     */
    public function createSocial(LoginSocialForm $form, UserCreateProfileForm $formProfile, $provider)
    {
        $result = false;

        try {
            $var_this = $this;
            $result = DB::transaction(static function () use ($form, $formProfile, $provider, $var_this) {
                /** @var User $user */
                $user = $var_this->createOrGetUser($form, $formProfile, $provider, false);
                if (!$user) {
                    logger()->debug('Cant create social user', [$form->toArray()]);
                    throw new APIRuntimeException('api_errors.auth.login_failed');
                }

                $token_result = $user->createToken('Personal Access Token');
                $token = $token_result->token;
                $token->expires_at = Carbon::now()->addYears(Auth::TOKEN_EXPIRES_YEAR);
                $token->save();

                if (!$token) {
                    logger()->debug('failed to save token', $form->toArray());
                    return false;
                }

                $user->update($form->updateDeviceAttributes());
                logger()->info('success update device token.', $form->toArray());

                return array_merge(UserDetail::getUserAndProfile($user, true), [
                    'api_token' => $token_result->accessToken,
                    'token_type' => 'Bearer',
                    'expires_at' => Carbon::parse($token_result->token->expires_at)->toDateTimeString()
                ]);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            throw_if_api_exception($e);
            logger()->debug('failed login', $form->toArray());
        }
        return $result;
    }

    /**
     * createOrGetUser
     *
     * @param LoginSocialForm $form
     * @param UserCreateProfileForm|null $formProfile
     * @param $provider
     * @param $is_login
     * @return User|null
     * @throws Throwable
     */
    private function createOrGetUser(LoginSocialForm $form, ?UserCreateProfileForm $formProfile, $provider, $is_login): ?User
    {
//        $user = null;
//        $is_exist_user = false;
        try {
            $var_this = $this;
            $result = DB::transaction(static function () use ($form, $formProfile, $provider, $is_login, $var_this) {
                $provider_user = $var_this->getProviderUser($form, $formProfile, $provider, $is_login);

                if (!$provider_user) {
                    logger()->debug('Invalid social token', [$form->toArray()]);
                    throw new APIRuntimeException('api_errors.auth.login_failed');
                }

                $social_user = SocialUser::query()->getModel()
                    ->where('provider', $provider)
                    ->where('provider_user_id', $provider_user['provider_user_id'])
                    ->first();
                logger()->debug('FINNN', [$social_user]);
                if ($social_user) {
                    // return [$social_user->user, $is_exist_user];
                    logger()->debug('USER EXIST', [$social_user->user]);
                    return $social_user->user;
                } else {
                    if ($is_login) {
                        logger()->debug('REturn login ', []);
                        return null;
                    }

                    $social_user = new SocialUser([
                        'provider_user_id' => $provider_user['provider_user_id'],
                        'provider' => $provider
                    ]);

                    $user = $var_this->userQuery()
                        ->where('login_id', $provider_user['login_id'])
                        ->first();
                    logger()->debug('USERR', [$user]);
                    if (!$user) {
                        // Create user
                        /** @var User $user */
                        $user = $var_this->userQuery()->createOrThrow([
                            'login_id' => $provider_user['login_id'],
                            'login_id_type' => LoginIdType::EMAIL,
                            'password' => bcrypt(str_random(8)),
                            'member_id' => $var_this->genNewMemberId(),
                            'mobile_platform' => 1,
                            'device_register' => DeviceRegister::APP,
                            'method_register' => $var_this->getMethodRegisterFromProvider($provider),
                            'registered_at' => now()
                        ]);

                        // Create profile
                        $user->userProfile()->create(array_merge($formProfile->createAttributes(), [
                            'nickname' => $provider_user['nickname']
                        ]));

                        // Update avatar
                        /* $avatar_url = $provider_user['avatar'];
                        if ($avatar_url && !empty($avatar_url)) {
                            logger()->debug('GET_AVATAR ', [$avatar_url]);
                            $avatar_attributes = $var_this->putFileToStorageFromUrl($avatar_url, 'uploads/user');
                            $storage = StorageFile::query()->getModel()->createOrThrow($avatar_attributes);
                            $user->userProfile()->update([
                                'avatar_id' => $storage->id
                            ]);
                        }*/

                        // Init data
                        $user->userTicket()->create(['ticket' => 0]);
                        $user->userPoint()->create(['point' => 0]);
                        $user->userNotificationUnread()->create(['number_unread_notification' => 0]);
                    }

                    $user->socialUsers()->updateOrCreate(['user_id' => $user->id], [
                        'provider_user_id' => $provider_user['provider_user_id'],
                        'provider' => $provider
                    ]);

                    return $user;
                }
            });
        } catch (Throwable $e) {
            $result = null;
            throw_if_api_exception($e);
            logger()->error('ERROR_GET_PROVIDER', [$e]);
            logger()->debug('failed login', $form->toArray());
        }

        return $result;
    }

    /**
     * getProviderUser
     *
     * @param LoginSocialForm $form
     * @param UserCreateProfileForm|null $formProfile
     * @param $provider
     * @param $is_login
     * @return array|null
     */
    private function getProviderUser(LoginSocialForm $form, ?UserCreateProfileForm $formProfile, $provider, $is_login): ?array
    {
        $user_info = null;
        if ($provider === 'google') {
            $provider_user = $this->verifyGoogleIdToken($form->getSocialToken());
            if ($provider_user) {
                $user_info = [
                    'provider' => $provider,
                    'provider_user_id' => $provider_user['sub'],
                    'login_id' => $provider_user['email'],
                    'nickname' => $provider_user['name'],
                    'avatar' => $provider_user['picture']
                ];
            }
        } else {
            /** @var ProviderUser $provider_user */
            $provider_user = Socialite::driver($provider)->userFromToken($form->getSocialToken());
            if (!$provider_user) {
                logger()->debug('Invalid social token', [$form->toArray()]);
                throw new APIRuntimeException('api_errors.auth.login_failed');
            }

            if ($provider_user) {
                $user_info = [
                    'provider' => $provider,
                    'provider_user_id' => $provider_user->getId(),
                    'login_id' => $provider_user->getEmail() ?? $provider_user->getNickname(),
                    'nickname' => $provider_user->getNickname() ?? $provider_user->getName(),
                    'avatar' => $provider_user->getAvatar()
                ];
            }
        }

        logger()->debug('PROVIDER_USER_INFO', $user_info);
        return $user_info;
    }

    /**
     * verifyGoogleIdToken
     *
     * @param string $id_token
     * @return array|false
     */
    function verifyGoogleIdToken(string $id_token)
    {
        // Get $id_token via HTTPS POST.
        $CLIENT_ID = env('GOOGLE_APP_ID');
        $client = new Google_Client(['client_id' => $CLIENT_ID]);
        $payload = $client->verifyIdToken($id_token);

        if ($payload) {
            logger()->debug('GOOGLE_PAYLOAD TOKEN', $payload);
        } else {
            logger()->debug('INVALID TOKEN', [$id_token]);
        }

        return $payload;
    }

    /**
     * getMethodRegisterFromProvider
     *
     * @param string $provider
     * @return int
     */
    private function getMethodRegisterFromProvider(string $provider)
    {
        switch ($provider) {
            case 'facebook':
                return MethodRegister::FACEBOOK;

            case 'google':
                return MethodRegister::GOOGLE;

            case 'apple':
                return MethodRegister::APPLE;

        }
    }

    /**
     * loginSocial
     *
     * @param LoginSocialForm $form
     * @param $provider
     * @return array|bool
     */
    public function loginSocial(LoginSocialForm $form, $provider)
    {
//        dd($this->verifyGoogleIdToken());
//        return $this->verifyGoogleIdToken($form->getSocialToken());
//        $result = false;

        try {
            $var_this = $this;
            $result = DB::transaction(static function () use ($form, $provider, $var_this) {
                /** @var User $user */
                $user = $var_this->createOrGetUser($form, null, $provider, true);
                if (!$user) {
                    logger()->debug('user not exist, cant login', [$form->toArray()]);
                    return null;
//                    throw new APIRuntimeException('api_errors.auth.exist_other_provider');
                }

                $token_result = $user->createToken('Personal Access Token');
                $token = $token_result->token;
                $token->expires_at = Carbon::now()->addYears(Auth::TOKEN_EXPIRES_YEAR);
                $token->save();

                if (!$token) {
                    logger()->debug('failed to save token', $form->toArray());
                    return false;
                }

                $user->update($form->updateDeviceAttributes());
                logger()->info('success update device token.', $form->toArray());

                return array_merge(UserDetail::getUserAndProfile($user, true), [
                    'api_token' => $token_result->accessToken,
                    'token_type' => 'Bearer',
                    'expires_at' => Carbon::parse($token_result->token->expires_at)->toDateTimeString()
                ]);
            });
        } catch (Throwable $e) {
            $result = null;
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->debug('failed login', $form->toArray());
        }

        return $result;
    }

    /**
     * checkCreateArnForVoip
     *
     * @param User $user
     * @param LoginForm $form
     * @return array
     * @throws \Exception
     */
    private function checkCreateArnForVoip(User $user, LoginForm $form)
    {
        $user_voip = [];
        if ($form->getMobilePlatform() === MobilePlatform::IOS) {
            $str_endpoint_arn = null;
            // Connect AWS
            try {
                $str_endpoint_arn = $this->createVoipEnpointARNForIOS($form->getVoipToken());
            } catch (Throwable $e) {
                logger()->error('ERROR_CREATE_VOIP_ARN', [$e]);
            }

            // Save DB
            if ($str_endpoint_arn) {
                $user->userVoip()->updateOrCreate(['user_id' => $user->id], [
                    'voip_token' => $form->getVoipToken(),
                    'endpoint_arn' => $str_endpoint_arn
                ]);
                $user_voip['user_voip'] = UserDetail::getUserVoip($user);
            } else {
                if ($user->userVoip) {
                    $user->userVoip->delete();
                }
            }
        }

        return $user_voip;
    }

    /**
     * createVoipEnpointARNForIOS
     *
     * @param string $voip_token
     * @return mixed
     */
    private function createVoipEnpointARNForIOS(string $voip_token)
    {
        $sns_service = new PushVoipService();
        $endpoint_arn = $sns_service->createEndPointArn($voip_token);
        return optional($endpoint_arn)['EndpointArn'];
    }

    /**
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

}
