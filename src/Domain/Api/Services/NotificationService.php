<?php

namespace Src\Domain\Api\Services;
use App\Eloquent\Notification;
use App\Eloquent\User;
use App\Exceptions\APIRuntimeException;
use Src\Domain\Api\Models\Notification\NotificationDetail;
use Src\Enums\Boolean;
use Src\Enums\NotificationType;

/**
 * Class NotificationService
 * @package Src\Domain\Api\Services
 */
class NotificationService
{
    /**
     * Fetch announcement
     * @return array
     */
    public function fetchAnnouncement()
    {
        $query = $this->notificationQuery()
            ->where('type', NotificationType::PUBLIC)
            ->where('start_notification_at', '<=', now())
            ->where('end_notification_at', '>=', now())
            ->orderBy('start_notification_at', 'desc');

        $paginator = $query->paginate(10);

        $paginator->getCollection()->transform(function ($item) {
            return (new NotificationDetail($item))->toListAnnouncementResponse();
        });

        return pagination($paginator);
    }

    /**
     * Fetch all notifications for a user
     * @param $author_id
     * @return array
     */
    public function fetchAll($author_id)
    {
        $user = $this->userQuery()->findOrFail($author_id);
        $user_notifications = $user->tNotifications()->orderBy('start_notification_at', 'desc');

        $paginator = $user_notifications->paginate(10);
        $paginator->getCollection()->transform(function ($item) {
            return (new NotificationDetail($item))->toListNotificationResponse();
        });
        return pagination($paginator);
    }

    /**
     * Detail and read notification
     *
     * @param int $author_id
     * @param int $notification_id
     * @return array|bool[]|int[]|null[]
     */
    public function findOrFail(int $author_id, int $notification_id)
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($author_id);
        /** @var Notification $notification */
        $notification = $user->tNotifications()->where('t_notifications.id', $notification_id)->first();

        if (!$notification) {
            throw new APIRuntimeException('api_errors.notification.not_found');
        }

        $user->tNotifications()->updateExistingPivot($notification->id, ['is_read' => Boolean::YES]);

        return (new NotificationDetail($notification))->toListNotificationResponse();
    }

    private function userQuery()
    {
        return User::queryModel();
    }

    private function notificationQuery()
    {
        return Notification::queryModel();
    }
}
