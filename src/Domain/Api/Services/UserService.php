<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Eloquent\VerificationCode as EloquentVerificationCode;
use DB;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\AbstractPaginator;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\User\CheckExistLoginIdForm;
use Src\Domain\Api\Models\User\FCMTokenForm;
use Src\Domain\Api\Models\User\User as UserDetail;
use Src\Domain\Api\Models\User\UserForm;
use Src\Domain\Api\Models\UserProfile\UserCreateProfileForm;
use Src\Domain\Api\Services\Traits\FriendTrait;
use Src\Domain\Api\Services\Traits\UserTrait;
use Src\Domain\Constants\Pagination;
use Src\Enum\ResultCode;
use Src\Utils\GenId;
use Throwable;

/**
 * Class UserService
 * @package Src\Domain\Api\Services
 */
class UserService
{
    use FriendTrait;
    use UserTrait;

    /** limit user  */
    private const LIMIT_USER = 8;

    /**
     * fetchPage
     *
     * @param string|null $keyword
     * @return array
     */
    public function fetchPage(string $keyword = null): array
    {
        $query = $this->userQuery()
            ->where('login_id', 'like', "%$keyword%")
            ->orderByDesc('id');
        $paginator = $query->paginate(Pagination::PER_PAGE);

        /** @var LengthAwarePaginator|AbstractPaginator $paginator */
        $paginator->getCollection()->transform(static function ($user) {
            return (new UserDetail($user))->toDetailApiResponse();
        });

        return pagination_formatter($paginator, ['keyword' => $keyword]);
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery(): User
    {
        return User::query()->getModel();
    }

    /**
     * checkExistLoginId
     *
     * @param CheckExistLoginIdForm $form
     * @return array
     */
    public function checkExistLoginId(CheckExistLoginIdForm $form): array
    {
        /** @var User $user */
        $isExistLoginId = $this->userQuery()->where('login_id', $form->getLoginId())->exists();

        return ['isExistLoginId' => $isExistLoginId];
    }

    /**
     * Store
     *
     * @param UserForm $form
     * @param UserCreateProfileForm $formProfile
     * @return bool|mixed
     */
    public function store(UserForm $form, UserCreateProfileForm $formProfile)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $formProfile) {
                $verification_code = EloquentVerificationCode::query()->getModel()
                    ->where('verify_code', $form->getVerifyCode())
                    ->where('created_at', '>=', now()->subMinutes(5))
                    ->first();

                if (!$verification_code) {
                    return ResultCode::ERROR_INVALID_VERIFICATION_CODE;
                }

                /** @var User $user */
                $user = $this->userQuery()->createOrThrow(array_merge($form->createAttributes(), [
                    'member_id' => $this->genNewMemberId()
                ]));

                // Init data
                $user->userProfile()->create($formProfile->createAttributes());
                $user->userTicket()->create(['ticket' => 0]);
                $user->userPoint()->create(['point' => 0]);
                $user->userNotificationUnread()->create(['number_unread_notification' => 0]);

                $verification_code->delete();

                logger()->info('success create user.', ['users.id' => $user->id]);
                return $user;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user.', $form->toArray());
        }
        return $result;
    }

    /**
     * Update
     *
     * @param UserForm $form
     * @param int $user_id
     * @return bool|mixed
     */
    public function update(UserForm $form, int $user_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $user->updateOrThrow($form->updateAttributes());
                logger()->info('success update user.', compact('user_id'));
                return (new UserDetail($user))->toDetailApiResponse();
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update user', compact('user_id'));
        }
        return $result;
    }

    /**
     * updateFCMToken
     *
     * @param FCMTokenForm $form
     * @param int $user_id
     * @return bool|mixed
     */
    public function updateFCMToken(FCMTokenForm $form, int $user_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $user->updateOrThrow($form->updateAttributes());
                logger()->info('success update fcm token.', compact('user_id'));
                return new UserDetail($user);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update user', compact('user_id'));
        }
        return $result;
    }

    /**
     * delete
     *
     * @param int $user_id
     * @return bool
     */
    public function delete(int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $user->delete();
                logger()->info('success delete user.', compact('user_id'));
                return true;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete user', compact('user_id'));
        }
        return $result;
    }

    /**
     * findUserByMemberId
     *
     * @param AuthorInterface $author
     * @param int $member_id
     * @return array|null
     * @throws Exception
     */
    public function findUserByMemberId(AuthorInterface $author, int $member_id): ?array
    {
        /** @var User $user */
        $user = $this->userQuery()
            ->officalMember()
            ->with('userProfile')
            ->where('member_id', $member_id)
            ->first();

        if ($this->isBlockEachOther($author->getId(), $user->id)) {
            $user = null;
        }

        return $user ? UserDetail::getUserAndProfile($user) : null;
    }

}
