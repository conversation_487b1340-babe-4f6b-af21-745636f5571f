<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Eloquent\User as EloquentUser;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\UserProfile\UserProfile;

/**
 * Class PartnerProfileService
 *
 * @package Src\Domain\Api\Services
 */
class PartnerProfileService
{
    /**
     * find Partner
     *
     * @param int $partner_id
     * @param int $user_id
     * @return array
     */
    public function findPartner(int $partner_id, int $user_id): array
    {
        $error = null;
        /** @var EloquentUser $user */
        $user = $this->userQuery()->findOrFail($user_id);
        if ($this->checkFriend($user, $partner_id) === false) {
            $error = __('models/partner_profile.field.not_friend');
            logger()->error('You are not friend', compact('user_id'));
            return [$error, null];
        }
        /** @var EloquentUser $partner */
        $partner = $user->userFriends()->where('friend_id', $partner_id)->first();
        return [$error, new UserProfile($partner->userProfile)];
    }

    /**
     * userQuery
     *
     * @return Builder|EloquentUser
     */
    private function userQuery()
    {
        return EloquentUser::query()->getModel();
    }

    /**
     * check friend
     *
     * @param EloquentUser $user
     * @param int $partner_id
     * @return bool
     */
    private function checkFriend(User $user, int $partner_id): bool
    {
        $friends = $user->userFriends;
        if (!$friends->isEmpty()) {
            /** @var EloquentUser $friend */
            foreach ($friends as $friend) {
                if ($friend->id === $partner_id) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * findInfoMember
     *
     * @param int $user_id
     * @return array
     */
    public function findInfoMember(int $user_id): array
    {
        $error = null;
        /** @var EloquentUser $user */
        $user = $this->userQuery()->findOrFail($user_id);

        /** @var EloquentUser $partner */
        return [$error, new UserProfile($user->userProfile)];
    }

}
