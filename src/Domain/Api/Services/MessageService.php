<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Message as EloquentMessage;
use App\Eloquent\MessageConversation as EloquentMessageConversation;
use App\Eloquent\User;
use Carbon\Carbon;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Message\Message;
use Src\Domain\Api\Models\Message\MessageForm;
use Src\Domain\Api\Models\User\User as UserDetail;
use Src\Enum\EventType;
use Src\Utils\Util;
use Throwable;

/**
 * Class MessageService
 * @package Src\Domain\Api\Services
 */
class MessageService
{

    /**
     * fetchAll
     *
     * @param AuthorInterface $author
     * @param $partner_id
     * @return array|null
     * @throws Exception
     */
    public function fetchAll(AuthorInterface $author, $partner_id): ?array
    {
        // Get partner
        $partner_model = User::query()->getModel()->with('userProfile.storageFile')->findOrFail($partner_id);
        $partner = UserDetail::getProfile($partner_model);

        // Get conversation
        $message_conversation_id = self::getMessageConversationId($author->getId(), $partner_id);
        if (!$message_conversation_id) {
            return [
                'message_conversation_id' => null,
                'partner' => $partner,
                'messages' => []
            ];
        }

        // Get messages
        $messages = $this->messageQuery()
            ->orderBy('id')
            ->where('message_conversation_id', $message_conversation_id)
            ->whereDoesntHave('deletedMessages', function ($query) use ($author) {
                $query->where('id', $author->getId());
            })
            ->get();

        $message_transform = $messages->transform(static function ($message) {
            return (new Message($message))->toListApiResponse();
        });

        return [
            'message_conversation_id' => $message_conversation_id,
            'partner' => $partner,
            'messages' => $message_transform->toArray()
        ];
    }

    /**
     * getMessageConversationId
     *
     * @param int $author_id
     * @param int $friend_id
     * @return int|null
     */
    public static function getMessageConversationId(int $author_id, int $friend_id): ?int
    {
        $friend_conversations = EloquentMessageConversation::query()->getModel()
            ->whereHas('messageParticipants', function ($query) use ($friend_id) {
                $query->where('id', $friend_id);
            })
            ->whereDoesntHave('deletedMessageConversations', function ($query) use ($author_id) {
                $query->where('id', $author_id);
            })
            ->get();

        $conversation = $friend_conversations->first(function ($conversation, $key) use ($author_id) {
            $is_conversation_with_author = false;
            foreach ($conversation->messageParticipants as $messageParticipant) {
                if ($messageParticipant->id === $author_id) {
                    $is_conversation_with_author = true;
                }
            }
            return $is_conversation_with_author;
        });

        return $conversation ? $conversation->id : null;
    }

    /**
     * messageQuery
     *
     * @return Builder|EloquentMessage
     */
    private function messageQuery()
    {
        return EloquentMessage::query()->getModel();
    }

    /**
     * Store
     *
     * @param MessageForm $form
     * @param AuthorInterface $author
     * @return bool|array
     * @throws Throwable
     */
    public function store(AuthorInterface $author, MessageForm $form)
    {
        $result = false;
        DB::beginTransaction();
        try {
            $partner_id = $form->getPartnerId();
            // Find exist conversation
            $message_conversation_id = self::getMessageConversationId($author->getId(), $partner_id);
            if ($message_conversation_id) {
                /** @var EloquentMessageConversation $message_conversation */
                $message_conversation = $this->messageConversationQuery()->find($message_conversation_id);
                if (!$message_conversation) {
                    $message_conversation = $this->createConversion($author, $partner_id);
                }
            } else {
                $message_conversation = $this->createConversion($author, $partner_id);
            }

            // Check to remove deleted status
            $isDeleted = $message_conversation->deletedMessageConversations()->where('id', $author->getId())->exists();
            if ($isDeleted) {
                $message_conversation->deletedMessageConversations()->detach([$author->getId()]);
            }

            // Create message
            /** @var EloquentMessage $message */
            $message_data = array_merge($form->createAttributes(), ['sender_id' => $author->getId()]);
            $message = $message_conversation->messages()->create($message_data);
            logger()->info('success create message.', ['messages.id' => $message->id]);

            // Update unread, last message for partner
            $partner = $message_conversation->messageParticipants()->where('user_id', $partner_id)->first();
            $curr_number_unread_message = $partner->pivot->number_unread_message;
            $participant_data = [
                'number_unread_message' => ++$curr_number_unread_message,
                'last_message' => $form->getMessage(),
                'last_message_at' => Carbon::now()
            ];
            $message_conversation->messageParticipants()->updateExistingPivot($partner_id, $participant_data);

            // Update last message for self
            $message_conversation->messageParticipants()->updateExistingPivot($author->getId(), [
                'last_message' => $form->getMessage(),
                'last_message_at' => Carbon::now()
            ]);
            logger()->info('success Update unread, last message for partner', ['user_id' => $partner_id]);

            DB::commit();

            // Send socket
            // Send update message
            $addition_data = [
                'message_conversation_id' => $message_conversation->id,
                'channel_id' => $message_conversation->channel_id,
                'message_id' => $message->id,
                'sender_id' => $message->sender_id,
                'partner_id' => $partner_id,
                'sender_name' => $author->getUserProfile()->nickname,
                'message' => $message->message,
                'created_at' => $message->created_at
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushDataWithTitleBody(EventType::MESSAGE_UPDATE_MESSAGE, [$message->sender_id, $partner_id]
                , [$partner->fcm_token], $addition_data, $author->getUserProfile()->nickname, $message->message);

            request_socket_api_and_push_notification('socket_api.message.update_message', $notify_data, $notify_payload);

            // Send update conversation
            $addition_data = [
                'receive_user_id' => $partner_id,
                'message_conversation_id' => $message_conversation->id,
                'channel_id' => $message_conversation->channel_id,
                'sender_id' => $message->sender_id,
                'sender_name' => $author->getUserProfile()->nickname,
            ];

            $notify_data = Util::formatSocketData([$partner_id], $addition_data);

            request_socket_api('socket_api.message.update_message_conversation', $notify_data);
            return (new Message($message))->toDetailApiResponse($message_conversation);
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->info('failed create message.', $form->toArray());
        }

        return $result;
    }

    /**
     * messageConversationQuery
     *
     * @return Builder|EloquentMessageConversation
     */
    private function messageConversationQuery()
    {
        return EloquentMessageConversation::query()->getModel();
    }

    function createConversion(AuthorInterface $author, $user_id)
    {
        /** @var EloquentMessageConversation $message_conversation */
        $message_conversation = $this->messageConversationQuery()->createOrThrow([
            'creator_id' => $author->getId(),
            'channel_id' => 'channel_' . $this->getMillitime()
        ]);

        logger()->info('success create message_conversation.', ['message_conversations.id' => $message_conversation->id]);

        $message_conversation->messageParticipants()->sync([$author->getId(), $user_id]);
        logger()->info('success create message_participans .', [$author->getId(), $user_id]);
        return $message_conversation;
    }

    /**
     * getMillitime
     *
     * @return string
     */
    function getMillitime()
    {
        $microtime = microtime();
        $comps = explode(' ', $microtime);

        return sprintf('%d%03d', $comps[1], $comps[0] * 1000);
    }

    /**
     * Update
     *
     * @param MessageForm $form
     * @param int $message_id
     * @return bool|mixed
     */
    public function update(MessageForm $form, int $message_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($message_id, $form) {
                /** @var EloquentMessage $message */
                $message = $this->messageQuery()->findOrFail($message_id);
                $message->updateOrThrow($form->updateAttributes());
                logger()->info('success update message.', compact($message_id));
                return new Message($message);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update message', compact('message_id'));
        }
        return $result;
    }

    /**
     * delete
     *
     * @param AuthorInterface $author
     * @param int $message_id
     * @return bool
     * @throws Throwable
     */
    public function delete(AuthorInterface $author, int $message_id): bool
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var EloquentMessage $message */
            $message = $this->messageQuery()->findOrFail($message_id);

            $isDeleted = $message->deletedMessages()->where('id', $author->getId())->exists();
            if ($message->sender_id == $author->getId()) {
                $message->delete();

                $message_conversation = $message->messageConversation()->first();

                DB::commit();

                // Send update message
                $addition_data = [
                    'message_conversation_id' => $message_conversation->id,
                    'channel_id' => $message_conversation->channel_id,
                    'message_id' => $message->id,
                    'sender_id' => $message->sender_id,
                    'sender_name' => $author->getUserProfile()->nickname,
                    'message' => $message->message,
                ];
                $notify_data = Util::formatSocketData([$message->sender_id], $addition_data);
                request_socket_api('socket_api.message.deleted_message', $notify_data);
            } else {
                if (!$isDeleted) {
                    $message->deletedMessages()->attach([$author->getId()]);
                }
            }

            DB::commit();
            logger()->info('success delete message.', compact('message_id'));
            return true;
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed delete message', compact('message_id'));
        }
        return $result;
    }

}
