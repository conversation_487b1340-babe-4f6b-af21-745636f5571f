<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\PointPlan as EloquentPointPlan;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Src\Domain\Api\Models\PointPlan\PointPlan;

/**
 * Class PointPlanService
 * @package Src\Domain\Api\Services
 */
class PointPlanService
{
    /**
     * fetchAll
     *
     * @return Collection|PointPlan[]
     */
    public function fetchAll(): Collection
    {
        $point_plans = $this->pointPlanQuery()
            ->orderBy('id')
            ->get();

        return $point_plans->transform(static function ($point_plan) {
            return (new PointPlan($point_plan))->toListApiResponse();
        });
    }

    /**
     * pointPlanQuery
     *
     * @return Builder|EloquentPointPlan
     */
    private function pointPlanQuery()
    {
        return EloquentPointPlan::query()->getModel();
    }

}
