<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\StorageFile;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\UserVerification\UserVerification;
use Src\Domain\Api\Models\UserVerification\UserVerificationForm;
use Src\Enum\UserStatus;
use Src\Traits\Utils\FileUpload\FileUploadable;
use Storage;
use Throwable;


/**
 * Class UserVerificationService
 * @package Src\Domain\Api\Services
 */
class UserVerificationService
{

    use FileUploadable;

    /**
     * find Or Fail
     *
     * @param AuthorInterface $author
     * @return array|null
     */
    public function findOrFail(AuthorInterface $author): ?array
    {
        /** @var User $user */
        $user = $this->userQuery()
            ->with('userVerification.storageFile')
            ->findOrFail($author->getId());

        $user_verification = $user->userVerification;
        return $user_verification ? (new UserVerification($user_verification))->toDetailApiResponse() : null;
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

    /**
     * Update
     *
     * @param AuthorInterface $author
     * @param UserVerificationForm $form
     * @return bool|mixed
     */
    public function update(AuthorInterface $author, UserVerificationForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($author, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($author->getId());

                if (!$user->userVerification()->exists()) {
                    logger()->debug('CREATE');
                    $verification_attributes = $this->putFileToStorage($form->getVerificationImage(), 'uploads/user_verification');
                    /** @var StorageFile $storage */
                    $storage = $this->storageFileQuery()->createOrThrow($verification_attributes);
                    $user->userVerification()->create($form->createAttributes($storage->id));
                } else {
                    logger()->debug('UPDATE');
                    $old_avatar = $user->userVerification->storageFile->file_path;
                    $verification_attributes = $this->putFileToStorage($form->getVerificationImage(), 'uploads/user_verification');
                    $user->userVerification->storageFile()->update($verification_attributes);
                    Storage::disk('public')->delete($old_avatar);

                    $user->userVerification()->update($form->updateAttributes());
                }

                if ($user->user_status === UserStatus::FINISH_UPDATE_PROFILE) {
                    $user->updateOrThrow([
                        'user_status' => UserStatus::FINISH_SEND_IDENTIFICATION
                    ]);
                }

                logger()->info('success update user_verification.', ['user_id' => $author->getId()]);
                return $user->toArray();
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update user_verification', ['user_id' => $author->getId()]);
        }
        return $result;
    }

    /**
     * StorageFile Query
     *
     * @return Builder|StorageFile
     */
    private function storageFileQuery()
    {
        return StorageFile::query()->getModel();
    }

}
