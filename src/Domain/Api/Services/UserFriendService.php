<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\FriendHistory;
use App\Eloquent\FriendTransaction;
use App\Eloquent\User;
use App\Eloquent\UserBlock;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\User\GenderForm;
use Src\Domain\Api\Models\User\User as UserDetail;
use Src\Domain\Api\Models\UserFriend\UserAcceptFriendForm;
use Src\Domain\Api\Models\UserFriend\UserFriendForm;
use Src\Domain\Api\Models\UserFriend\UserInviteFriendForm;
use Src\Domain\Api\Services\Traits\FriendTrait;
use Src\Enum\EventType;
use Src\Enum\NotificationDiv;
use Src\Exception\APIRuntimeException;
use Src\Utils\GenId;
use Src\Utils\Util;
use Throwable;

/**
 * Class UserFriendService
 * @package Src\Domain\Api\Services
 */
class UserFriendService
{
    use FriendTrait;

    /**
     * fetchAll
     *
     * @param int $user_id
     * @param GenderForm $form
     * @return array
     * @throws Exception
     */
    public function fetchAll(int $user_id, GenderForm $form): array
    {
        if ($form->getGenderParty()) {
            $user_friends = $this->getListFriendsOfUser($user_id, 'gender_party', $form->getGenderParty());
        } else {
            $user_friends = $this->getListFriendsOfUser($user_id);
        }

        $list_friends = UserDetail::retrieveFriends($user_friends);
        usort($list_friends, 'sort_list_friends');

        return $list_friends;
    }

    /**
     * fetchFriendParty
     *
     * @param AuthorInterface $author
     * @return array
     * @throws Exception
     */
    public function fetchFriendParty(AuthorInterface $author): array
    {
        $user_friends = $this->getListFriendsOfUser($author->getId(), 'gender_partner', $author->getGenderPartner());
        $list_friends = UserDetail::retrieveFriends($user_friends);
        usort($list_friends, "sort_list_friends");

        return $list_friends;
    }

    /**
     * inviteAddFriend
     *
     * @param UserInviteFriendForm $form
     * @param AuthorInterface $author
     * @return array|bool
     * @throws Throwable
     */
    public function inviteAddFriend(UserInviteFriendForm $form, AuthorInterface $author)
    {
        $result = false;
        try {
            $user_id = $author->getId();
            if (count($form->getFriendIds()) === 1) {
                $friend_id = $form->getFriendIds()[0];
                // check my id != id add friend
                if ($friend_id == $user_id) {
                    throw new APIRuntimeException('api_errors.friend.friend_same_author');
                }

                // Check friend already
                $is_friend_already = $author->getUser()->userFriends()->where('id', $friend_id)->exists();
                /** @var User $friend */
                $friend = $this->userQuery()->with('userProfile')->findOrFail($friend_id);
                if ($is_friend_already) {
                    throw new APIRuntimeException('api_errors.friend.became_friend', ['nickname' => $friend->userProfile->nickname]);
                }

                // Check block each other
                if ($this->isBlockEachOther($user_id, $friend_id)) {
                    throw new APIRuntimeException('api_errors.friend.block_each_other', ['nickname' => $friend->userProfile->nickname]);
                }
            }

            // Send invite
            $friend_transactions = [];
            foreach ($form->getFriendIds() as $friend_id) {
                $friend_transaction = $this->inviteOneFriend($form, $author, $friend_id);
                if ($friend_transaction) {
                    $friend_transactions[] = $friend_transaction;
                }
            }

            return $friend_transactions;
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user_friends.', $form->toArray());
            throw_if_api_exception($e);
        }

        return $result;
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

    /**
     * inviteOneFriend
     *
     * @param UserInviteFriendForm $form
     * @param AuthorInterface $author
     * @param int $friend_id
     * @return bool|int
     * @throws Throwable
     */
    private function inviteOneFriend(UserInviteFriendForm $form, AuthorInterface $author, int $friend_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            $user_id = $author->getId();

            /** @var User $friend */
            $friend = $this->userQuery()->with('userProfile')->findOrFail($friend_id);

            // Check friend already
            $is_friend_already = $author->getUser()->userFriends()->where('id', $friend_id)->exists();
            if ($is_friend_already) {
                logger()->debug('became friend, ignore invite', ['user_id' => $friend->id]);
                return false;
            }

            if ($this->isBlockEachOther($user_id, $friend_id)) {
                logger()->debug('block each other, ignore invite', ['user_id' => $friend->id]);
                return false;
            }

            // check user block
            $user_block_query = UserBlock::query()->getModel();
            $result = $user_block_query
                ->where(function ($query) use ($user_id, $friend_id) {
                    $query->where('user_id', $user_id);
                    $query->where('block_user_id', $friend_id);
                })->orWhere(function ($query) use ($user_id, $friend_id) {
                    $query->where('user_id', $friend_id);
                    $query->where('block_user_id', $user_id);
                })->count();
            if ($result !== 0) {
                logger()->debug('fail to create user_friends.', $form->toArray());
                return false;
            }
            // check my id != id add friend
            if ($friend_id == $user_id) {
                return false;
            }

            // Add point token
            $invite_token = GenId::genInviteFriendToken();
            $friend_transaction = FriendTransaction::queryModel()->createOrThrow([
                'send_user_id' => $user_id,
                'receive_user_id' => $friend_id,
                'invite_token' => $invite_token,
                'party_id' => $form->getPartyId()
            ]);

            // Create push, socket data
            $addition_data = [
                'creator_user_id' => $author->getId(),
                'creator_nickname' => $author->getUserProfile()->nickname,
                'invite_token' => $invite_token
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::FRIEND_INVITE, [$friend_id], [$friend->fcm_token],
                $addition_data, 'friend.invite', ['nickname' => $author->getUserProfile()->nickname]);

            // Create Notification
            $notification_id = UserNotificationService::createUserNotification([$friend->id], NotificationDiv::INVITE_ADD_FRIEND,
                FriendTransaction::getTableName(), $friend_transaction->id, $notify_payload, $notify_data, null, User::getTableName(), $author->getId(), true);
            $notify_data['notification_id'] = $notification_id;

            DB::commit();

            // Send socket
            request_socket_api_and_push_notification('socket_api.friend.invite', $notify_data, $notify_payload);

            logger()->info('success invite add friend.', $form->toArray());

            return $friend_transaction->toArray();
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->info('failed create user_friends.', $form->toArray());
        }

        return $result;
    }

    /**
     * addFriend
     *
     * @param UserAcceptFriendForm $form
     * @param AuthorInterface $author
     * @return mixed
     * @throws Throwable
     */
    public function addFriend(UserAcceptFriendForm $form, AuthorInterface $author)
    {
        $result = false;
        $message = null;
        DB::beginTransaction();
        try {
            // Get data from point token
            /** @var FriendTransaction $friend_transaction */
            $friend_transaction = FriendTransaction::query()->where('invite_token', $form->getInviteToken())->first();

            $friend_id = $friend_transaction->send_user_id;
            $user_id = $friend_transaction->receive_user_id;
            if ($user_id !== $author->getId()) {
                throw new APIRuntimeException('api_errors.friend.not_receive_user');
            }

            /** @var User $user */
            $user = $this->userQuery()->findOrFail($user_id);
            $friend = $this->userQuery()->findOrFail($friend_id);

            // check user block
            $user_block_query = UserBlock::query()->getModel();
            $result_block = $user_block_query
                ->where(function ($query) use ($user_id, $friend_id) {
                    $query->where('user_id', $user_id);
                    $query->where('block_user_id', $friend_id);
                })->orWhere(function ($query) use ($user_id, $friend_id) {
                    $query->where('user_id', $friend_id);
                    $query->where('block_user_id', $user_id);
                })->count();
            if ($result_block !== 0) {
                $message = __('flash.exception.UserIsBlocked');
                logger()->debug('fail to create user_friends.', $form->toArray());
                return false;
            }

            $is_add_friend = false;
            if (!$user->userFriends()->where('id', $friend_id)->exists()) {
                $user->userFriends()->attach([$friend_id]);
                $friend->userFriends()->attach([$user_id]);
                $is_add_friend = true;
                logger()->info('success create user_friends.', $form->toArray());

                // Create friend history
                $friend_history = $this->friendHistoryQuery()->createOrThrow([
                    'party_id' => $friend_transaction->party_id,
                    'send_user_id' => $friend_id,
                    'receive_user_id' => $user_id
                ]);
                logger()->info('success create friend history.', ['id' => $friend_history->id]);

                [$notify_data, $notify_payload] = $this->getPaylooadAndDataForSuccessFriendEvent($user, $friend);
                [$notify_data_friend, $notify_payload_friend] = $this->getPaylooadAndDataForSuccessFriendEvent($friend, $user);

                //  Create Notification
                $notification_id = UserNotificationService::createUserNotification([$user->id], NotificationDiv::ADD_FRIEND,
                    FriendTransaction::getTableName(), $friend_transaction->id, $notify_payload, $notify_data, null, User::getTableName(), $author->getId());
                $notify_data['notification_id'] = $notification_id;

                $notification_id = UserNotificationService::createUserNotification([$friend->id], NotificationDiv::ADD_FRIEND,
                    FriendTransaction::getTableName(), $friend_transaction->id, $notify_payload_friend, $notify_data_friend, null, User::getTableName(), $author->getId());
                $notify_data_friend['notification_id'] = $notification_id;
                logger()->info('success create activity_histories.', $form->toArray());
            }

            // Update action for notification
            UserNotificationService::disableAction(FriendTransaction::getTableName(), $friend_transaction->id);

            // Delete point token
            $friend_transaction->delete();
            DB::commit();

            // Send socket
            if ($is_add_friend) {
                request_socket_api_and_push_notification('socket_api.friend.add_friend_success', $notify_data, $notify_payload);
                request_socket_api_and_push_notification('socket_api.friend.add_friend_success', $notify_data_friend, $notify_payload_friend);
            }

            $result = $is_add_friend ? optional($user->userFriends->first())->pivot : null;
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            throw_if_api_exception($e);
            logger()->info('failed create user_friends.', $form->toArray());
        }
        return $result;
    }

    /**
     * @return Builder|FriendHistory
     */
    private function friendHistoryQuery()
    {
        return FriendHistory::query()->getModel();
    }

    /**
     * getPaylooadAndDataForSuccessFriendEvent
     *
     * @param User $user
     * @param $partner
     * @return array
     * @throws Throwable
     */
    private function getPaylooadAndDataForSuccessFriendEvent(User $user, $partner)
    {
        // Create push, socket data
        [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::FRIEND_ADD_FRIEND_SUCCESS, [$user->id], [$user->fcm_token],
            [], 'friend.invite', ['nickname' => $partner->userProfile->nickname]);

        return [$notify_data, $notify_payload];
    }

    /**
     * removeFriend
     *
     * @param UserFriendForm $form
     * @param int $user_id
     * @return bool
     */
    public function removeFriend(UserFriendForm $form, int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $user_id) {
                $friend_id = $form->getFriendId();
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                if ($user->userFriends()->detach($friend_id)) {
                    logger()->info('success remove user_friends.', $form->toArray());
                }

                /** @var User $friend */
                $friend = $this->userQuery()->findOrFail($friend_id);
                if ($friend->userFriends()->detach($user_id)) {
                    logger()->info('success remove user_friends.', $form->toArray());
                }

                return true;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed remove user_friends', $form->toArray());
        }
        return $result;
    }

}
