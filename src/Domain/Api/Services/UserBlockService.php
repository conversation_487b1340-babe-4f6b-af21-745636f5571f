<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use DB;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\User\GenderForm;
use Src\Domain\Api\Models\User\User as UserDetail;
use Src\Domain\Api\Models\UserBlock\UserBlockForm;
use Src\Domain\Api\Models\UserBlock\UserRemoveBlockForm;
use Throwable;

/**
 * Class UserBlockService
 * @package Src\Domain\Api\Services
 */
class UserBlockService
{

    /**
     * fetchAll
     *
     * @param int $user_id
     * @param GenderForm $form
     * @return array
     * @throws Exception
     */
    public function fetchAll(int $user_id, GenderForm $form): array
    {
        /** @var User $user */
        $user = $this->userQuery()
            ->with(['userBlocks' => function ($query) use ($form) {
                $query->with('userProfile.storageFile')
                    ->whereHas('userProfile', function ($query) use ($form) {
                        $query->when($form->getGenderParty(), function ($query) use ($form) {
                            $query->where('gender_party', $form->getGenderParty());
                        });
                    });
            }])
            ->findOrFail($user_id);

        return (new UserDetail($user))->retrieveBlocks();
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

    /**
     * block
     *
     * @param UserBlockForm $form
     * @param int $user_id
     * @return bool
     */
    public function block(UserBlockForm $form, int $user_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $user_id) {
                $block_results = [];
                foreach ($form->getBlockUserIds() as $block_user_id) {
                    $block_result = $this->blockOneUser($user_id, $block_user_id);
                    if ($block_result) {
                        $block_results[] = $block_result;
                    }
                }

                return $block_results;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user_blocks.', $form->toArray());
        }

        return $result;
    }

    /**
     * blockOneUser
     *
     * @param int $user_id
     * @param int $user_block_id
     * @return bool|mixed
     */
    private function blockOneUser(int $user_id, int $user_block_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id, $user_block_id) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $is_add_block = false;
                if ($user_id !== $user_block_id) {
                    if (!$user->userBlocks()->where('id', $user_block_id)->exists()) {
                        $user->userBlocks()->attach($user_block_id);
                        $is_add_block = true;
                        logger()->info('success create user_blocks.', ['user_block_id' => $user_block_id]);
                    }
                }

                return $is_add_block ? optional($user->userBlocks->first())->pivot : null;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user_blocks.', ['user_block_id' => $user_block_id]);
        }

        return $result;
    }

    /**
     * removeBlock
     *
     * @param UserRemoveBlockForm $form
     * @param int $user_id
     * @return bool
     */
    public function removeBlock(UserRemoveBlockForm $form, int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $user_id) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                if ($user->userBlocks()->detach($form->getBlockUserId()) == 1) {
                    logger()->info('success remove user_blocks.', $form->toArray());
                    return true;
                }
                return false;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed remove user_blocks', $form->toArray());
        }
        return $result;
    }

}
