<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\UserContact as EloquentUserContact;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\UserContact\UserContact;
use Src\Domain\Api\Models\UserContact\UserContactForm;
use Src\Enum\ContactState;
use Throwable;

/**
 * Class UserContactService
 * @package Src\Domain\Api\Services
 */
class UserContactService
{

    /**
     * Store
     *
     * @param UserContactForm $form
     * @param $user_id
     * @return bool|mixed
     */
    public function store(UserContactForm $form, $user_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $user_id) {
                /** @var EloquentUserContact $user_contact */
                $store_data = array_merge($form->createAttributes(ContactState::NOT_SUPPORTED), ['user_id' => $user_id]);
                $user_contact = $this->user_contactQuery()->createOrThrow($store_data);
                logger()->info('success create user_contact.', ['user_contacts.id' => $user_contact->id]);
                return new UserContact($user_contact);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user_contact.', $form->toArray());
        }
        return $result;
    }

    /**
     * user_contactQuery
     *
     * @return Builder|EloquentUserContact
     */
    private function user_contactQuery()
    {
        return EloquentUserContact::query()->getModel();
    }

}
