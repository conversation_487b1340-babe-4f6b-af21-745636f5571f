<?php

namespace Src\Domain\Api\Services\Traits;

use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Enum\Gender;
use Src\Exception\APIRuntimeException;

trait TicketTrait
{

    /**
     * validateTicketToUse
     *
     * @param AuthorInterface $author
     */
    private function validateTicketToUse(AuthorInterface $author)
    {
        if ($author->getGenderParty() === Gender::MALE && $author->getTicket() <= 0) {
            throw new APIRuntimeException('api_errors.ticket.not_enough');
        }
    }

}
