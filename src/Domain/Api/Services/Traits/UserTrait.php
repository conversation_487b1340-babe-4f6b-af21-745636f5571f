<?php

namespace Src\Domain\Api\Services\Traits;

use App\Eloquent\User;
use App\Eloquent\UserVerification;
use Src\Utils\GenId;

trait UserTrait
{

    /**
     * genNewMemberId
     *
     * @return string
     */
    public function genNewMemberId()
    {
        $exist_member_ids = User::queryModel()->get()->pluck('member_id')->toArray();
        return GenId::genMemberId($exist_member_ids);
    }

    /**
     * genNewVerifyCode
     *
     * @return string
     */
    public function genNewVerifyCode()
    {
        $exist_verify_codes = UserVerification::queryModel()->get()->pluck('verify_code')->toArray();
        return GenId::genVerifyCode($exist_verify_codes);
    }

}
