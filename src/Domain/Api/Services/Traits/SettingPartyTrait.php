<?php

namespace Src\Domain\Api\Services\Traits;

use App\Eloquent\SettingParty as EloquentSettingParty;
use App\Eloquent\User;
use Illuminate\Support\Collection;
use Src\Enum\SettingPartyStatus;
use Src\Exception\ErrorException;

trait SettingPartyTrait
{

    /**
     * getListSettingPartyOfUser
     *
     * @param $user_id
     * @return Collection
     */
    public function getListSettingPartyOfUser($user_id): Collection
    {
        $exist_settings = EloquentSettingParty::query()->getModel()
            ->whereHas('joinedParticipants', function ($query) use ($user_id) {
                $query->where('id', $user_id);
            })
            ->where('status', '!=', SettingPartyStatus::PENDING)
            ->get();

        return $exist_settings;
    }

    /**
     * getMatchParties
     *
     * @param array $params
     * @return Collection
     */
    public function getMatchParties(array $params): Collection
    {
        $query = EloquentSettingParty::query()->getModel();

        $setting_party_id = $params['setting_party_id'];
        $group_type = $params['group_type'];
        $from_age = $params['from_age'];
        $to_age = $params['to_age'];
        $from_age_partner = $params['from_age_partner'];
        $to_age_partner = $params['to_age_partner'];
        $gender = $params['gender'];
        $gender_partner = $params['gender_partner'];

        $match_setting_parties = $query
            ->when($setting_party_id, function ($query) use ($setting_party_id) {
                return $query->where('id', '!=', $setting_party_id);
            })
            ->when($group_type, function ($query) use ($group_type) {
                return $query->where('group_type', $group_type);
            })
            ->when(!env('APP_TEST_LOCAL', false), function ($query) {
                return $query->where('status', SettingPartyStatus::WAIT_MATCHING);
            })
            ->where('from_age', '>=', $from_age_partner)
            ->where('to_age', '<=', $to_age_partner)
            ->where('from_age_partner', '<=', $from_age)
            ->where('to_age_partner', '>=', $to_age)
            ->where('gender', $gender_partner)
            ->where('gender_partner', $gender)
            ->with(['participants'])
            ->get();

        return $match_setting_parties;
    }

    /**
     * updateSettingStatusAllJoined
     *
     * @param $setting_party_id
     * @throws ErrorException
     */
    private function updateSettingStatusAllJoined($setting_party_id)
    {
        /** @var EloquentSettingParty $setting_party */
        $setting_party = $this->settingPartyQuery()->findOrFail($setting_party_id);
        $setting_party->updateOrThrow([
            'status' => SettingPartyStatus::ALL_PARTICIPANT_JOINED,
            'update_status_at' => now()
        ]);
        logger()->info('success update status setting_party', [$setting_party_id]);
    }

    /**
     * getMinMaxAgeFromParticipants
     *
     * @param \Illuminate\Database\Eloquent\Collection $participants
     * @return int[]
     */
    private function getMinMaxAgeFromParticipants(\Illuminate\Database\Eloquent\Collection $participants)
    {
        $min_age = 100;
        $max_age = 0;
//        dd_with_format($participants->toArray());
        /** @var User $participant */
        foreach ($participants as $participant) {
            logger()->debug('AGE', [$participant->userProfile->age]);
            if ($participant->userProfile->age < $min_age) {
                $min_age = $participant->userProfile->age;
            }
            if ($participant->userProfile->age > $max_age) {
                $max_age = $participant->userProfile->age;
            }
        }

        return [$min_age, $max_age];
    }

    //========================= Get participant ============================

    /**
     * getAllSettingParticipantIdsExceptAuthor
     *
     * @param EloquentSettingParty $setting_party
     * @param $author_user_id
     * @return array
     */
    private function getAllSettingParticipantIdsExceptAuthor(EloquentSettingParty $setting_party, $author_user_id)
    {
        /** @var Collection $participants */
        $participants = $this->getAllSettingParticipantIds($setting_party);
        return collect($participants)->reject(function ($participant_id) use ($author_user_id) {
            return $author_user_id === $participant_id;
        })->toArray();
    }

    /**
     * getAllSettingParticipantIds
     *
     * @param EloquentSettingParty $setting_party
     * @return array
     */
    private function getAllSettingParticipantIds(EloquentSettingParty $setting_party)
    {
        return $setting_party->participants()->pluck('id')->toArray();
    }
}
