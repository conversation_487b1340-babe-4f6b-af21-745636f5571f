<?php

namespace Src\Domain\Api\Services\Traits;

use App\Eloquent\User;
use Illuminate\Database\Eloquent\Collection;

trait FriendTrait
{

    /**
     * getAllFriendIdsOfUser
     *
     * @param int $user_id
     * @return \Illuminate\Support\Collection
     */
    private function getAllFriendIdsOfUser(int $user_id)
    {
        $user_friends = $this->getListFriendsOfUser($user_id);
        return $user_friends->pluck('id');
    }

    /**
     * getListFriendsOfUser
     *
     * @param int $user_id
     * @param string|null $filter_column
     * @param null $filter_value
     * @return Collection
     */
    private function getListFriendsOfUser(int $user_id, string $filter_column = null, $filter_value = null)
    {
        /** @var User $user */
        $user = User::queryModel()->with('userBlocks')->findOrFail($user_id);
        $user_blocks_ids = $user->userBlocks->pluck('id')->toArray();

        $user_friends = $user->userFriends()
            ->whereNotIn('id', $user_blocks_ids)
            ->with('userProfile.storageFile')
            ->when($filter_column, function ($query) use ($filter_column, $filter_value) {
                $query->whereHas('userProfile', function ($query) use ($filter_column, $filter_value) {
                    $query->where($filter_column, $filter_value);
                });
            })
            ->get();

        return $user_friends;
    }

    /**
     * isBlockEachOther
     *
     * @param int $user_id
     * @param int $check_user_id
     * @return bool
     */
    private function isBlockEachOther(int $user_id, int $check_user_id)
    {
        $block_user_ids = User::queryModel()->findOrFail($user_id)->userBlocks->pluck('id')->toArray();
        $block_check_user_ids = User::queryModel()->findOrFail($check_user_id)->userBlocks->pluck('id')->toArray();
        return in_array($check_user_id, $block_user_ids) || in_array($user_id, $block_check_user_ids);
    }

}
