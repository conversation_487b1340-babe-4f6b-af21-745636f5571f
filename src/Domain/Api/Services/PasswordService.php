<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\PasswordReset as EloquentPasswordReset;
use App\Eloquent\User;
use App\Mail\PasswordReset\SendPasswordResetMail;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Src\Domain\Api\Models\User\ChangePasswordForm;
use Src\Domain\Api\Models\User\ResetPassword;
use Src\Domain\Api\Models\User\ResetPasswordForm;
use Src\Domain\Constants\Auth;
use Src\Enum\LoginIdType;
use Src\Exception\APIRuntimeException;
use Src\Utils\TwilioSMS;
use Throwable;

class PasswordService
{
    /**
     * sendToken
     *
     * @param ResetPasswordForm $form
     * @return bool|mixed
     */
    public function sendToken(ResetPasswordForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var User $user */
                $user = $this->userQuery()
                    ->where('login_id', $form->getLoginId())
                    ->first();
                if (!$user) {
                    throw new APIRuntimeException('api_errors.password.user_not_exist');
                }

                $password_reset = $this->ResetPasswordQuery()->createOrThrow([
                    'login_id' => $user->login_id,
                    'token' => Str::random(64)
                ]);
                $login_id_type = $user->login_id_type;

                if ($password_reset) {
                    $url = env('APP_URL') . '/user/reset-password/' . $password_reset->token;
                    if ($login_id_type == LoginIdType::EMAIL) {
                        Mail::to($form->getLoginId())->send(new SendPasswordResetMail([
                            'url' => $url
                        ]));
                    } else {
                        $phone_format = env('COUNTRY_CODE') . ltrim($user->login_id, '0');
                        TwilioSMS::sendSMSReset($phone_format, $url);
                    }
                    logger()->info('success create password reset.', ['password_resets.id' => $password_reset->id]);
                    if (env('APP_DEBUG')) {
                        return (new ResetPassword($password_reset))->toDetailApiResponse();
                    } else {
                        return [];
                    }
                }
            });
        } catch (Throwable $e) {
            $result = null;
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed send token reset password.', $form->toArray());
        }
        return $result;
    }

    /**
     * ResetPasswordQuery
     *
     * @return Builder|EloquentPasswordReset
     */
    private function ResetPasswordQuery()
    {
        return EloquentPasswordReset::query()->getModel();
    }

    /**
     * changePassword
     *
     * @param ChangePasswordForm $form
     * @param $author
     * @return bool|mixed
     */
    public function changePassword(ChangePasswordForm $form, $author)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $author) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($author->getId());
                if (!(Hash::check($form->getCurrentPassword(), $user->password))) {
                    throw new APIRuntimeException('api_errors.password.current_password');
                } elseif ((Hash::check($form->getNewPassword(), $user->password))) {
                    throw new APIRuntimeException('api_errors.password.duplicate_password');
                }
                $user->updateOrThrow($form->createAttributes());
                logger()->info('success change password.', ['users.id' => $author->getId()]);

                // Update Api token
                $author->getToken()->revoke();
                $token_result = $user->createToken('Personal Access Token');
                $token = $token_result->token;
                $token->expires_at = Carbon::now()->addYears(Auth::TOKEN_EXPIRES_YEAR);
                $token->save();
                if (!$token) {
                    logger()->debug('failed to save token', $form->toArray());
                    return false;
                }
                return [
                    'api_token' => $token_result->accessToken,
                    'token_type' => 'Bearer',
                    'expires_at' => Carbon::parse($token_result->token->expires_at)->toDateTimeString()
                ];
            });
        } catch (Throwable $e) {
            $result = null;
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed change password.', $form->toArray());
        }
        return $result;
    }

    /**
     * userQuery
     *
     * @return Builder|Model
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }
}
