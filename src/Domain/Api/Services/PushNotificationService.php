<?php

namespace Src\Domain\Api\Services;

use Kreait\Firebase\Messaging\RawMessageFromArray;
use Src\Enum\EventType;
use Throwable;

/**
 * Class PushNotificationService
 * @package Src\Domain\Api\Services
 */
class PushNotificationService
{

    /**
     * sendPushForPartyMatched
     *
     * @param $notification
     * @param array $data
     * @return bool
     */
    public static function sendPushForPartyMatched($notification, $data = [])
    {
        logger()->debug('sendPushForPartyMatched');

        $fcm_tokens = $notification['fcm_tokens'];
        $agora_tokens = isset($data['agora_tokens']) ? $data['agora_tokens'] : [];

        logger()->debug('$fcm_tokens', $fcm_tokens);
        logger()->debug('$agora_tokens', $agora_tokens);
        if (empty($fcm_tokens) || empty($agora_tokens)) {
            logger()->debug('$fcm_tokens or agora_tokens is empty, return', $fcm_tokens);
            return false;
        }

        unset($data['agora_tokens']);

        try {
            $title = $notification['title'];
            $body = $notification['body'];

            foreach ($fcm_tokens as $user_fcm_token) {
                foreach ($agora_tokens as $agora_token) {
                    if ($user_fcm_token['id'] == $agora_token['uid']) {
                        $push_data = array_merge($data, ['agora_token' => $agora_token]);
                        $push_data = [
                            'push_data' => json_encode($push_data)
                        ];
                        logger()->debug('$user_fcm_token', [$user_fcm_token]);
                        logger()->debug('PUSH_DATA_EACH_DEVICE', [$push_data]);
                        // Get token from user
                        $message = new RawMessageFromArray([
                            'data' => $push_data,
                            'apns' => [
                                'payload' => [
                                    'aps' => [
                                        'alert' => [
                                            'title' => $title,
                                            'body' => $body,
                                        ],
                                        'sound' => 'default'
                                    ],
                                ],
                            ]
                        ]);

                        $messaging = app('firebase.messaging');
//                        $report = $messaging->send($message, $user_fcm_token['fcm_token']);

                        $report = $messaging->sendMulticast($message, [$user_fcm_token['fcm_token']]);
                    }
                }
            }

            logger()->info('success send push notification', $fcm_tokens);
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed send push notification');
            return false;
        }

        return true;
    }

    /**
     * sendPushNotificationMultiDevice
     *
     * @param $notification
     * @param array $data
     * @return bool
     */
    public static function sendPushNotificationMultiDevice($notification, $data = [])
    {
        logger()->debug('sendPushNotificationMultiDevice');
        logger()->debug('EVENT_TYPE', [$data['event_type']]);
           if( $data && $data['event_type'] === EventType::PARTY_STARTED){
               return self::sendPushForPartyMatched($notification, $data);
           }
        $fcm_tokens = $notification['fcm_tokens'];

        if (empty($fcm_tokens)) {
            logger()->debug('$fcm_tokens empty, return', $fcm_tokens);
            return false;
        }

        try {
            $title = $notification['title'];
            $body = $notification['body'];

            $push_data = [
                'push_data' => json_encode($data)
            ];
            logger()->debug('PUSH_DATA', [$push_data]);
            // Get token from user
            $message = new RawMessageFromArray([
                'data' => $push_data,
                'apns' => [
                    'payload' => [
                        'aps' => [
                            'alert' => [
                                'title' => $title,
                                'body' => $body,
                            ],
                            'sound' => 'default'
                        ],
                    ],
                ]
            ]);

            $messaging = app('firebase.messaging');
            $report = $messaging->sendMulticast($message, $fcm_tokens);
            logger()->info('success send push notification', $fcm_tokens);
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed send push notification');
            return false;
        }

        return true;
    }

}
