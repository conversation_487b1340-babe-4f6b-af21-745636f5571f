<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\User\User as UserDetail;

/**
 * Class TopService
 * @package Src\Domain\Api\Services
 */
class TopService
{

    /**
     * findOrFail
     *
     * @param AuthorInterface $author
     * @return array
     * @throws Exception
     */
    public function findOrFail(AuthorInterface $author): array
    {
        $user_id = $author->getId();
        /** @var User $user */
        $user = $this->userQuery()
            ->with('userProfile')
            ->with('userPoint')
            ->with('userTicket')
            ->with('userNotificationUnread')
            ->findOrFail($user_id);

        return (new UserDetail($user))->getTopInfo();
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

}
