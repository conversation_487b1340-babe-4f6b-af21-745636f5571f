<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\MessageConversation as EloquentMessageConversation;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\MessageConversation\MessageConversation;
use Src\Domain\Api\Models\MessageConversation\MessageConversationForm;
use Throwable;

/**
 * Class MessageConversationService
 * @package Src\Domain\Api\Services
 */
class MessageConversationService
{
    /**
     * fetchAll
     *
     * @return Collection|MessageConversation[]
     */
    public function fetchAll(AuthorInterface $author): Collection
    {

        $message_conversations = $this->messageConversationQuery()
            ->orderByDesc('id')
            ->with(['messageParticipants' => function ($query) use ($author) {
                $query->with('userProfile.storageFile');
            }])
            ->whereHas('messageParticipants', function ($query) use ($author) {
                $query->where('id', $author->getId());
            })
            ->whereDoesntHave('deletedMessageConversations', function ($query) use ($author) {
                $query->where('id', $author->getId());
            })
            ->get();

//        return $message_conversations;
        return $message_conversations->transform(static function ($message_conversation) use ($author) {
            return (new MessageConversation($message_conversation))->toListApiResponse($author->getId());
        });
    }

    /**
     * messageConversationQuery
     *
     * @return Builder|EloquentMessageConversation
     */
    private function messageConversationQuery()
    {
        return EloquentMessageConversation::query()->getModel();
    }

    /**
     * find Or Fail
     *
     * @param int $message_conversation_id
     * @return MessageConversation
     */
    public function findOrFail(int $message_conversation_id): MessageConversation
    {
        /** @var EloquentMessageConversation $message_conversation */
        $message_conversation = $this->messageConversationQuery()->findOrFail($message_conversation_id);
        return new MessageConversation($message_conversation);
    }

    /**
     * Store
     *
     * @param MessageConversationForm $form
     * @return bool|mixed
     */
    public function store(MessageConversationForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var EloquentMessageConversation $message_conversation */
                $message_conversation = $this->messageConversationQuery()->createOrThrow($form->createAttributes());
                logger()->info('success create message_conversation.', ['message_conversations.id' => $message_conversation->id]);
                return new MessageConversation($message_conversation);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create message_conversation.', $form->toArray());
        }
        return $result;
    }

    /**
     * readAllMessage
     *
     * @param AuthorInterface $author
     * @param int $partner_id
     * @return bool|mixed
     */
    public function readAllMessage(AuthorInterface $author, int $partner_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($author, $partner_id) {
                $message_conversation_id = MessageService::getMessageConversationId($author->getId(), $partner_id);
                if (!$message_conversation_id) {
                    return null;
                }

                /** @var EloquentMessageConversation $message_conversation */
                $message_conversation = $this->messageConversationQuery()->findOrFail($message_conversation_id);
                $message_conversation->messageParticipants()->updateExistingPivot($author->getId(), [
                    'number_unread_message' => 0,
                ]);

                logger()->info('success update message_conversation.', compact($message_conversation_id));
                return (new MessageConversation($message_conversation))->toDetailApiResponse();
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update message_conversation', compact('message_conversation_id'));
        }
        return $result;
    }

    /**
     * delete
     *
     * @param AuthorInterface $author
     * @param int $partner_id
     * @return bool
     */
    public function delete(AuthorInterface $author, int $partner_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($author, $partner_id) {
                $message_conversation_id = MessageService::getMessageConversationId($author->getId(), $partner_id);

                /** @var EloquentMessageConversation $message_conversation */
                $message_conversation = $this->messageConversationQuery()->findOrFail($message_conversation_id);
                $isDeleted = $message_conversation->deletedMessageConversations()->where('id', $author->getId())->exists();
                if (!$isDeleted) {
                    $message_conversation->deletedMessageConversations()->attach([$author->getId()]);
                }
                logger()->info('success delete message_conversation.', compact('message_conversation_id'));
                return true;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete message_conversation', compact('message_conversation_id'));
        }
        return $result;
    }

}
