<?php

namespace Src\Domain\Api\Services\SettingParty;

use App\Eloquent\SettingParty as EloquentSettingParty;
use Carbon\Carbon;
use DB;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\Traits\SettingPartyTrait;
use Src\Domain\Api\Services\UserNotificationService;
use Src\Enum\Event\SettingPartyDeleteEvent;
use Src\Enum\EventType;
use Src\Enum\SettingPartyStatus;
use Src\Utils\Util;
use Throwable;

/**
 * Class DeleteService
 * @package Src\Domain\Api\Services
 */
class DeleteService
{
    use SettingPartyTrait;
    use PartyTrait;

    /**
     * delete
     *
     * @param int $setting_party_id
     * @return bool
     */
    public function delete(int $setting_party_id, AuthorInterface $author)
    {
        /** @var EloquentSettingParty $setting_party */
        $setting_party = $this->settingPartyQuery()->findOrFail($setting_party_id);
        return $this->deleteOneSettingParty(SettingPartyDeleteEvent::DELETE, $setting_party, $author);
    }

    /**
     * deleteOneSettingParty
     *
     * @param int $action
     * @param EloquentSettingParty $setting_party
     * @param AuthorInterface|null $author
     * @return array|bool
     */
    public function deleteOneSettingParty(int $action, EloquentSettingParty $setting_party, AuthorInterface $author = null)
    {
        logger()->debug('deleteOneSettingParty Start', [$setting_party->id]);

        $setting_party_id = $setting_party->id;
        $result = false;
        DB::beginTransaction();
        try {
            $fcm_tokens = $setting_party->participants->pluck('fcm_token')->toArray();
            $receive_user_ids = $setting_party->participants->pluck('id')->toArray();

            // Update action for notification
            UserNotificationService::disableAction(EloquentSettingParty::getTableName(), $setting_party_id);

            // Delete setting party
            $setting_party->participants()->sync([]);
            $setting_party->delete();
            DB::commit();

            // Creat message
            $message_path = '';
            $placeholder_data = [];
            $event_type = EventType::SETTING_PARTY_DELETE;

            switch ($action){
                case SettingPartyDeleteEvent::DELETE:
                    $event_type = EventType::SETTING_PARTY_DELETE;
                    $message_path = 'setting_party.delete';

                    break;

                case SettingPartyDeleteEvent::DELETE_TIMEOUT:
                    $event_type = EventType::SETTING_PARTY_DELETE_TIMEOUT;
                    $message_path = 'setting_party.delete_auto';
                    break;

                case SettingPartyDeleteEvent::DECLINE:
                    $event_type = EventType::SETTING_PARTY_DECLINE;
                    $message_path = 'setting_party.decline';

                    break;

                case SettingPartyDeleteEvent::LEAVE:
                    $event_type = EventType::SETTING_PARTY_LEAVE;
                    $message_path = 'setting_party.leave';
                    break;
            }

            if ($author) {
                $placeholder_data = ['nickname' => $author->getUserProfile()->nickname];
            }

            // Create push, socket data
            $addition_data = [
                'setting_party_id' => $setting_party->id,
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData($event_type, $receive_user_ids, $fcm_tokens,
                $addition_data, $message_path, $placeholder_data);

            // Send socket
            $url_socket = 'socket_api.setting_party.deleted';
            request_socket_api_and_push_notification($url_socket, $notify_data, $notify_payload);

            logger()->info('success delete setting_party.', compact('setting_party_id'));
            return $setting_party->toArray();
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed delete setting_party', compact('setting_party_id'));
        }
        return $result;
    }

    /**
     * deleteTimeout
     *
     * @return array|bool
     */
    public function deleteTimeout()
    {
        logger()->debug('deleteTimeout Start.');
        $result = false;
        try {
            $setting_parties = $this->settingPartyQuery()
                ->where('status','!=', SettingPartyStatus::PENDING)
                ->where('update_status_at', '<=', Carbon::now()->subMinutes(TIMEOUT_SETTING_GROUP))
                ->with('participants')
                ->get();
            logger()->debug('List setting party will delete', $setting_parties->pluck('id')->toArray());
            if ($setting_parties->isEmpty()) {
                logger()->debug('Have not setting party timeout');
                return true;
            }

            $deleted_party_ids = [];

            foreach ($setting_parties as $setting_party) {
                $result = $this->deleteOneSettingParty(SettingPartyDeleteEvent::DELETE_TIMEOUT, $setting_party, null);
                if ($result) {
                    $deleted_party_ids[] = $setting_party->id;
                }
            }

            logger()->debug('success deleteTimeout.', $deleted_party_ids);
            return true;
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed deleteTimeout');
        }
        return $result;
    }

    /**
     * delete
     *
     * @param int $setting_party_id
     * @param AuthorInterface $author
     * @return bool
     * @throws Throwable
     */
    public function decline(int $setting_party_id, AuthorInterface $author)
    {
        /** @var EloquentSettingParty $setting_party */
        $setting_party = $this->settingPartyQuery()->findOrFail($setting_party_id);

        return $this->deleteOneSettingParty(SettingPartyDeleteEvent::DECLINE, $setting_party, $author);
    }

}
