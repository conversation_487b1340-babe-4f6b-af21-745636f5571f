<?php

namespace Src\Domain\Api\Services\SettingParty;

use App\Eloquent\SettingParty as EloquentSettingParty;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\SettingParty\SettingParty;
use Src\Domain\Api\Models\SettingParty\SettingPartyWaitingForm;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\Traits\SettingPartyTrait;
use Src\Enum\GroupType;
use Throwable;

/**
 * Class ReadService
 * @package Src\Domain\Api\Services
 */
class ReadService
{
    use SettingPartyTrait;
    use PartyTrait;

    /**
     * listSettingGroupOfUser
     *
     * @param $user_id
     * @return array|null
     */
    public function listSettingGroupOfUser($user_id): ?array
    {
        $result = $this->checkUserInSettingPartyOrParty($user_id);
        if ($result !== false) {
            return [
                'result_status' => $result->result_code,
                'data' => $result->data,
            ];
        }

        return null;
    }

    /**
     * listGroupWaiting
     *
     * @param AuthorInterface $author
     * @param SettingPartyWaitingForm $form
     * @return array
     */
    public function listGroupWaiting(AuthorInterface $author, SettingPartyWaitingForm $form): array
    {
        $match_params = [
            'setting_party_id' => null,
            'group_type' => null,
            'from_age' => $author->getUserProfile()->age,
            'to_age' => $author->getUserProfile()->age,
            'from_age_partner' => $form->getFromAgePartner(),
            'to_age_partner' => $form->getToAgePartner(),
            'gender' => $author->getGenderParty(),
            'gender_partner' => $author->getGenderPartner(),
        ];
        $match_setting_parties = $this->getMatchParties($match_params);

        $group_types = GroupType::getValues();
        $count_by_group_type = [];
        foreach ($group_types as $group_type) {
            $number_waiting_setting_group = 0;
            foreach ($match_setting_parties as $setting_party) {
                if ($setting_party->group_type === $group_type) {
                    $number_waiting_setting_group++;
                }
            }

            $count_by_group_type["group_type_$group_type"] = $number_waiting_setting_group;
        }

        return $count_by_group_type;
    }

    /**
     * fetchAllParticipants
     *
     * @param $setting_party_id
     * @return array|null
     */
    public function fetchAllParticipants($setting_party_id): ?array
    {
        // Find Setting Party
        try {
            $query = $this->settingPartyQuery();
            $setting_party = $query
                ->with('participants.userProfile.storageFile')
                ->findOrFail($setting_party_id);

            return (new SettingParty($setting_party))->toAllParticipants();

        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed fetchAllParticipants', ['setting_party_id' => $setting_party_id]);
        }

        return null;
    }

    /**
     * fetchAllParticipantWithOther
     *
     * @param $setting_party_id
     * @return array|null
     */
    public function fetchAllParticipantWithOther($setting_party_id): ?array
    {
        // Find Setting Party
        try {
            $query = $this->settingPartyQuery();
            $setting_party = $query
                ->with('settingPartyGroups.participants.userProfile.storageFile')
                ->findOrFail($setting_party_id);

            return (new SettingParty($setting_party))->toAllParticipants();

        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed fetchAllParticipants', ['setting_party_id' => $setting_party_id]);
        }

        return null;
    }

    /**
     * find Or Fail
     *
     * @param int $setting_party_id
     * @return SettingParty
     */
    public function findOrFail(int $setting_party_id): SettingParty
    {
        /** @var EloquentSettingParty $setting_party */
        $setting_party = $this->settingPartyQuery()->findOrFail($setting_party_id);
        return new SettingParty($setting_party);
    }

}
