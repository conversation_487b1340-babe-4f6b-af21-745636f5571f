<?php

namespace Src\Domain\Api\Services\SettingParty;

use App\Eloquent\SettingParty as EloquentSettingParty;
use App\Eloquent\SettingPartyGroup;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\SettingParty\SettingParty;
use Src\Domain\Api\Models\SettingParty\SettingPartyForm;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\Traits\SettingPartyTrait;
use Src\Domain\Api\Services\UserNotificationService;
use Src\Enum\Age;
use Src\Enum\EventType;
use Src\Enum\Gender;
use Src\Enum\GroupType;
use Src\Enum\InviteMemberType;
use Src\Enum\NotificationDiv;
use Src\Enum\PartyType;
use Src\Enum\ResultStatus;
use Src\Enum\SettingPartyParticipantStatus;
use Src\Enum\SettingPartyStatus;
use Src\Exception\ErrorException;
use Src\Utils\CustomResponse;
use Src\Utils\Util;
use Throwable;

/**
 * Class CreateService
 * @package Src\Domain\Api\Services
 */
class CreateService
{
    use SettingPartyTrait;
    use PartyTrait;

    /**
     * Store
     *
     * @param SettingPartyForm $form
     * @param AuthorInterface $author
     * @param $party_type
     * @return bool|mixed
     * @throws Throwable
     */
    public function store($form, AuthorInterface $author, $party_type)
    {
        $result = false;

        DB::beginTransaction();
        try {
            $creator_id = $author->getId();

            // Check user in setting party or  party
            $result = $this->checkUserInSettingPartyOrParty($creator_id);
            if ($result instanceof CustomResponse) {
                return $result->getResponseData();
            }

            $participants = User::queryModel()->with('userProfile')->findMany(array_merge([], $form->getInviteUserIds(), [$creator_id]));

            // Create Setting party
            $setting_party = $this->createSettingParty($form, $author, $participants, $party_type);

            // Get notification data
            [$notify_data, $notify_payload] = $this->getSocketAndPushData($form, $author, $setting_party, $form->getInviteUserIds(), $party_type);

            // Create Notification
            $notification_id = UserNotificationService::createUserNotification($form->getInviteUserIds(), NotificationDiv::INVITE_JOIN_SETTING_PARTY,
                EloquentSettingParty::getTableName(), $setting_party->id, $notify_payload, $notify_data, null, User::getTableName(), $author->getId(), true);
            $notify_data['notification_id'] = $notification_id;

            DB::commit();

            // Send invite by socket

            if ($party_type === PartyType::FRIEND_PARTY || $form->getGroupType() !== GroupType::ONE_PARTNER) {
                request_socket_api_and_push_notification('socket_api.setting_party.invite', $notify_data, $notify_payload);
            }

            logger()->info('success create setting_party.', ['setting_parties.id' => $setting_party->id]);
            $result_data = (new SettingParty($setting_party))->toDetailApiResponse();

            return [
                'result_status' => ResultStatus::SUCCESS,
                'data' => $result_data,
            ];
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed create setting_party.', $form->toArray());
        }

        return $result;
    }

    /**
     * createSettingParty
     *
     * @param SettingPartyForm $form
     * @param AuthorInterface $creator
     * @param Collection $participants
     * @param int $party_type
     * @return EloquentSettingParty
     * @throws ErrorException
     */
    private function createSettingParty($form, AuthorInterface $creator, Collection $participants, int $party_type)
    {
        // Get min, max age
        [$min_age, $max_age] = $this->getMinMaxAgeFromParticipants($participants);

        /** @var EloquentSettingParty $setting_party */
        $setting_party = $this->settingPartyQuery()->createOrThrow(
            $form->createAttributes($creator, InviteMemberType::ONLY_FRIEND, $min_age, $max_age, $party_type));

        // Invite users
        $participant_data[$creator->getId()] = [
            'status' => SettingPartyParticipantStatus::JOINED,
            'is_creator' => true
        ];

        if ($party_type !== PartyType::FRIEND_PARTY && $form->getGroupType() === GroupType::ONE_PARTNER) {
            $this->updateSettingStatusAllJoined($setting_party->id);
        } else {
            foreach ($form->getInviteUserIds() as $invite_user_id) {
                $participant_data[$invite_user_id] = [
                    'status' => SettingPartyParticipantStatus::INVITED,
                    'is_creator' => false
                ];
            }
        }

        $setting_party->participants()->sync($participant_data);

        return $setting_party;
    }

    /**
     * getSocketAndPushData
     *
     * @param SettingPartyForm $form
     * @param AuthorInterface $creator
     * @param EloquentSettingParty $setting_party
     * @param array $invite_user_ids
     * @param int $party_type
     * @return array
     * @throws Throwable
     */
    private function getSocketAndPushData(SettingPartyForm $form, AuthorInterface $creator, EloquentSettingParty $setting_party, array $invite_user_ids, int $party_type)
    {
        $fcm_tokens = User::query()->findMany($invite_user_ids)->pluck('fcm_token')->toArray();
        $format_data = [
            'nickname' => $creator->getUserProfile()->nickname,
            'group_type' => $setting_party->group_type,
            'gender_name' => Gender::getDescription($creator->getUserProfile()->gender_party),
        ];

        [$from_age_partner, $to_age_partner] = $form->getFromToAgePartner($party_type);

        if ($party_type == PartyType::FRIEND_PARTY) {
            $message_path = 'setting_party.invite_with_friend';
            $format_data['number_person'] = $form->getNumberPerson();
        } else {
            if (Age::isOptionAge($from_age_partner, $to_age_partner)) {
                $message_path = 'setting_party.invite_age_option';
            } else {
                $message_path = 'setting_party.invite';
                $format_data['from_age'] = $from_age_partner;
                $format_data['to_age'] = $to_age_partner;
            }
        }
        // Create push, socket data
        $addition_data = [
            'creator_user_id' => $creator->getId(),
            'creator_nickname' => $creator->getUserProfile()->nickname,
            'setting_party_id' => $setting_party->id,
            'party_type' => $setting_party->party_type,
            'invite_member_type' => $setting_party->invite_member_type,
        ];
        [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::SETTING_PARTY_INVITE, $invite_user_ids, $fcm_tokens,
            $addition_data, $message_path, $format_data);

        return [$notify_data, $notify_payload];
    }

    /**
     * createWithOther
     *
     * @param AuthorInterface $creator
     * @param SettingPartyForm $form
     * @return bool|mixed
     * @throws Throwable
     */
    public function createWithOther(AuthorInterface $creator, SettingPartyForm $form)
    {
        $result = false;

        DB::beginTransaction();
        try {
            $creator_id = $creator->getId();

            // Check user in setting party or  party
            $result = $this->checkUserInSettingPartyOrParty($creator_id);
            if ($result instanceof CustomResponse) {
                return $result->getResponseData();
            }

            // Get list of setting parties with other user
            $setting_party_others = $this->findMatchSettingWithOther($creator, $form);

            // Get list participant
            $participants = User::query()->with('userProfile')->findMany(array_merge([], $form->getInviteUserIds(), [$creator_id]));

            // Combine setting party or or create new
            [$setting_party, $is_new_setting_party] = $this->createOrCombineSettingParty($creator, $form, $setting_party_others, $participants);

            // Get notification data
            [$notify_data, $notify_payload] = $this->getSocketAndPushData($form, $creator, $setting_party, $form->getInviteUserIds(), PartyType::NEW_PARTY);

            // Create Notification
            $notification_id = UserNotificationService::createUserNotification($form->getInviteUserIds(), NotificationDiv::INVITE_JOIN_SETTING_PARTY,
                EloquentSettingParty::getTableName(), $setting_party->id, $notify_payload, $notify_data);
            $notify_data['notification_id'] = $notification_id;

            DB::commit();

            // Send invite by socket
            if ($form->getGroupType() !== GroupType::ONE_PARTNER) {
                if (!empty($form->getInviteUserIds())) {
                    request_socket_api_and_push_notification('socket_api.setting_party.invite', $notify_data, $notify_payload);
                }

                if (!$is_new_setting_party) {
                    $receive_user_ids = $setting_party->participants()->pluck('id')->toArray();
                    $addition_data = [
                        'creator_user_id' => $creator->getId(),
                        'setting_party_id' => $setting_party->id,
                    ];
                    $notify_data = Util::formatSocketData($receive_user_ids, $addition_data);
                    request_socket_api('socket_api.setting_party.other_group_joined', $notify_data);
                }
            }

            logger()->info('success create setting_party.', ['setting_parties.id' => $setting_party->id]);
            $result_data = (new SettingParty($setting_party))->toDetailApiResponse();

            return [
                'result_status' => ResultStatus::SUCCESS,
                'data' => $result_data,
            ];
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed create setting_party.', $form->toArray());
        }

        return $result;
    }

    /**
     * findMatchSettingWithOther
     *
     * @param AuthorInterface $creator
     * @param SettingPartyForm $form
     * @return Builder[]|Collection|Model[]
     */
    private function findMatchSettingWithOther(AuthorInterface $creator, SettingPartyForm $form)
    {
        $setting_party_others = EloquentSettingParty::query()->getModel()
            ->orderBy('id')
            ->where('group_type', $form->getGroupType())
            ->where('invite_member_type', InviteMemberType::HAVE_OTHER_USER)
            ->where('status', SettingPartyStatus::WAITING_USER_JOIN)
            ->where('from_age_partner', $form->getFromAgePartner())
            ->where('to_age_partner', $form->getToAgePartner())
            ->where('gender', $creator->getGenderParty())
            ->where('gender_partner', $creator->getGenderPartner())
            ->with(['participants'])
            ->get();

        return $setting_party_others;
    }

    /**
     * createOrCombineSettingParty
     *
     * @param AuthorInterface $creator
     * @param SettingPartyForm $form
     * @param Collection $setting_party_others
     * @param Collection $participants
     * @return array
     * @throws ErrorException
     */
    private function createOrCombineSettingParty(AuthorInterface $creator, SettingPartyForm $form
        , Collection $setting_party_others, Collection $participants)
    {
        $creator_id = $creator->getId();
        $invite_user_ids = $form->getInviteUserIds();

        // Find combine setting party
        /** @var EloquentSettingParty $combine_setting_party */
        $combine_setting_party = null;
        $is_create_setting_party = false;
        $number_user_new_group = count($invite_user_ids) + 1;

        foreach ($setting_party_others as $setting_party_other) {
            $number_user_other_group = count($setting_party_other->participants);
            $number_remain_user = $setting_party_other->group_type - ($number_user_new_group + $number_user_other_group);
            logger()->debug('NUMBER_REMAIL', [$number_remain_user]);
            if ($number_remain_user >= 0) {
                $combine_setting_party = $setting_party_other;
                break;
            }
        }

        logger()->debug('$combine_setting_party', [$combine_setting_party]);
        $is_new_setting_party = false;
        // Check create setting party
        if ($combine_setting_party === null) {
            $is_create_setting_party = true;
            [$min_age, $max_age] = $this->getMinMaxAgeFromParticipants($participants);

            /** @var EloquentSettingParty $setting_party */
            $combine_setting_party = $this->settingPartyQuery()->createOrThrow(
                $form->createWithOtherAttributes($creator, InviteMemberType::HAVE_OTHER_USER, $min_age, $max_age));
            $is_new_setting_party = true;
        }

        // Attach users into setting party
        $setting_party = $this->attachSettingPartyGroup($creator_id, $combine_setting_party, $invite_user_ids, $is_create_setting_party);

        return [$setting_party, $is_new_setting_party];
    }

    /**
     * attachSettingPartyGroup
     *
     * @param int $creator_id
     * @param EloquentSettingParty $setting_party
     * @param array $invite_user_ids
     * @param bool $is_create_setting_party
     * @return EloquentSettingParty
     * @throws ErrorException
     */
    private function attachSettingPartyGroup(int $creator_id, EloquentSettingParty $setting_party, array $invite_user_ids, bool $is_create_setting_party)
    {
        if (!$is_create_setting_party) {
            if (empty($invite_user_ids) && ($setting_party->participants->count() + 1 === $setting_party->group_type)) {
                $setting_party->updateOrThrow([
                    'status' => SettingPartyStatus::ALL_PARTICIPANT_JOINED
                ]);
            }
        }

        // Create setting_party_groups
        $setting_party_group = SettingPartyGroup::query()->getModel()->createOrThrow([
            'creator_id' => $creator_id,
            'is_owner_setting_party' => $is_create_setting_party ? true : false,
            'setting_party_id' => $setting_party->id,
            'group_type' => $setting_party->group_type
        ]);

        // Invite users
        $participant_data[$creator_id] = [
            'status' => SettingPartyParticipantStatus::JOINED,
            'is_creator' => true,
            'setting_party_group_id' => $setting_party_group->id
        ];

        foreach ($invite_user_ids as $invite_user_id) {
            $participant_data[$invite_user_id] = [
                'status' => SettingPartyParticipantStatus::INVITED,
                'is_creator' => false,
                'setting_party_group_id' => $setting_party_group->id
            ];
        }

        $setting_party->participants()->attach($participant_data);

        return $setting_party;
    }

}
