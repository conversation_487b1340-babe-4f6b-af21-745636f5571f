<?php

namespace Src\Domain\Api\Services\Queue;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\PartyHistory;
use App\Eloquent\PartyHistoryRecord;
use App\Eloquent\SettingParty as EloquentSettingParty;
use App\Eloquent\StorageFile;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Enum\FileDiv;
use Src\Enum\PartyRecordStatus;
use Src\Exception\ErrorException;
use Src\Utils\Agora\GenToken\AgoraGenToken;
use Src\Utils\Agora\Record\AgoraRecordManager;
use Src\Utils\Agora\Record\AwsConvertManager;
use Src\Utils\GenId;

/**
 * Class PartyRecordQueueService
 * @package Src\Domain\Api\Services
 */
class PartyRecordQueueService
{

    use PartyTrait;

    /**
     * start
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws \Throwable
     */
    public function startRecord(int $party_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var PartyHistory $party_history */
            $party_history = PartyHistory::queryModel()->where('party_id', $party_id)->first();

            if(!$party_history){
                throw new ErrorException('Not found party history by party_id, cancel starting record');
            }

            $channel = $party_history->channel_id;
            $rectUID = GenId::genRecordUid();
            logger()->debug("channel: $channel, rectUID: $rectUID");
            // acquire
            $result = AgoraRecordManager::acquire($channel, $rectUID);
            logger()->debug("RESULT_ACQUIRE:", $result);
            $resource_id = $result['resourceId'];

            // start
            $expire_at = now()->addMinutes(PARTY_MINUTE_RECORD);
            $channelToken = AgoraGenToken::genChannelToken($channel, $rectUID, $expire_at);
            $record_token = $channelToken['token'];
            $result_start = AgoraRecordManager::start($channel, $rectUID, $record_token, $resource_id);
            logger()->debug("RESULT_START:", $result_start);

            // Update status
            if (isset($party_history->partyHistoryRecord) && ($channel === $party_history->partyHistoryRecord->channel_id)) {
                $party_history->partyHistoryRecord()->update([
                    'record_uid' => $rectUID,
                    'record_token' => $record_token,
                    'record_resource_id' => $resource_id,
                    'record_sid' => $result_start['sid'],
                    'record_status' => PartyRecordStatus::STARTED,
                ]);
            } else {
                $party_history->partyHistoryRecord()->create([
                    'channel_id' => $channel,
                    'record_uid' => $rectUID,
                    'record_token' => $record_token,
                    'record_resource_id' => $resource_id,
                    'record_sid' => $result_start['sid'],
                    'record_status' => PartyRecordStatus::STARTED,
                ]);
            }

            logger()->debug("PARTY_HISTORY_RECORD after start: ", compact($party_history->partyHistoryRecord->party_history_id));
            DB::commit();

            logger()->info('success start record party.', compact($party_id));
            return true;
        } catch (\Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->debug('failed start record party', compact('party_id'));
        }
        return $result;
    }

    /**
     * endRecordAndConvert
     *
     * @param int $party_id
     * @return bool
     * @throws \Throwable
     */
    public function endRecord(int $party_id)
    {
        $result = false;
        try {
            $result = $this->endRecord($party_id);
            if ($result) {
                $this->convertRecordFile($party_id);
            }
            return true;
        } catch (\Throwable $e) {
            throw_if_api_exception($e);
            logger()->error($e);
        }
        return $result;
    }


    /**
     * convert Record File
     *
     * @param int $party_id
     * @return bool
     * @throws \Throwable
     */
    private function convertRecordFile(int $party_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var PartyHistory $psrty_history */
            $psrty_history = PartyHistory::query()->where('party_id', $party_id)->first();
            if (!$psrty_history) {
                throw new ErrorException('Can not find party_history with party_id, cancel convert record file');
            }

            $storage_file_id = $psrty_history->partyHistoryRecord->record_file_id;
            /** @var StorageFile $storage_file */
            $storage_file = StorageFile::query()->findOrFail($storage_file_id);

            if (!isset($storage_file->file_path)) {
                throw new ErrorException('Can not find file_path in storage, cancel convert record file');
            }

            $file_path = $storage_file->file_path;
            [$record_result, $file_convert] = AwsConvertManager::convertFileStorage($file_path);

            if (!$record_result) {
                throw new ErrorException('Can not save file to storage, cancel convert record file');
            }
            logger()->debug('Success to convert record file', $file_convert);

            $storage_file->updateOrThrow([
                'file_type' => 'video/mp4',
                'file_size' => $file_convert['size'],
                'file_url' => $file_convert['url']
            ]);

            DB::commit();
            logger()->info('Success convert file record', compact('party_id'));
            return true;

        } catch (\Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->debug('failed to convert file record', compact('party_id'));
        }
        return $result;
    }

    /**
     * partyQuery
     *
     * @return Builder|EloquentParty
     */
    private function partyQuery()
    {
        return EloquentParty::query()->getModel();
    }

    /**
     * settingPartyQuery
     *
     * @return Builder|EloquentSettingParty
     */
    private function settingPartyQuery()
    {
        return EloquentSettingParty::query()->getModel();
    }
}
