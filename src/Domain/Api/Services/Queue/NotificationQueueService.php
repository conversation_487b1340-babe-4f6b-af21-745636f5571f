<?php

namespace Src\Domain\Api\Services\Queue;

use Src\Enum\EventType;

/**
 * Class NotificationQueueService
 * @package Src\Domain\Api\Services
 */
class NotificationQueueService
{

    /**
     * start
     *
     * @param int $user_id
     * @param int $number_unread_notification
     * @return bool
     */
    public function sendNewBadge(int $user_id, int $number_unread_notification )
    {
        request_socket_api('socket_api.notification.new', [
            'event_type' => EventType::NOTIFICATION_NEW,
            'receive_user_ids' => [$user_id],
            'number_unread_notification' => $number_unread_notification
        ]);
        return true;
    }
}
