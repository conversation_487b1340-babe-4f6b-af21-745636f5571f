<?php

namespace Src\Domain\Api\Services\Queue;

use App\Eloquent\Party as EloquentParty;
use App\Jobs\Party\PartyEndJob;
use App\Jobs\Party\PartyNoticeEndJob;
use App\Jobs\PartyRecord\PartyRecordStartJob;
use DB;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\Party\DeleteService;
use Src\Domain\Api\Services\Party\UpdateService;
use Src\Domain\Api\Services\PartyHistoryService;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Enum\EventType;
use Src\Enum\PartyStatus;
use Src\Utils\Agora\GenToken\AgoraGenToken;
use Src\Utils\Util;
use Throwable;

/**
 * Class PartyQueueService
 * @package Src\Domain\Api\Services
 */
class PartyQueueService
{

    use PartyTrait;

    /**
     * start
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws Throwable
     */
    public function startParty(int $party_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()
                ->with('participants.userProfile', 'partyGroups')
                ->findOrFail($party_id);

            // Update Party
            $start_time = now();
            $end_time = $start_time->copy()->addMinutes(PARTY_NUMBER_MINUTE_FOR_ONE_TICKET);
            $party->updateOrThrow([
                'party_status' => PartyStatus::STARTED,
                'start_at' => $start_time,
                'expire_at' => $end_time,
                'party_time' => Util::calculateTime($start_time, $end_time),
            ]);

            // Grant permission
            (new UpdateService())->joindUserAndGenAgoraToken($party);

            // Delete setting party
            $this->deleteSettingPartyFromParty($party);

            // Create history
            (new PartyHistoryService())->createPartyHistory($party->id);

            // Dispatch notice end party job
            PartyNoticeEndJob::dispatch($party->id)->delay($end_time->copy()->subMinutes(PARTY_MINUTE_NOTICE_BEFORE_END));

            // Dispatch end party job
            PartyEndJob::dispatch($party->id)->delay($end_time);

            DB::commit();

            // Send Socket
            $participants = $party->participants()->get();
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party, true);
            [$joined_user_ids, $agora_tokens] = $this->getAgoraInfoOarParticipants($party, $participants);

            // Create push, socket data
            $addition_data = [
                'party_id' => $party->id,
                'joined_user_ids' => $joined_user_ids,
                'agora_tokens' => $agora_tokens
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_STARTED, $participant_user_ids, $participant_fcm_tokens,
                $addition_data, 'party.started');
            request_socket_api_and_push_notification('socket_api.party.started', $notify_data, $notify_payload);

            // Start record party
            PartyRecordStartJob::dispatch($party->id);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }
        return $result;
    }

    /**
     * getAgoraInfoOarParticipants
     *
     * @param EloquentParty $party
     * @param Collection $participants
     * @return array[]
     */
    private function getAgoraInfoOarParticipants(EloquentParty $party, Collection $participants)
    {
        $agora_tokens = [];
        $joined_user_ids = [];
        foreach ($participants as $participant) {
            if ($participant->pivot->agora_token) {
                $agora_tokens[] = AgoraGenToken::createFormatAgoraTokensResponse($party->channel_id, $participant->id,
                    $participant->pivot->agora_token, $participant->pivot->expire_at);
                $joined_user_ids[] = $participant->id;
            }
        }
        logger()->debug('LIST_AGORA_TOKENS', $agora_tokens);

        return [$joined_user_ids, $agora_tokens];
    }

    /**
     * noticeEndParty
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws Throwable
     */
    public function noticeEndParty(int $party_id)
    {
        $result = false;
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->findOrFail($party_id);

            // Send Socket
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);

            // Create push, socket data
            $addition_data = [
                'party_id' => $party->id,
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_NOTICE_END, $participant_user_ids, $participant_fcm_tokens,
                $addition_data, 'party.notice_end');
            request_socket_api_and_push_notification('socket_api.party.notice_end', $notify_data, $notify_payload);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }
        return $result;
    }

    /**
     * endParty
     *
     * @param int $party_id
     * @param bool $is_from_queue
     * @return bool|mixed|Party
     * @throws Throwable
     */
    public function endParty(int $party_id, bool $is_from_queue = false)
    {
        $party_delete_service = new DeleteService();
        return $party_delete_service->endParty($party_id, $is_from_queue);
    }

}
