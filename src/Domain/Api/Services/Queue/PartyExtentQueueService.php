<?php

namespace Src\Domain\Api\Services\Queue;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Enum\EventType;
use Src\Enum\PartyStatus;
use Src\Enum\UseTicketType;
use Src\Utils\Util;
use Throwable;

/**
 * Class PartyExtentQueueService
 * @package Src\Domain\Api\Services
 */
class PartyExtentQueueService
{
    use PartyTrait;

    /**
     * Update
     *
     * @param int $creator_id
     * @param int $party_id
     * @return bool|mixed
     */
    public function askExtendParty(int $creator_id, int $party_id)
    {
        $result = false;
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->findOrFail($party_id);
            /** @var User $creator */
            $creator = User::query()->getModel()->with('userProfile')->findOrFail($creator_id);
            logger()->debug('CREATOR', [$creator->toArray()]);
            // Send Socket
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);

            // Create push, socket data
            $addition_data = [
                'creator_user_id' => $creator->id,
                'creator_nickname' => $creator->userProfile->nickname,
                'party_id' => $party->id,
                'use_ticket_type' => UseTicketType::ONE_TICKET,
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_ASK_EXTEND, $participant_user_ids, $participant_fcm_tokens,
                $addition_data, 'party.ask_extend', ['nickname' => $creator->userProfile->nickname]);
            request_socket_api_and_push_notification('socket_api.party.ask_extend', $notify_data, $notify_payload);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }

        return $result;
    }

    /**
     * partyQuery
     *
     * @return Builder|EloquentParty
     */
    private function partyQuery()
    {
        return EloquentParty::query()->getModel();
    }

    /**
     * acceptExtendParty
     *
     * @param int $creator_id
     * @param int $party_id
     * @return bool|Party
     */
    public function acceptExtendParty(int $creator_id, int $party_id)
    {
        $result = false;
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->findOrFail($party_id);
            /** @var User $creator */
            $creator = User::query()->getModel()->with('userProfile')->findOrFail($creator_id);

            // Send Socket
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);

            // Create push, socket data
            $addition_data = [
                'creator_user_id' => $creator->id,
                'creator_nickname' => $creator->userProfile->nickname,
                'party_id' => $party->id,
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_ACCEPTED_EXTEND, $participant_user_ids, $participant_fcm_tokens,
                $addition_data, 'party.accepted_extend', ['nickname' => $creator->userProfile->nickname]);
            request_socket_api_and_push_notification('socket_api.party.accepted_extend', $notify_data, $notify_payload);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }

        return $result;
    }

    /**
     * extendParty
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws Throwable
     */
    public function extendParty(int $party_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->findOrFail($party_id);

            $expire_at = Util::getTimeEndFromUseTicketType(UseTicketType::ONE_TICKET, $party->expire_at);
            $party->updateOrThrow([
                'party_status' => PartyStatus::EXTENDED,
                'expire_at' => $expire_at,
                'party_time' => Util::calculateTime($party->start_at, $expire_at),
                'is_matched_extend' => true
            ]);

            $this->extendUserAndGenAgoraToken($party);

            DB::commit();

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }
        return $result;
    }

    /**
     * extendUser
     *
     * @param int $extend_user_id
     * @param int $party_id
     * @param array $agora_token_info
     * @return bool|Party
     */
    public function extendUser(int $extend_user_id, int $party_id, array $agora_token_info)
    {
        $result = false;
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->findOrFail($party_id);

            /** @var User $creator */
            $creator = User::query()->getModel()->findOrFail($extend_user_id);

            // Create push, socket data
            $addition_data = [
                'creator_user_id' => $extend_user_id,
                'party_id' => $party->id,
                'agora_token' => $agora_token_info,
            ];

            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_USER_EXTENDED, [$creator->id], [$creator->fcm_token],
                $addition_data, 'party.user_extended', ['nickname' => $creator->userProfile->nickname]);
            request_socket_api_and_push_notification('socket_api.party.user_extended', $notify_data, $notify_payload);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }

        return $result;
    }

}
