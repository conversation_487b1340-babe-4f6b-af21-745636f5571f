<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\IapReceipt;
use App\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use RuntimeException;
use Src\Enum\MobilePlatform;
use Src\Exception\APIRuntimeException;

/**
 * Class ValidateReceiptServiceIOS
 *
 * @package Src\Domain\Api\Services
 */
class ValidateReceiptServiceIOS
{

    /** @var string */
    private $receipt_data;

    /** @var Model */
    private $plan_model;

    public function __construct($receipt_data, $plan_model)
    {
        $this->plan_model = $plan_model;
        $this->receipt_data = $receipt_data;
    }

    /**
     * validateReceipt
     *
     * @return array
     */
    public function validateReceipt()
    {
        // Get Info of receipt
        $in_apps = $this->getReceipInfo();

        if (empty($in_apps)) {
            throw new RuntimeException('Invalid receipt');
        }

        // Check valid in db
        $valid_receipts = [];
        $errors = [];
        foreach ($in_apps as $in_app) {
            [$result, $error] = $this->checkInfo($in_app);
            if (!$result) {
                $errors[] = $error['message'];
                continue;
            }
            $valid_receipts[] = $in_app;
        }

        if (empty($valid_receipts)) {
            $num = count($errors);
            throw new APIRuntimeException($errors[$num - 1]);
        }

        $valid_receipt = $valid_receipts[0];
        logger()->debug('VALID_RECEIPT', [$valid_receipt]);

        $plan = null;
        if ($valid_receipt) {
            $plan = $this->plan_model
                ->where('ios_package_id', $valid_receipt['product_id'])
                ->first();
        }
        return [$valid_receipt, $plan];
    }

    /**
     * getReceipInfo
     *
     * @return array
     */
    private function getReceipInfo()
    {
        $result = [];
        $url = 'https://sandbox.itunes.apple.com/verifyReceipt';

        $response = Http::post($url, [
            'receipt-data' => $this->receipt_data
        ]);

        if (!$response->ok()) {
            logger()->error("Error validate apple receipt: $url");
            throw new APIRuntimeException('api_errors.receipt_validate.error_validate_with_apple');
        }

        $response_data = $response->json();

        if ($response_data['status'] !== 0) {
            throw new APIRuntimeException('api_errors.receipt_validate.invalid_receipt');
        }

        $in_apps = $response_data['receipt']['in_app'];

        logger()->debug("Success validate apple receipt: $url", [$response->json()]);

        foreach ($in_apps as $in_app) {
            $result[] = [
                'product_id' => $in_app['product_id'],
                'purchase_date' => $in_app['purchase_date'],
                'transaction_id' => $in_app['transaction_id'],
            ];
        }

        return $result;
    }

    /**
     * checkInfo
     *
     * @param array $in_app
     * @return bool
     */
    private function checkInfo(array $in_app): array
    {
        $error = [];
        $is_exist_plan = $this->plan_model
            ->where('ios_package_id', $in_app['product_id'])
            ->exists();
        logger()->debug('Plan model: ', ['table' => $this->plan_model->getTable()]);

        if (!$is_exist_plan) {
            logger()->debug('Package not exist', [$in_app['product_id']]);
            $error['message'] = 'api_errors.receipt_validate.product_package_not_exist';
            return [false, $error];
        }

        if (IapReceipt::query()->getModel()->where('transaction_id', $in_app['transaction_id'])->exists()) {
            $error['message'] = 'api_errors.receipt_validate.transaction_exist';
            return [false, $error];
        }

        return [true, []];
    }

    /**
     * saveIapReceipt
     *
     * @param $receipt
     * @param int $user_id
     */
    public function saveIapReceipt($receipt, int $user_id)
    {
        IapReceipt::query()->getModel()->createOrThrow([
            'user_id' => $user_id,
            'mobile_platform' => MobilePlatform::IOS,
            'product_id' => $receipt['product_id'],
            'purchase_at' => $receipt['purchase_date'],
            'transaction_id' => $receipt['transaction_id'],
        ]);
    }

}
