<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\PointHistory as EloquentPointHistory;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\PointHistory\PointHistory;
use Src\Domain\Api\Models\PointHistory\PointHistoryForm;
use Src\Enum\PointUpdateDiv;
use Throwable;

/**
 * Class PointHistoryService
 * @package Src\Domain\Api\Services
 */
class PointHistoryService
{

    /**
     * fetchAll
     *
     * @param AuthorInterface $author
     * @param PointHistoryForm $form
     * @return Collection|PointHistory[]
     */
    public function fetchAll(AuthorInterface $author, PointHistoryForm $form): array
    {
        $point_update_div = $form->getPointUpdateDiv();

        if ($point_update_div === PointUpdateDiv::BUY) {
            $point_histories = $this->pointHistoryQuery()
                ->where('user_id', $author->getId())
                ->where('point_update_div', $point_update_div)
                ->where('point_at', '>=', now()->subMonths(6))
                ->limit(100)
                ->get();
            $list = $point_histories->transform(static function ($point_history) {
                return (new PointHistory($point_history))->toListApiResponse();
            });

            $result = [
                'current_point' => $author->getPoint(),
                'point_histories' => $list
            ];
        } else if ($point_update_div === PointUpdateDiv::PRESENT) {
            $point_histories = $this->pointHistoryQuery()
                ->with('receiveUser.userProfile')
                ->where('user_id', $author->getId())
                ->where('point_update_div', $point_update_div)
                ->where('point_at', '>=', now()->subMonths(12))
                ->limit(100)
                ->get();

            $list = $point_histories->transform(static function ($point_history) {
                return (new PointHistory($point_history))->toListApiResponse();
            });
            $result = [
                'point_histories' => $point_histories
            ];
        } else {
            $point_histories = $this->pointHistoryQuery()
                ->where('user_id', $author->getId())
                ->where('point_update_div', $point_update_div)
                ->where('point_at', '>=', now()->subMonths(12))
                ->limit(100)
                ->get();


            $list = $point_histories->transform(static function ($point_history) {
                return (new PointHistory($point_history))->toListApiResponse();
            });

            $result = [
                'refund_money' => 0,
                'point_histories' => $list
            ];
        }

        return $result;
//            return PointHistory::toListApiResponse($user_point_histories, $point_update_div);
    }

    /**
     * pointHistoryQuery
     *
     * @return Builder|EloquentPointHistory
     */
    private function pointHistoryQuery()
    {
        return EloquentPointHistory::query()->getModel();
    }

    /**
     * Store
     *
     * @param AuthorInterface $author
     * @param PointHistoryForm $form
     * @return bool|mixed
     */
    public function store(AuthorInterface $author, PointHistoryForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($author, $form) {
                /** @var EloquentPointHistory $point_history */
                $point_history = $this->pointHistoryQuery()->createOrThrow($form->createAttributes());
                logger()->info('success create point_history.', ['point_histories.id' => $point_history->id]);
                return new PointHistory($point_history);
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed create point_history.', $form->toArray());
        }
        return $result;
    }

}
