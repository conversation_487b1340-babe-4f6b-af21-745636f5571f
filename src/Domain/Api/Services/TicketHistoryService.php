<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\TicketHistory as EloquentTicketHistory;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\TicketHistory\TicketHistory;
use Src\Enum\TicketUpdateDiv;

/**
 * Class TicketHistoryService
 * @package Src\Domain\Api\Services
 */
class TicketHistoryService
{

    /**
     * fetchAll
     *
     * @param AuthorInterface $author
     * @return array
     */
    public function fetchAll(AuthorInterface $author): array
    {
        $ticket_histories = $this->ticketHistoryQuery()
            ->where('user_id', $author->getId())
            ->where('ticket_update_div', TicketUpdateDiv::BUY)
            ->orderByDesc('id')
            ->get();

        $ticket_histories->transform(static function ($ticket_history) {
            return (new TicketHistory($ticket_history))->toListApiResponse();
        });

        return $ticket_histories->toArray();
    }

    /**
     * ticketHistoryQuery
     *
     * @return Builder|EloquentTicketHistory
     */
    private function ticketHistoryQuery()
    {
        return EloquentTicketHistory::query()->getModel();
    }
}
