<?php

namespace Src\Domain\Api\Services\Party;

use App\Eloquent\Party as EloquentParty;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\Traits\FriendTrait;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Enum\PartyParticipantStatus;
use Src\Enum\PartyStatus;

/**
 * Class ReadService
 * @package Src\Domain\Api\Services
 */
class ReadService
{

    use PartyTrait;
    use FriendTrait;

    /**
     * fetchAllParticipants
     *
     * @param AuthorInterface $author
     * @param $party_id
     * @param bool $is_split_group
     * @return array
     */
    public function fetchAllParticipants(AuthorInterface $author, $party_id, bool $is_split_group): array
    {
        $user_id = $author->getId();
        $check_party = $this->partyQuery()->findOrFail($party_id);

        /** @var  EloquentParty $party */
        $party = $this->partyQuery()
            ->with(['partyGroups.partyParticipants' => function ($query) use ($check_party) {
                $query->when($check_party->party_status === PartyStatus::EXTENDED, function ($query) {
                    $query->whereNotIn('party_participant_status', PartyParticipantStatus::getListNotInParty());
                });
                $query->with('user.userProfile.storageFile');
            }])
            ->whereHas('participants', function ($query) use ($user_id) {
                $query->where('id', $user_id);
            })
            ->findOrFail($party_id);

        $user_friend_ids = $this->getAllFriendIdsOfUser($user_id)->toArray();
        $party_model = new Party($party);
        if ($is_split_group) {
            return $party_model->getParticipantSplitGroups($user_id, $user_friend_ids);
        }

        return $party_model->getParticipants($user_id, $user_friend_ids);
    }

}
