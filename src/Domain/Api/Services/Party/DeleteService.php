<?php

namespace Src\Domain\Api\Services\Party;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\PartyGroup;
use App\Eloquent\SettingParty as EloquentSettingParty;
use App\Jobs\PartyRecord\PartyRecordEndJob;
use Carbon\Carbon;
use DB;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Services\PartyExtendService;
use Src\Domain\Api\Services\PartyHistoryService;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\UserNotificationService;
use Src\Enum\EventType;
use Src\Enum\PartyParticipantStatus;
use Src\Enum\PartyStatus;
use Src\Exception\ErrorException;
use Src\Utils\Util;
use Throwable;

/**
 * Class DeleteService
 * @package Src\Domain\Api\Services
 */
class DeleteService
{

    use PartyTrait;

    /**
     * End Party
     *
     * @param AuthorInterface $author
     * @param int $party_id
     * @return bool|mixed|Party
     * @throws Throwable
     */
    public function end(AuthorInterface $author, int $party_id)
    {
        return $this->endParty($party_id);
    }

    /**
     * End party from other service
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws Throwable
     */
    public function endParty(int $party_id, bool $is_from_queue = false)
    {
        $result = false;
        /** @var EloquentParty $party */
        $party = $this->partyQuery()->where('party_status', '!=', PartyStatus::ENDED)->with('partyGroups')->findOrFail($party_id);

//        (new PartyExtendService())->processUpdateExtend($party);
        DB::beginTransaction();
        try {
            if ($is_from_queue) {
//            logger()->debug('greaterThan', [now()->addSeconds(3)->greaterThan($party->expire_at) ]);
//            if(now()->diffInMinutes($party->expire_at, false) > 1 ){
                if ($party->expire_at && now()->addSeconds(3)->greaterThan($party->expire_at)) {
                    $this->processEndParty($party);
                } else {
                    logger()->debug('PROCESS_UPDATE_EXTEND', $party->toArray());
                    (new PartyExtendService())->processUpdateExtend($party);
                }
            } else {
                $this->processEndParty($party);
            }

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }
        return $result;
    }

    /**
     * processEndParty
     *
     * @param EloquentParty $party
     * @throws Throwable
     * @throws ErrorException
     */
    private function processEndParty(EloquentParty $party)
    {
        // Update party
        $ended_at = now()->lessThan($party->expire_at) ? now() : $party->expire_at;
        $party_time = Util::calculateTime($party->start_at, $party->expire_at);
        $party->updateOrThrow([
            'party_status' => PartyStatus::ENDED,
            'ended_at' => $ended_at,
            'real_party_time' => Util::calculateTime($party->start_at, $ended_at)
        ]);

        // Update time for participants
        $remain_participants = $party->participants()
            ->wherePivotNotIn('party_participant_status', PartyParticipantStatus::getListNotInParty())->get();
        foreach ($remain_participants as $remain_participant){
            $party->participants()->updateExistingPivot($remain_participant->id, [
                'party_time' => $party_time,
                'real_party_time' => Util::calculateTime(Carbon::make($remain_participant->pivot->started_at), $ended_at)
            ]);
        }

        // Delete setting party
        $this->deleteSettingPartyFromParty($party);

        (new PartyHistoryService())->updatePartyHistory($party->id);

        // Send Socket
        [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);

        // Update action for notification of setting_parties
        /** @var PartyGroup $party_group */
        foreach ($party->partyGroups as $party_group) {
            UserNotificationService::disableAction(EloquentSettingParty::getTableName(), $party_group->setting_party_id);
        }

        // Update action for notification
        UserNotificationService::disableAction(EloquentParty::getTableName(), $party->id);

        DB::commit();

        // Create push, socket data
        $addition_data = [
            'party_id' => $party->id,
        ];
        [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_END, $participant_user_ids, $participant_fcm_tokens,
            $addition_data, 'party.end');
        request_socket_api_and_push_notification('socket_api.party.end', $notify_data, $notify_payload);

        // End record party
        PartyRecordEndJob::dispatch($party->id);
    }

    /**
     * deleteTimeout
     *
     * @return bool
     * @throws Throwable
     */
    public function deleteTimeout()
    {
        logger()->debug('delete party timeout Start.');
        $parties = $this->partyQuery()
            ->where('match_at', '<=', Carbon::now()->subMinutes(TIMEOUT_PARTY))
            ->where('party_status', PartyStatus::MATCHED)
            ->with('participants')
            ->get();
        logger()->debug('List party will delete', $parties->pluck('id')->toArray());

        if ($parties->isEmpty()) {
            logger()->debug('Have not setting party timeout');
            return true;
        }

        $deleted_party_ids = [];
        foreach ($parties as $party) {
            $result = $this->deleteOneParty($party->id);
            if ($result) {
                $deleted_party_ids[] = $party->id;
            }
        }

        logger()->debug('success deleteTimeout.', $deleted_party_ids);
        return true;
    }

    //========================== End Party ============================

    /**
     * Update
     *
     * @param int $party_id
     * @return bool|mixed
     * @throws Throwable
     */
    public function deleteOneParty(int $party_id)
    {
        $result = false;
        DB::beginTransaction();
        try {
            /** @var EloquentParty $party */
            $party = $this->partyQuery()->with(['partyGroups', 'participants'])->findOrFail($party_id);

            if (now()->diffInMinutes($party->expire_at, false) > 1) {
                logger()->debug('END_TIME_AFTER_NOW, IGNORE END PARTY', $party->toArray());
                return true;
            }

            // Enable setting party again
            $this->enableSettingPartyStatus($party);

            // Delete party
            foreach ($party->partyGroups as $partyGroup) {
                $partyGroup->delete();
            }
            $party->participants()->sync([]);
            $party->delete();

            DB::commit();

            // Send Socket
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);

            // Create push, socket data
            $addition_data = [
                'party_id' => $party->id,
            ];
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_MATCHING_TIMEOUT, $participant_user_ids, $participant_fcm_tokens,
                $addition_data, 'party.matching_timeout');
            request_socket_api_and_push_notification('socket_api.party.matching_timeout', $notify_data, $notify_payload);

            logger()->info('success update party.', compact($party_id));
            return new Party($party);
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->debug('failed update party', compact('party_id'));
        }
        return $result;
    }

    /**
     * delete
     *
     * @param AuthorInterface $author
     * @param int $party_id
     * @return bool
     */
    public function delete(AuthorInterface $author, int $party_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($author, $party_id) {
                /** @var EloquentParty $party */
                $party = $this->partyQuery()->findOrFail($party_id);
                $party->delete();
                logger()->info('success delete party.', compact('party_id'));
                return true;
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete party', compact('party_id'));
        }
        return $result;
    }

}
