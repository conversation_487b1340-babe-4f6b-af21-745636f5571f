<?php

namespace Src\Domain\Api\Services\Party;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\PartyGroup;
use App\Eloquent\SettingParty;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Models\Party\PartyForm;
use Src\Domain\Api\Models\Party\PartyGroupForm;
use Src\Domain\Api\Models\Party\PartyParticipantForm;
use Src\Domain\Api\Models\Party\PartyWithFriendForm;
use Src\Domain\Api\Services\PushVoipService;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\Traits\SettingPartyTrait;
use Src\Domain\Api\Services\UserNotificationService;
use Src\Enum\EventType;
use Src\Enum\Gender;
use Src\Enum\MobilePlatform;
use Src\Enum\NotificationDiv;
use Src\Enum\PartyParticipantStatus;
use Src\Enum\SettingPartyStatus;
use Src\Exception\APIRuntimeException;
use Src\Exception\ErrorException;
use Src\Utils\CustomResponse;
use Src\Utils\GenId;
use Src\Utils\Util;
use Throwable;

/**
 * Class CreateService
 * @package Src\Domain\Api\Services
 */
class CreateService
{

    use SettingPartyTrait;
    use PartyTrait;

    /**
     * createWithFriend
     *
     * @param AuthorInterface $creator
     * @param PartyWithFriendForm $form
     * @return array|bool|CustomResponse
     * @throws Throwable
     */
    public function createWithFriend(AuthorInterface $creator, PartyWithFriendForm $form)
    {
        $result = false;
        DB::beginTransaction();
        try {
            $creator_id = $creator->getId();
            $setting_party_id = $form->getSettingPartyId();

            // Check exist party
            $result = $this->checkUserInParty($creator_id);
            if ($result instanceof CustomResponse) {
                return $result->getResponseData();
            }

            // Get Setting Party Of User
            /** @var SettingParty $creator_setting_party */
            $creator_setting_party = $this->settingPartyQuery()
                ->with('participants.userProfile.avatar')
                ->findOrFail($setting_party_id);

            if (!env('APP_TEST_LOCAL', false)) {
                if ($creator_setting_party->status === SettingPartyStatus::WAITING_USER_JOIN) {
                    throw new APIRuntimeException('api_errors.setting_party.not_all_joined');
                }

                if ($creator_setting_party->status === SettingPartyStatus::PENDING) {
                   throw new APIRuntimeException('api_errors.setting_party.pending');
                }
            }

            // Split group
            $party_groups = $this->getParticipantGroupsByIds($creator_setting_party, $creator_id);

            // CREATE PARTY
            $party = $this->createPartyWithFriend($form, $party_groups, $creator_setting_party);

            // Get data for socket & push
            [$notify_data, $notify_payload] = $this->getSocketAndPushDataForPartyWithFriend($creator, $party,
                $party_groups['participants'], $party_groups['participant_ids']);

            // Create Notification
            $notification_id = UserNotificationService::createUserNotification($party_groups['participant_ids'], NotificationDiv::MATCHING_PARTY,
                EloquentParty::getTableName(), $party->id, $notify_payload, $notify_data, null, User::getTableName(), $creator->getId(), true);
            $notify_data['notification_id'] = $notification_id;

//            DB::rollBack();
//            return false;
            DB::commit();

            // Send Socket
            request_socket_api('socket_api.party.matched', $notify_data);

            // Send push notification or voip
            $this->sendPushNotificationOrVoip($party, $notify_data, $notify_payload);

            // Send Socket
//            request_socket_api_and_push_notification('socket_api.party.matched', $notify_data, $notify_payload);

            logger()->info('success match setting_party.', ['party_id' => $party->id]);
            return (new Party($party))->toDetailApiResponse();
        } catch (Throwable $e) {
            DB::rollBack();
            logger()->error($e);
            logger()->info('failed create setting_party.');
            throw_if_api_exception($e);
        }

        return $result;
    }

    /**
     * getParticipantGroupsByIds
     *
     * @param SettingParty $setting_party
     * @param $creator_id
     * @return array
     */
    private function getParticipantGroupsByIds(SettingParty $setting_party, $creator_id)
    {
        $invite_user_ids = $setting_party->participants()->pluck('id')->toArray();
        $participants = User::queryModel()->with('userProfile')->findMany(array_merge([], $invite_user_ids, [$creator_id]));
        $male_participants = $participants->filter(function ($participant) {
            /** @var User $participant */
            return $participant->userProfile->gender_party === Gender::MALE;
        });

        $female_participants = $participants->filter(function ($participant) {
            /** @var User $participant */
            return $participant->userProfile->gender_party === Gender::FEMALE;
        });

        [$male_min_age, $male_max_age] = $this->getMinMaxAgeFromParticipants($male_participants);
        [$female_min_age, $female_max_age] = $this->getMinMaxAgeFromParticipants($female_participants);

        $male_party_group = [
            'gender' => Gender::MALE,
            'gender_partner' => Gender::FEMALE,
            'from_age' => $male_min_age,
            'to_age' => $male_max_age,
            'participants' => $male_participants,
            'participant_ids' => $male_participants->pluck('id')->toArray(),
        ];

        $female_party_group = [
            'gender' => Gender::FEMALE,
            'gender_partner' => Gender::MALE,
            'from_age' => $female_min_age,
            'to_age' => $female_max_age,
            'participants' => $female_participants,
            'participant_ids' => $female_participants->pluck('id')->toArray(),
        ];

        return [
            'male_party_group' => $male_party_group,
            'female_party_group' => $female_party_group,
            'participant_ids' => $participants->pluck('id')->toArray(),
            'participants' => $participants,
        ];
    }

    /**
     * createPartyWithFriend
     *
     * @param PartyWithFriendForm $form
     * @param array $party_groups
     * @param SettingParty $setting_party
     * @return EloquentParty
     * @throws ErrorException
     */
    private function createPartyWithFriend(PartyWithFriendForm $form, array $party_groups, SettingParty $setting_party)
    {
        $male_party_group = $party_groups['male_party_group'];
        $female_party_group = $party_groups['female_party_group'];

        // Save Party
        /** @var EloquentParty $party */
        $party = $this->partyQuery()->createOrThrow($form->createAttributes($setting_party));

        $party->updateOrThrow([
            'channel_id' => GenId::genPartyChannelId($party->id)
        ]);

        // Create party Group
        $male_party_group_model = PartyGroup::queryModel()->createOrThrow(
            PartyGroupForm::createWithFriendAttributes($party->id, $setting_party->id, $male_party_group));
        $female_party_group_model = PartyGroup::queryModel()->createOrThrow(
            PartyGroupForm::createWithFriendAttributes($party->id, $setting_party->id, $female_party_group));

        // Add participants
        $male_participant_data = PartyParticipantForm::createAttributes($male_party_group_model->id, $male_party_group['participant_ids']);
        $female_participant_data = PartyParticipantForm::createAttributes($female_party_group_model->id, $female_party_group['participant_ids']);

        $party->participants()->sync($male_participant_data + $female_participant_data);

        return $party;
    }

    /**
     * getSocketAndPushDataForPartyWithFriend
     *
     * @param AuthorInterface $creator
     * @param EloquentParty $party
     * @param Collection $participants
     * @param $participant_ids
     * @return array
     * @throws Throwable
     */
    private function getSocketAndPushDataForPartyWithFriend(AuthorInterface $creator, EloquentParty $party, Collection $participants, $participant_ids)
    {
        // Create push, socket data
        $addition_data = [
            'party_type' => $party->party_type,
            'creator_user_id' => $creator->getId(),
            'creator_nickname' => $creator->getUserProfile()->nickname,
            'party_id' => $party->id,
        ];

        [, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);
        [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_MATCHED, $participant_ids, $participant_fcm_tokens,
            $addition_data, 'party.matched', ['group_type' => $party->group_type]);

        return [$notify_data, $notify_payload];
    }

    /**
     * matching
     *
     * @param AuthorInterface $creator
     * @param PartyForm $form
     * @return array|bool|CustomResponse|null
     * @throws Throwable
     */
    public function matching(AuthorInterface $creator, PartyForm $form)
    {
        $result = false;
        DB::beginTransaction();
        try {
            $creator_id = $creator->getId();
            $setting_party_id = $form->getSettingPartyId();

            // Check exist party
            $result = $this->checkUserInParty($creator_id);
            if ($result instanceof CustomResponse) {
                return $result->getResponseData();
            }

            // Get Setting Party Of User
            /** @var SettingParty $creator_setting_party */
            $creator_setting_party = $this->settingPartyQuery()
                ->with('participants.userProfile.avatar')
                ->findOrFail($setting_party_id);

            if (!env('APP_TEST_LOCAL', false)) {
                if ($creator_setting_party->status === SettingPartyStatus::WAITING_USER_JOIN) {
                    throw new APIRuntimeException('api_errors.setting_party.not_all_joined');
                }

                if ($creator_setting_party->status === SettingPartyStatus::PENDING) {
                    throw new APIRuntimeException('api_errors.setting_party.pending');
                }
            }

            // Update status
            $creator_setting_party->updateOrThrow([
                'status' => SettingPartyStatus::WAIT_MATCHING
            ]);
            DB::commit();

            // Get match setting group
            $match_params = [
                'setting_party_id' => $creator_setting_party->id,
                'group_type' => $creator_setting_party->group_type,
                'from_age' => $creator_setting_party->from_age,
                'to_age' => $creator_setting_party->from_age,
                'from_age_partner' => $creator_setting_party->from_age_partner,
                'to_age_partner' => $creator_setting_party->to_age_partner,
                'gender' => $creator_setting_party->gender,
                'gender_partner' => $creator_setting_party->gender_partner,
            ];

            $matched_setting_party = $this->getMatchParties($match_params)->first();

            if (!$matched_setting_party) {
                return null;
            }

            // CREATE PARTY
            [$party, $participant_ids] = $this->createParty($form, $creator_setting_party, $matched_setting_party);

            // Get data for socket & push
            [$notify_data, $notify_payload] = $this->getSocketAndPushData($creator, $party, $participant_ids);

            // Create Notification
            $notification_id = UserNotificationService::createUserNotification($participant_ids, NotificationDiv::MATCHING_PARTY,
                EloquentParty::getTableName(), $party->id, $notify_payload, $notify_data, null, User::getTableName(), $creator->getId(), true);
            $notify_data['notification_id'] = $notification_id;

            DB::commit();

            // Send Socket
            request_socket_api('socket_api.party.matched', $notify_data);

            // Send push notification or voip
            $this->sendPushNotificationOrVoip($party, $notify_data, $notify_payload);

            logger()->info('success match setting_party.', ['setting_parties.id' => $setting_party_id]);
            return (new Party($party))->toDetailApiResponse();
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed create setting_party.');
        }

        return $result;
    }

    /**
     * sendPushNotificationOrVoip
     *
     * @param EloquentParty $party
     * @param array $notify_data
     * @param array $notify_payload
     */
    private function sendPushNotificationOrVoip(EloquentParty $party,array $notify_data, array $notify_payload){
        // Push iOS
        $ios_participants = $party->participants()->where('mobile_Platform', MobilePlatform::IOS)
            ->with('userVoip')->get();
        if(!$ios_participants->isEmpty()){
            logger()->debug('$ios_participants', $ios_participants->pluck('id')->toArray());
            $notify_data['receive_user_ids'] = $ios_participants->pluck('id')->toArray();
            $this->sendPushVoipMultiDevice($ios_participants,$notify_data, $notify_payload);
        }

        // Push Android
        $android_participants = $party->participants()->where('mobile_Platform', MobilePlatform::ANDROID)
            ->get();
        if(!$android_participants->isEmpty()){
            [$participant_user_ids, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenFromParticipants($android_participants);
            $notify_data['receive_user_ids'] = $participant_user_ids;
            $notify_payload['fcm_tokens'] = $participant_fcm_tokens;
            request_push_notification( $notify_data, $notify_payload);
        }

        DB::rollBack();
    }

    /**
     * sendPushVoipMultiDevice
     *
     * @param Collection $users
     * @param array $notify_data
     * @param array $notify_payload
     */
    private function sendPushVoipMultiDevice(Collection $users, array $notify_data, array $notify_payload){
        logger()->debug('sendPushVoipMultiDevice', $notify_data);
        /** @var User $user */
        foreach ($users as $user){
            if(optional($user->userVoip)->endpoint_arn){
                $push_voip_service = new PushVoipService();
                $push_voip_service->pushVoip($notify_payload['title'], $notify_payload['body'], $user->userVoip->endpoint_arn, $notify_data);
            }
        }
    }

    /**
     * createParty
     *
     * @param PartyForm $form
     * @param SettingParty $creator_setting_party
     * @param SettingParty $matched_setting_party
     * @return array
     * @throws ErrorException
     */
    private function createParty(PartyForm $form, SettingParty $creator_setting_party, SettingParty $matched_setting_party)
    {
        // Save Party
        /** @var EloquentParty $party */
        $party = $this->partyQuery()->createOrThrow($form->createAttributes($creator_setting_party));

        $party->updateOrThrow([
            'channel_id' => GenId::genPartyChannelId($party->id)
        ]);

        // Create party Group
        $creator_party_group = PartyGroup::queryModel()->createOrThrow(PartyGroupForm::createAttributes($party->id, $creator_setting_party));
        $matched_party_group = PartyGroup::queryModel()->createOrThrow(PartyGroupForm::createAttributes($party->id, $matched_setting_party));

        // Add participants
        $creator_participant_ids = $creator_setting_party->participants->pluck(['id'])->toArray();
        $matched_participant_ids = $matched_setting_party->participants->pluck(['id'])->toArray();
        $participant_ids = array_merge($creator_participant_ids, $matched_participant_ids);
        $participant_data = [];
        foreach ($participant_ids as $participant_id) {
            $party_group_id = in_array($participant_id, $creator_participant_ids) ? $creator_party_group->id : $matched_party_group->id;
            $participant_data[$participant_id] = [
                'party_participant_status' => PartyParticipantStatus::MATCHED,
                'party_group_id' => $party_group_id,
                'started_at' => null,
                'ended_at' => null,
                'agora_token' => null,
                'number_extended' => 0,
                'party_time' => 0,
                'expire_at' => null,
            ];
        }

        $party->participants()->sync($participant_data);

        // Delete Setting Group
        $creator_setting_party->update([
            'status' => SettingPartyStatus::PENDING
        ]);
        $matched_setting_party->update([
            'status' => SettingPartyStatus::PENDING
        ]);

        return [$party, $participant_ids];
    }

    /**
     * getSocketAndPushData
     *
     * @param AuthorInterface $creator
     * @param EloquentParty $party
     * @param $participant_ids
     * @return array
     * @throws Throwable
     */
    private function getSocketAndPushData(AuthorInterface $creator, EloquentParty $party, $participant_ids)
    {
        // Create push, socket data
        $addition_data = [
            'party_type' => $party->party_type,
            'creator_user_id' => $creator->getId(),
            'creator_nickname' => $creator->getUserProfile()->nickname,
            'party_id' => $party->id,
        ];
        [, $participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenOfParty($party);
        [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_MATCHED, $participant_ids, $participant_fcm_tokens,
            $addition_data, 'party.matched', ['group_type' => $party->group_type]);

        return [$notify_data, $notify_payload];
    }

}
