<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Party;
use App\Eloquent\Party as EloquentParty;
use App\Jobs\Party\PartyEndJob;
use App\Jobs\Party\PartyNoticeEndJob;
use App\Jobs\PartyExtend\PartyExtendAcceptJob;
use App\Jobs\PartyExtend\PartyExtendAskJob;
use App\Jobs\PartyExtend\PartyExtendExtendPartyJob;
use DB;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\PartyExtend\PartyExtendForm;
use Src\Domain\Api\Models\User\User;
use Src\Domain\Api\Services\Traits\PartyTrait;
use Src\Domain\Api\Services\Traits\TicketTrait;
use Src\Enum\EventType;
use Src\Enum\PartyParticipantStatus;
use Src\Enum\PartyStatus;
use Src\Exception\APIRuntimeException;
use Src\Exception\ErrorException;
use Src\Utils\Util;
use Throwable;

/**
 * Class PartyExtendService
 * @package Src\Domain\Api\Services
 */
class PartyExtendService
{
    use TicketTrait;
    use PartyTrait;

    /**
     * store
     *
     * @param AuthorInterface $author
     * @param PartyExtendForm $form
     * @return array|bool
     * @throws Throwable
     */
    public function store(AuthorInterface $author, PartyExtendForm $form)
    {
        $result = false;
        DB::beginTransaction();
        try {
            PartyExtendAskJob::dispatchAfterResponse($author->getId(), $form->getPartyId());

            return [
                'party_id' => $form->getPartyId()
            ];
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->info('failed create party_extend.', $form->toArray());
        }

        return $result;
    }

    /**
     * accept
     *
     * @param AuthorInterface $author
     * @param PartyExtendForm $form
     * @return bool|mixed
     * @throws Throwable
     */
    public function accept(AuthorInterface $author, PartyExtendForm $form)
    {

        $result = false;
        DB::beginTransaction();
        try {
            $result = $this->commonExtend($author, $form);

            logger()->info('success accept extend party.');
//            return $party ? (new PartyDetail($party))->toDetailApiResponse() : null;
            return $result;
        } catch (Throwable $e) {
            DB::rollBack();
            throw_if_api_exception($e);
            logger()->error($e);
            logger()->debug('failed update party_extend');
        }

        return $result;
    }

    /**
     * commonExtend
     *
     * @param AuthorInterface $author
     * @param PartyExtendForm $form
     * @return array|null
     * @throws Throwable
     */
    private function commonExtend(AuthorInterface $author, PartyExtendForm $form)
    {
        $creator_id = $author->getId();
        // Check condition
        $party = $this->checkValidConditionExtend($author, $form);

        // Save accept extend
        $extend_accept = $this->addPartyExtendAccept($party, $creator_id, false);
        if (!$extend_accept) {
            DB::rollBack();
            return null;
        }
        PartyExtendAcceptJob::dispatchNow($creator_id, $form->getPartyId());

        // Check to extend
        $this->extendForUser($party, $author);

        DB::commit();

        $participant = $party->participants()->where('id', $creator_id)->first();

        return [
            'user' => User::getUserAndProfile($participant),
            'participant_info' => $participant->pivot
        ];
    }

    /**
     * checkValidConditionExtend
     *
     * @param AuthorInterface $author
     * @param PartyExtendForm $form
     * @return EloquentParty
     */
    private function checkValidConditionExtend(AuthorInterface $author, PartyExtendForm $form): Party
    {
        $this->validateTicketToUse($author);

        /** @var Party $party */
        $party = Party::query()->getModel()->findOrFail($form->getPartyId());
        if ($party->party_status === PartyStatus::ENDED) {
            throw new APIRuntimeException('api_errors.party.party_ended_cant_extend');
        }

        return $party;
    }

    /**
     * addPartyExtendAccept
     *
     * @param EloquentParty $party
     * @param int $creator_id
     * @param $is_extended
     * @return bool
     */
    private function addPartyExtendAccept(Party $party, int $creator_id, $is_extended): bool
    {
        $party_group_id = $this->getPartyGroupIdOfUser($party, $creator_id);

        if (!$party->partyExtendAcceptUsers()->where('id', $creator_id)->exists()) {
            $party->partyExtendAcceptUsers()->attach([$creator_id => [
                'party_group_id' => $party_group_id,
                'is_extended' => $is_extended
            ]]);
            return true;
        } else {

            return false;
        }
    }

    /**
     * getPartyGroupIdOfUser
     *
     * @param EloquentParty $party
     * @param $creator_id
     * @return mixed
     */
    private function getPartyGroupIdOfUser(Party $party, $creator_id)
    {
        $participant = $party->participants()->where('id', $creator_id)->first();
        if (!$participant) {
            throw new APIRuntimeException('api_errors.party.not_in_party');
        }

        return $participant->pivot->party_group_id;
    }

    /**
     * extendForUser
     *
     * @param EloquentParty $party
     * @param AuthorInterface $author
     */
    private function extendForUser(Party $party, AuthorInterface $author)
    {
        // Check have user of two group have accepted
        $is_partner_accepted = $party->partyExtendAcceptUsers()
                ->get()
                ->unique('pivot.party_group_id')
                ->pluck('pivot.party_group_id')
                ->count() === 2;

        if ($party->is_matched_extend) {
            $this->extendUserAndGenAgoraToken($party);
        } else {
            if (!$party->is_matched_extend && $is_partner_accepted) {
                PartyExtendExtendPartyJob::dispatchAfterResponse($party->id);
            }
        }
    }

    //======================= For other service ==============================

    /**
     * processUpdateExtend
     *
     * @param EloquentParty $party
     * @throws Throwable
     * @throws ErrorException
     */
    public function processUpdateExtend(EloquentParty $party)
    {
        // Update party
        $party->updateOrThrow([
            'is_matched_extend' => false
        ]);

        /** @var BelongsToMany $extended_participants */
        $extended_participants = $party->participants()->wherePivot('is_extended', true)->get();
        $expired_participants = $party->participants()->wherePivot('is_extended', false)->get();

        // Update participant
        foreach ($extended_participants as $extended_participant) {
            $party->participants()->updateExistingPivot($extended_participant->id, [
                'is_extended' => false
            ]);
        }

        foreach ($expired_participants as $expired_participant) {
            $party->participants()->updateExistingPivot($expired_participant->id, [
                'party_participant_status' => PartyParticipantStatus::EXPIRED,
                'is_extended' => false,
                'expire_at' => now(),
            ]);
        }


        // Clear extend accepts
        $party->partyExtendAcceptUsers()->sync([]);

        // Dispatch notice end party job
        PartyNoticeEndJob::dispatch($party->id)->delay($party->expire_at->copy()->subMinutes(PARTY_MINUTE_NOTICE_BEFORE_END));

        // Dispatch end party job
        PartyEndJob::dispatch($party->id)->delay($party->expire_at);
        logger()->debug('DISPATCH_END_PARTY_JOB_AGAIN_AT', [$party->expire_at]);
        DB::commit();

        $addition_data = [
            'party_id' => $party->id,
        ];

        // Send Extended
        [$extended_participant_user_ids, $extended_participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenFromParticipants($extended_participants);
        logger()->debug('$extended_participant_user_ids', $extended_participant_user_ids);
        if (!empty($extended_participant_user_ids)) {

            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_STAGE_USER_EXTENDED, $extended_participant_user_ids, $extended_participant_fcm_tokens,
                $addition_data, 'party.stage_user_extended');
            request_socket_api_and_push_notification('socket_api.party.stage_user_extended', $notify_data, $notify_payload);
        }

        // Send Expired
        [$expired_participant_user_ids, $expired_participant_fcm_tokens] = $this->getParticipantIdsAndFCMTokenFromParticipants($expired_participants);
        logger()->debug('$expired_participant_user_ids', $expired_participant_user_ids);
        if (!empty($expired_participant_user_ids)) {
            [$notify_payload, $notify_data] = Util::formatSocketPushData(EventType::PARTY_STAGE_USER_EXPIRED, $expired_participant_user_ids, $expired_participant_fcm_tokens,
                $addition_data, 'party.stage_user_extended');
            request_socket_api_and_push_notification('socket_api.party.stage_user_expired', $notify_data, $notify_payload);
        }
    }

}
