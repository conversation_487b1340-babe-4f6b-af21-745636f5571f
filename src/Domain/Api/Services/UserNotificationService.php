<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\SettingParty as EloquentSettingParty;
use App\Eloquent\User;
use App\Eloquent\UserNotification as EloquentUserNotification;
use App\Jobs\NotificationJob;
use Illuminate\Support\Collection;
use Src\Domain\Api\Models\Auth\AuthorInterface;
use Src\Domain\Api\Models\Party\Party;
use Src\Domain\Api\Models\SettingParty\SettingParty;
use Src\Domain\Api\Models\UserNotification\UserNotification;
use Src\Domain\Api\Models\UserNotification\UserNotificationForm;
use Src\Enum\Boolean;
use Src\Enum\NotificationDiv;

/**
 * Class UserNotificationService
 * @package Src\Domain\Api\Services
 */
class UserNotificationService
{

    /**
     * createUserNotification
     *
     * @param array $user_ids
     * @param int $notification_div
     * @param $foreign_table
     * @param $foreign_id
     * @param $notify_payload
     * @param $notify_data
     * @param null $party_id
     * @param null $creator_foreign_table
     * @param null $creator_foreign_id
     * @param bool $has_action
     * @return int
     */
    public static function createUserNotification(array $user_ids, int $notification_div, $foreign_table, $foreign_id,
                                                  $notify_payload, $notify_data, $party_id = null, $creator_foreign_table = null, $creator_foreign_id = null, $has_action = false): int
    {
        logger()->debug('$notify_data', $notify_data);
        logger()->debug('USER_IDS', $user_ids);
        // Create notification and relations
        $user_notification = EloquentUserNotification::queryModel()->createOrThrow(UserNotificationForm::createNotificationAttributes($notification_div,
            $foreign_table, $foreign_id, $notify_payload, $notify_data, $party_id, $has_action));

        $user_notification->users()->attach($user_ids);
        if ($creator_foreign_table && $creator_foreign_id) {
            $user_notification->creator()->create([
                'foreign_table' => $creator_foreign_table,
                'foreign_id' => $creator_foreign_id
            ]);
        }

        // Update badge
        $users = User::queryModel()->whereIn('id', $user_ids)->get();
        logger()->debug('USERS', $users->toArray());

        foreach ($users as $user) {
            $user->userNotificationUnread()->increment('number_unread_notification');
            $number_unread_notification = optional($user->userNotificationUnread()->first())->number_unread_notification;

            NotificationJob::dispatch($user->id, $number_unread_notification);
        }
        logger()->debug('CREAATED_NOTIFICATION_ID', [$user_notification->id]);
        return $user_notification->id;
    }

    /**
     * disableAction
     *
     * @param string $foreign_table
     * @param int $foreign_id
     */
    public static function disableAction(string $foreign_table, int $foreign_id)
    {
        // Update action for notification
        $notifications = EloquentUserNotification::queryModel()
            ->where('foreign_table', $foreign_table)
            ->where('foreign_id', $foreign_id)
            ->get();

        if (!$notifications->isEmpty()) {
            logger()->debug('DISABLE_ACTION', [$foreign_table, $foreign_id]);
            foreach ($notifications as $notification) {
                $notification->update(['has_action' => false]);
            }
        }
    }

    /**
     * fetchAll
     *
     * @param AuthorInterface $author
     * @return Collection
     */
    public function fetchAll(AuthorInterface $author): Collection
    {
        /** @var User $user */
        $user = User::query()->getModel()->findOrFail($author->getId());
        $user_notifications = $user->userNotifications()->orderByDesc('id')->get();

        $user->userNotificationUnread()->update(['number_unread_notification' => 0]);

        return $user_notifications->transform(static function ($user_notification) {
            return (new UserNotification($user_notification))->toListApiResponse();
        });
    }

    /**
     * findOrFail
     *
     * @param AuthorInterface $author
     * @param int $user_notification_id
     * @return array
     */
    public function findOrFail(AuthorInterface $author, int $user_notification_id): array
    {
        /** @var EloquentUserNotification $user_notification */
        $user = User::query()->getModel()->findOrFail($author->getId());
        $user_notification = $user->userNotifications()->where('id', $user_notification_id)->first();
        throw_model_not_found_if_not_exist($user_notification, EloquentUserNotification::class);

        $attach_data = [];


        if ($user_notification->has_action && $user_notification->notification_div === NotificationDiv::INVITE_JOIN_SETTING_PARTY) {
//            $data = json_decode($user_notification->data);
            $setting_party_id = $user_notification->foreign_id;
            if ($setting_party_id) {
                $setting_party = EloquentSettingParty::queryModel()->find($setting_party_id);
                if ($setting_party) {
                    $attach_data = [
                        'setting_party' => (new SettingParty($setting_party))->toDetailApiResponse()
                    ];
                }
            }
        }

        if ($user_notification->has_action && $user_notification->notification_div === NotificationDiv::MATCHING_PARTY) {
            $data = json_decode($user_notification->data);
            if ($data->party_id) {
                /** @var EloquentParty $party */
                $party = EloquentParty::queryModel()->find($data->party_id);
                if ($party) {
                    $participant = $party->participants()->where('id', $author->getId())->first();
                    $my_participant = [
                        'channel_id' => $party->channel_id,
                        'uid' => $author->getId(),
                        'party_participant_status' => $participant->pivot->party_participant_status,
                        'agora_token' => $participant->pivot->agora_token,
                        'expire_at' => $participant->pivot->expire_at,
                    ];

                    $party_data = (new Party($party))->toDetailApiResponse();
                    $party_data['my_participant'] = $my_participant;
                    $attach_data = [
                        'party' => $party_data
                    ];
                }
            }
        }

        $user->userNotifications()->updateExistingPivot($user_notification->id, [
            'is_read_detail' => Boolean::YES
        ]);

        return (new UserNotification($user_notification))->toDetailApiResponse($attach_data);
    }

}
