<?php

namespace Src\Domain\Api\Services;

use App\Eloquent\User;
use App\Eloquent\VerificationCode as EloquentVerificationCode;
use App\Mail\VerifyCode\SendVerifyCodeMail;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Mail;
use Src\Domain\Api\Models\VerificationCode\VerificationCode;
use Src\Domain\Api\Models\VerificationCode\VerificationCodeForm;
use Src\Domain\Api\Services\Traits\UserTrait;
use Src\Enum\LoginIdType;
use Src\Exception\APIRuntimeException;
use Src\Utils\TwilioSMS;
use Throwable;

/**
 * Class VerificationCodeService
 * @package Src\Domain\Api\Services
 */
class VerificationCodeService
{

    use UserTrait;

    /**
     * find Or Fail
     *
     * @param string $verify_code
     * @return array
     */
    public function findOrFail(string $verify_code): array
    {
        /** @var EloquentVerificationCode $verification_code */
        $verification_code = $this->verificationCodeQuery()
            ->where('verify_code', $verify_code)
            ->where('created_at', '>=', now()->subMinutes(5))
            ->first();

        return [
            'is_valid' => $verification_code ? true : false
        ];
    }

    /**
     * verificationCodeQuery
     *
     * @return Builder|EloquentVerificationCode
     */
    private function verificationCodeQuery()
    {
        return EloquentVerificationCode::query()->getModel();
    }

    /**
     * Store
     *
     * @param VerificationCodeForm $form
     * @return bool|mixed
     */
    public function store(VerificationCodeForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                $user = User::query()->getModel()
                ->where('login_id', $form->getLoginId())->first();
                if ($user) {
                    logger()->error('User is already exist', $form->toArray());
                    throw new APIRuntimeException('api_errors.verification_code.user_exists');
                }

                $check_code = $this->verificationCodeQuery()
                    ->where('login_id', $form->getLoginId())
                    ->first();
                if ($check_code) {
                    $check_code->delete();
                }

                $verify_code = $this->genNewVerifyCode();

                /** @var EloquentVerificationCode $verification_code */
                $verification_code = $this->verificationCodeQuery()->createOrThrow($form->createAttributes($verify_code));

                // Send email
                if ($form->getLoginIdType() == LoginIdType::EMAIL) {
                    Mail::to($form->getLoginId())->send(new SendVerifyCodeMail([
                        'verify_code' => $verify_code,
                    ]));
                } else { // Send SMS
                    $toNumber = env('COUNTRY_CODE') . ltrim($form->getLoginId(), '0');
                    TwilioSMS::sendSMS($toNumber, $verify_code);
                }

                logger()->info('success create verification_code.', ['verification_codes.id' => $verification_code->id]);
                if (env('APP_DEBUG')) {
                    return (new VerificationCode($verification_code))->toDetailApiResponse();
                } else {
                    return [];
                }
            });
        } catch (Throwable $e) {
            $result = null;
            logger()->error($e);
            throw_if_api_exception($e);
            logger()->info('failed create verification_code.', $form->toArray());
        }
        return $result;
    }

    /**
     * deleteCodeInvalid
     *
     * @return bool|mixed
     */
    public function deleteCodeInvalid()
    {
        $result = false;
        try {
            $result = DB::transaction(function () {
                $verification_codes = $this->verificationCodeQuery()
                    ->where('created_at', '<', now()->subMinutes(5))
                    ->get();
                foreach ($verification_codes as $verification_code) {
                    $verification_code->delete();
                }
            });
        } catch (Throwable $e) {
            logger()->error($e);
            logger()->info('failed delete verification_code.');
        }
        return $result;
    }

}
