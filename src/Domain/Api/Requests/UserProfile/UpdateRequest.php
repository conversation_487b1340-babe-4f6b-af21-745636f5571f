<?php

namespace Src\Domain\Api\Requests\UserProfile;

use Src\Domain\Api\Models\UserProfile\UserProfileForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Base BaseRequest
 *
 * @package Src\Domain\Api\Requests\UserProfile
 */
class UpdateRequest extends FormRequest
{
    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'avatar' => [
                'file',
                'max:' . 10 * 1024
            ],
            'nickname' => [
                'max:100'
            ],
            'birthday' => [
                'string'
            ],
            'occupation' => [
                'max:100'
            ],
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'avatar' => __('models/user_profile.field.avatar_id'),
            'nickname' => __('models/user_profile.field.nickname'),
            'birthday' => __('models/user_profile.field.birthday'),
            'occupation' => __('models/user_profile.field.occupation'),
        ];
    }

    /**
     * @return UserProfileForm
     */
    public function validatedForm(): UserProfileForm
    {
        return new UserProfileForm($this->validated());
    }

}
