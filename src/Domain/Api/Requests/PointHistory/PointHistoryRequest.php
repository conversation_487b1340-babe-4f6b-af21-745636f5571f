<?php

namespace Src\Domain\Api\Requests\PointHistory;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\PointHistory\PointHistoryForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\PointUpdateDiv;

/**
 * Class PointHistoryRequest
 * @package Src\Domain\Api\Requests\UserPoint
 */
class PointHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'point_update_div' => [
                'required',
                Rule::in(PointUpdateDiv::getValues())
            ],
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'point_update_div' => __('models/point_history.field.point_update_div'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return (new PointHistoryForm($this->validated()));
    }
}
