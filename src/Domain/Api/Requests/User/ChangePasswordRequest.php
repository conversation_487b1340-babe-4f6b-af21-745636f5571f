<?php

namespace Src\Domain\Api\Requests\User;

use Src\Domain\Api\Models\User\ChangePasswordForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class ChangePasswordRequest
 * @package Src\Domain\Api\Requests\User
 */
class ChangePasswordRequest extends FormRequest
{
    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'current_password' => 'required',
            'new_password' => [
                'required',
                'min:8',
                'max:45',
                'regex:/(?=.*[a-zA-z])(?=.*\d)[A-Za-z\d]{8,}$/',
                'confirmed'
            ]
        ];
    }

    public function attributes(): array
    {
        return [
            'old_password' => __('models/user.field.password'),
            'new_password' => __('models/user.field.password')
        ];
    }

    /**
     * @return ChangePasswordForm
     */
    public function validatedForm(): ChangePasswordForm
    {
        return new ChangePasswordForm($this->validated());
    }
}
