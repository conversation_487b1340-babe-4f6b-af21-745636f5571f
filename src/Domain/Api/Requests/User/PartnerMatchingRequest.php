<?php

namespace Src\Domain\Api\Requests\User;

use Src\Domain\Api\Models\User\PartnerMatchingForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class PartnerMatchingRequest
 * @package Src\Domain\Api\Requests\User
 */
class PartnerMatchingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'min_age' => [
                'required',
                'numeric',
                'min:18'
            ],
            'max_age' => [
                'required',
            ],
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'min_age' => __('custom.field.min_age'),
            'max_age' => __('custom.field.max_age'),
        ];
    }

    /**
     * inherit
     */
    public function validatedForm()
    {
        return new PartnerMatchingForm($this->validated());
    }
}
