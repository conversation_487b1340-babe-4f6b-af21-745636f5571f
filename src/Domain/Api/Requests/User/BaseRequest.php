<?php

namespace Src\Domain\Api\Requests\User;

use Src\Domain\Api\Models\User\UserForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class UserBlockRequest
 * @package Src\Domain\Api\Requests\User
 */
class BaseRequest extends FormRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [

        ];
    }


    /**
     * base attributes
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'password' => __('models/user.field.password'),
            'login_id' => __('models/user.field.login_id'),
            'gender' => __('models/user.field.gender'),
            'gender_partner' => __('models/user.field.gender_partner'),
            'mobile_platform' => __('models/user.field.mobile_platform'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return new UserForm($this->validated());
    }
}
