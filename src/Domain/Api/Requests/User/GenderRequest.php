<?php

namespace Src\Domain\Api\Requests\User;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\User\GenderForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\Gender;

/**
 * Class GenderRequest
 * @package Src\Domain\Api\Requests\User
 */
class GenderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'gender_party' => [
                Rule::in(Gender::getValues()),
            ],
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'gender_party' => __('models/user_profile.field.gender_party'),
        ];
    }

    /**
     * inherit
     */
    public function validatedForm()
    {
        return new GenderForm($this->validated());
    }
}
