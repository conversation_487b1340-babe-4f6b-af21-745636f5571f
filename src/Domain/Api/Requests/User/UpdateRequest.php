<?php

namespace Src\Domain\Api\Requests\User;

use Illuminate\Validation\Rule;

/**
 * Class UpdateRequest
 * @package Src\Domain\Api\Requests\User
 */
class UpdateRequest extends BaseRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        $user_id = $this->route()->parameter('user_id');
        return array_merge(parent::rules(), [
            'login_id' => [
                Rule::unique('users', 'login_id')->ignore($user_id),
                'login_id',
                'required',
                'string',
                'max:100'
            ],
            'password' => [
                'nullable',
                'string',
                'min:6',
                'max:45'
            ],
        ]);
    }
}
