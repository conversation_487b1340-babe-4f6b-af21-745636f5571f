<?php

namespace Src\Domain\Api\Requests\User;

use Src\Domain\Api\Models\User\FCMTokenForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class UpdateFCMTokenRequest
 * @package Src\Domain\Api\Requests\User
 */
class UpdateFCMTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fcm_token' => [
                'required',
                'string'
            ],

        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'fcm_token' => __('models/user.field.fcm_token')
        ];
    }

    /**
     * inherit
     */
    public function validatedForm()
    {
        return new FCMTokenForm($this->validated());
    }
}
