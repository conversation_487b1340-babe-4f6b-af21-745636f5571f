<?php

namespace Src\Domain\Api\Requests\User;

use Src\Domain\Api\Models\User\CheckExistLoginIdForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class CheckExistLoginIdRequest
 * @package Src\Domain\Api\Requests\User
 */
class CheckExistLoginIdRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'login_id' => [
                'required',
            ],

        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'login_id' => __('models/user.field.login_id')
        ];
    }

    /**
     * inherit
     */
    public function validatedForm()
    {
        return new CheckExistLoginIdForm($this->validated());
    }
}
