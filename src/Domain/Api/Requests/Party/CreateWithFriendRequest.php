<?php

namespace Src\Domain\Api\Requests\Party;

use Src\Domain\Api\Models\Party\PartyWithFriendForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Base CreateWithFriendRequest
 *
 * @package_partner Src\Domain\Api\Requests\PartyWithFriend
 */
class CreateWithFriendRequest extends FormRequest
{

    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'setting_party_id' => [
                'required'
            ],
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'setting_party_id' => __('models/party.field.setting_party_id'),
        ];
    }

    /**
     * @return PartyWithFriendForm
     */
    public function validatedForm(): PartyWithFriendForm
    {
        return new PartyWithFriendForm($this->validated());
    }

}
