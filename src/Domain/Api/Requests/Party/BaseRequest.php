<?php

namespace Src\Domain\Api\Requests\Party;

use Src\Domain\Api\Models\Party\PartyForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Base BaseRequest
 *
 * @package Src\Domain\Api\Requests\Party
 */
class BaseRequest extends FormRequest
{

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'setting_party_id' => __('models/party.field.setting_party_id'),
        ];
    }

    /**
     * @return PartyForm
     */
    public function validatedForm(): PartyForm
    {
        return new PartyForm($this->validated());
    }

    /**
     * Base rules
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'setting_party_id' => [
                'required'
            ],
        ];
    }

}
