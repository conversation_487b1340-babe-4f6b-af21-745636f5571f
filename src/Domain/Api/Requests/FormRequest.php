<?php

namespace Src\Domain\Api\Requests;

use App\Rules\PhoneNumber;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest as Base;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Src\Enum\LoginIdType;

/**
 * Class FormRequest
 * @package Src\Domain\Admin\Requests
 */
abstract class FormRequest extends Base
{
    /**
     * @return mixed
     */
    abstract public function validatedForm();

    /**
     * Failed Validation
     *
     * @param Validator $validator
     */
    protected function failedValidation(Validator $validator)
    {
        throw (new ValidationException($validator));
//        $errors = (new ValidationException($validator))->errors();
//        throw new HttpResponseException(json_response(ResultCode::ERROR_INPUT, null, $errors));
    }

    /**
     * Get rules for login_id field
     *
     * @param bool $isCheckExist
     * @return array
     */
    protected function getRuleForLoginId(bool $isCheckExist = false)
    {
        $rules = [
            'required',
            'max:100'
        ];

        if ($isCheckExist) {
            $rules[] = Rule::unique('users', 'login_id');
        }

        if ($this->input('login_id_type') === LoginIdType::EMAIL) {
            $rules = array_merge($rules, [
                'string',
                'email'
            ]);
        } elseif ($this->input('login_id_type') === LoginIdType::PHONE) {
            $rules = array_merge($rules, [
                new PhoneNumber()
            ]);
        }

        return $rules;
    }
}
