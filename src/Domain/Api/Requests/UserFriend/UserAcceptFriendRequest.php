<?php

namespace Src\Domain\Api\Requests\UserFriend;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserFriend\UserAcceptFriendForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class UserAcceptFriendRequest
 * @package Src\Domain\Api\Requests\UserFriend
 */
class UserAcceptFriendRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'invite_token' => [
                'required',
                'string',
                Rule::exists('friend_transactions', 'invite_token')
            ],
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'invite_token' => __('models/user_friend.field.invite_token'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return new UserAcceptFriendForm($this->validated());
    }
}
