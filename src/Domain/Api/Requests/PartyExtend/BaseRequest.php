<?php

namespace Src\Domain\Api\Requests\PartyExtend;

use Src\Domain\Api\Models\PartyExtend\PartyExtendForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Base BaseRequest
 *
 * @package Src\Domain\Api\Requests\PartyExtend
 */
class BaseRequest extends FormRequest
{

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'party_id' => __('models/party_extend.field.party_id')
        ];
    }

    /**
     * @return PartyExtendForm
     */
    public function validatedForm(): PartyExtendForm
    {
        return new PartyExtendForm($this->validated());
    }

    /**
     * Base rules
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'party_id' => [
                'required'
            ]
        ];
    }

}
