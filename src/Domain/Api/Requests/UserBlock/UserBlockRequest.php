<?php


namespace Src\Domain\Api\Requests\UserBlock;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserBlock\UserBlockForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class UserBlockRequest
 * @package Src\Domain\Api\Requests\UserBlock
 */
class UserBlockRequest extends FormRequest
{
    /**
     * authorize
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * rules
     *
     * @return array[]
     */
    public function rules()
    {
        return [
            'block_user_ids' => [
                'required',
                'array',
//                Rule::exists('users', 'id')
            ],
        ];
    }

    /**
     * base attributes
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'block_user_ids' => __('models/user_block.field.block_user_ids'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return new UserBlockForm($this->validated());
    }
}
