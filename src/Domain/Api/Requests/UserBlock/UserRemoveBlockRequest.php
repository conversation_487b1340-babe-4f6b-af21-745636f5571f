<?php


namespace Src\Domain\Api\Requests\UserBlock;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserBlock\UserBlockForm;
use Src\Domain\Api\Models\UserBlock\UserRemoveBlockForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class UserRemoveBlockRequest
 * @package Src\Domain\Api\Requests\UserBlock
 */
class UserRemoveBlockRequest extends FormRequest
{
    /**
     * authorize
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * rules
     *
     * @return array[]
     */
    public function rules()
    {
        return [
            'block_user_id' => [
                'required',
                Rule::exists('users', 'id')
            ],
        ];
    }

    /**
     * base attributes
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'block_user_id' => __('models/user_block.field.block_user_id'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return new UserRemoveBlockForm($this->validated());
    }
}
