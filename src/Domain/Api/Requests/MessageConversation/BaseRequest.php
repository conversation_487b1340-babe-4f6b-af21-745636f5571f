<?php

namespace Src\Domain\Api\Requests\MessageConversation;

use Src\Domain\Api\Models\MessageConversation\MessageConversationForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Base BaseRequest
 *
 * @package Src\Domain\Api\Requests\MessageConversation
 */
class BaseRequest extends FormRequest
{

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'creator_id' => __('models/message_conversation.field.creator_id'),
            'channel_id' => __('models/message_conversation.field.channel_id')
        ];
    }

    /**
     * @return MessageConversationForm
     */
    public function validatedForm(): MessageConversationForm
    {
        return new MessageConversationForm($this->validated());
    }

    /**
     * Base rules
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'creator_id' => [
                'required'
            ],
            'channel_id' => [
                'required',
                'max:45'
            ]
        ];
    }

}
