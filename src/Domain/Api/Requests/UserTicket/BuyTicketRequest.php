<?php


namespace Src\Domain\Api\Requests\UserTicket;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserTicket\BuyTicketForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\MobilePlatform;

/**
 * Class BuyTicketRequest
 *
 * @package Src\Domain\Api\Requests\UserTicket
 */
class BuyTicketRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'mobile_platform' => [
                'required',
                Rule::in(MobilePlatform::IOS, MobilePlatform::ANDROID)
            ],
            'receipt_data' => [
                'required_if:mobile_platform,==,' . MobilePlatform::IOS . ''
            ],
            'party_id' => [
                'nullable',
                Rule::exists('parties', 'id')
            ],
            'package_name' => [
                'required_if:mobile_platform,==,' . MobilePlatform::ANDROID . ''
            ],
            'product_id' => [
                'required_if:mobile_platform,==,' . MobilePlatform::ANDROID . ''
            ],
            'token' => [
                'required_if:mobile_platform,==,' . MobilePlatform::ANDROID . ''
            ]
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'receipt_data' => __('models/user_ticket.field.receipt_data'),
            'ticket_plan_id' => __('models/user_ticket.field.ticket_plan_id'),
            'party_id' => __('models/parties.field.party_id'),
            'package_name' => __('models/user_ticket.field.package_name'),
            'product_id' => __('models/user_ticket.field.product_id'),
            'token' => __('models/user_ticket.field.token'),

        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return (new BuyTicketForm($this->validated()));
    }
}
