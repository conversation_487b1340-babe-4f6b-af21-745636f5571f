<?php

namespace Src\Domain\Api\Requests\Auth;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\Auth\Form\LoginSocialForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\MobilePlatform;

/**
 * Class LoginSocialRequest
 * @package Src\Domain\Api\Requests\Auth
 */
class LoginSocialRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'social_token' => [
                'required',
                'string',
            ],
            'fcm_token' => [
                'nullable',
                'string'
            ],
            'mobile_platform' => [
                'required',
                Rule::in(MobilePlatform::getValues())
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'social_token' => __('models/user.field.social_token'),
            'fcm_token' => __('models/user.field.fcm_token'),
            'mobile_platform' => __('models/user.field.mobile_platform')
        ];
    }

    /**
     * @return LoginSocialForm
     */
    public function validatedForm(): LoginSocialForm
    {
        return new LoginSocialForm($this->validated());
    }
}
