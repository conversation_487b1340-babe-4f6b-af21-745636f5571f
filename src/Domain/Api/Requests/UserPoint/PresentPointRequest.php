<?php

namespace Src\Domain\Api\Requests\UserPoint;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserPoint\PresentPointForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\PresentPointDiv;

/**
 * Class PresentPointRequest
 * @package Src\Domain\Api\Requests\UserPoint
 */
class PresentPointRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'target_user_id' => [
                'required',
                Rule::exists('users', 'id')
            ],
            'present_point_div' => [
                'required',
                Rule::in(PresentPointDiv::getValues())
            ],
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'target_user_id' => __('models/userPoint.field.target_user_id'),
            'present_point_div' => __('models/userPoint.field.present_point_div'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return (new PresentPointForm($this->validated()));
    }
}
