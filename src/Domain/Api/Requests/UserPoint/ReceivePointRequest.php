<?php

namespace Src\Domain\Api\Requests\UserPoint;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\UserPoint\ReceivePointForm;
use Src\Domain\Api\Requests\FormRequest;

/**
 * Class ReceivePointRequest
 * @package Src\Domain\Api\Requests\UserPoint
 */
class ReceivePointRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'point_token' => [
                'required',
                'string',
                Rule::exists('point_transactions', 'point_token')
            ]
        ];
    }

    /**
     * base attributes
     */
    public function attributes()
    {
        return [
            'point_token' => __('models/point_transaction.field.point_token'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return (new ReceivePointForm($this->validated()));
    }
}
