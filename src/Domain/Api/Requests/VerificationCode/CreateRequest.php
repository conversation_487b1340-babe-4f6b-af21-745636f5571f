<?php

namespace Src\Domain\Api\Requests\VerificationCode;

use Illuminate\Validation\Rule;
use Src\Domain\Api\Models\VerificationCode\VerificationCodeForm;
use Src\Domain\Api\Requests\FormRequest;
use Src\Enum\LoginIdType;

/**
 * Base CreateRequest
 *
 * @package Src\Domain\Api\Requests\VerificationCode
 */
class CreateRequest extends FormRequest
{

    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'login_id' => $this->getRuleForLoginId(),
            'login_id_type' => [
                'required',
                Rule::in(LoginIdType::getValues())
            ],
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'login_id' => __('models/verification_code.field.login_id'),
            'login_id_type' => __('models/verification_code.field.login_id_type')
        ];
    }

    /**
     * @return VerificationCodeForm
     */
    public function validatedForm(): VerificationCodeForm
    {
        return new VerificationCodeForm($this->validated());
    }

}
