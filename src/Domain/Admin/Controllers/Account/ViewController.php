<?php

namespace Src\Domain\Admin\Controllers\Account;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\AccountService;
use Src\Exception\AuthenticationException;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\Account
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param AccountService $service
     * @return Factory|View
     */
    public function index(Request $request, AccountService $service){
        $keyword = $request->input('keyword');
        $account = $service->fetchAll($keyword);
        return view('admin.account.index', compact('account', 'keyword'));
    }

    /**
     * show
     *
     * @param AccountService $service
     * @param int $account_id
     * @return Factory|View
     */
    public function show(AccountService $service, int $account_id) {
        $account = $service->findOrFail($account_id);
        return view('admin.account.show', compact('account'));
    }
}
