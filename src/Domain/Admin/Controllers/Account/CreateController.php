<?php

namespace Src\Domain\Admin\Controllers\Account;

use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Models\Account\AccountForm;
use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Admin\Requests\Account\CreateRequest;
use Src\Domain\Admin\Services\AccountService;
use Illuminate\Http\RedirectResponse;

/**
 * Class CreateController
 *
 * @package Src\Domain\Admin\Controllers\Account
 */
class CreateController extends Controller
{
    /**
     * create
     *
     * @return Factory|View
     */
    public function create()
    {
        $form = new AccountForm(old());
        return view('admin.account.create', compact('form'));
    }

    /**
     * store
     *
     * @param CreateRequest $request
     * @param AccountService $service
     * @return RedirectResponse
     */
    public function store(CreateRequest $request, AccountService $service)
    {
        if(!$service->store($request->validatedForm())){
            flash()->error(__('flash.store.failed'));
            return redirect()->route('admin.account.create')->withInput();
        }

        flash()->info(__('flash.store.succeeded'));
        return redirect()->route('admin.account.index');
    }
}
