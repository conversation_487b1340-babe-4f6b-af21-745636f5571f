<?php

namespace Src\Domain\Admin\Controllers\Account;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Requests\Account\EditRequest;
use Src\Domain\Admin\Services\AccountService;
use Src\Exception\AuthenticationException;

/**
 * Class EditController
 *
 * @package Src\Domain\Admin\Controllers\Account
 */
class EditController extends Controller
{
    /**
     * edit
     *
     * @param AccountService $service
     * @param int $account_id
     * @return Factory|View
     */
    public function edit(AccountService $service, int $account_id)
    {
        $detail = $service->findOrFail($account_id);
        return view('admin.account.edit', compact('detail'));
    }

    /**
     * Update
     *
     * @param EditRequest $request
     * @param AccountService $service
     * @param int $account_id
     * @return RedirectResponse
     */
    public function update(EditRequest $request, AccountService $service, int $account_id): RedirectResponse
    {
        if (!$service->update($account_id, $request->validatedForm())) {
            flash()->error(__('flash.update.failed'));
            return redirect()->route('admin.profile.edit', $account_id)->withInput();
        }
        flash()->info(__('flash.update.succeeded'));
        return redirect()->route('admin.account.show', $account_id);
    }
}
