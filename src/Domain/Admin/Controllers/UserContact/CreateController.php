<?php

namespace Src\Domain\Admin\Controllers\UserContact;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Models\UserContact\UserContactForm;
use Src\Domain\Admin\Requests\UserContact\CreateRequest;
use Src\Domain\Admin\Services\UserContactService;

/**
 * Class CreateController
 * @package Src\Domain\Admin\Controllers\UserContact
 */
class CreateController extends Controller
{
    /**
     * create
     *
     * @return Factory|View
     */
    public function create()
    {
        $form = new UserContactForm(old());
        return view('admin.user_contact.create', compact('form'));
    }

    /**
     * store
     *
     * @param CreateRequest $request
     * @param UserContactService $user_contact_service
     * @return RedirectResponse
     */
    public function store(CreateRequest $request, UserContactService $user_contact_service): RedirectResponse
    {
        if (!$user_contact_service->store($request->validatedForm())) {
            flash()->error(__('flash.store.failed'));
            return redirect()->route('admin.user_contact.create')->withInput();
        }
        flash()->info(__('flash.store.succeeded'));
        return redirect()->route('admin.user_contact.index');
    }
}
