<?php

namespace Src\Domain\Admin\Controllers\UserContact;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserContactService;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\UserContact
 */
class ViewController extends Controller
{

    /**
     * index
     *
     * @param Request $request
     * @param UserContactService $user_contact_service
     * @return Factory|View
     */
    public function index(Request $request, UserContactService $user_contact_service)
    {
        $userContact = $user_contact_service->fetchPage();
        return view('admin.user_contact.index', compact('userContact'));
    }

    /**
     * show
     *
     * @param UserContactService $user_contact_service
     * @param int $user_contact_id
     * @return Factory|View
     */
    public function show(UserContactService $user_contact_service, int $user_contact_id)
    {
        $user_contact = $user_contact_service->findOrFail($user_contact_id);
        return view('admin.user_contact.show', compact('user_contact'));
    }
}
