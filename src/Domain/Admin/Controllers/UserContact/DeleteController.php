<?php

namespace Src\Domain\Admin\Controllers\UserContact;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserContactService;

/**
 * Class DeleteController
 * @package Src\Domain\Admin\Controllers\UserContact
 */
class DeleteController extends Controller
{
    /**
     * delete
     *
     * @param UserContactService $user_contact_service
     * @param int $user_contact_id
     * @return JsonResponse
     */
    public function delete(UserContactService $user_contact_service, int $user_contact_id): JsonResponse
    {
        if (!$user_contact_service->delete($user_contact_id)) {
            return response()->json(['errors' => [[__('flash.delete.failed')]]], 500);
        }

        return response()->json();
    }
}
