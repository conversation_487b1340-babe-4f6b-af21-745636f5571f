<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Src\Domain\Admin\Models\Auth\AuthorInterface;
use Src\Exception\AuthenticationException;

/**
 * Class Controller
 * @package Src\Domain\Admin\Controller
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs;

    /**
     * @return AuthorInterface
     * @throws AuthenticationException
     */
    protected function author(): AuthorInterface
    {
        $author = request()->get('author');
        if (null === $author) {
            throw new AuthenticationException('Unauthorized.');
        }
        return $author;
    }
}
