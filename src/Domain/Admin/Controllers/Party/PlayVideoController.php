<?php

namespace Src\Domain\Admin\Controllers\Party;

use App\Eloquent\PartyHistory;
use Src\Domain\Admin\Controllers\Controller;
use Src\Utils\Agora\Record\AwsConvertManager;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

/**
 * Class PlayVideoController
 *
 * @package Src\Domain\Admin\Controllers\Party
 */
class PlayVideoController extends Controller
{
    /**
     * index
     *
     * @param int $party_history_id
     * @return Factory|RedirectResponse|View
     */
    public function index(int $party_history_id)
    {
        /** @var PartyHistory $party_history */
        $party_history = PartyHistory::query()->findOrFail($party_history_id);

        if(!isset($party_history->partyHistoryRecord)) {
            flash()->error(__('api_errors.party.party_no_record.message'));
            return redirect()->route('admin.party.index');
        }

        if(!isset($party_history->partyHistoryRecord->recordFile)) {
            flash()->error(__('api_errors.party.party_no_file_record.message'));
            return redirect()->route('admin.party.index');
        }

        $url = $party_history->partyHistoryRecord->recordFile->file_url;

        $url = AwsConvertManager::getPresignedUrl($url);

        return view('admin.party.video.index', compact('url'));
    }

}
