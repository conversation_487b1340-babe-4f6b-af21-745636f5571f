<?php

namespace Src\Domain\Admin\Controllers\User;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserService;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\User
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param Request $request
     * @param UserService $service
     * @return Factory|View
     */
    public function index(Request $request, UserService $service)
    {
        $keyword = $request->input('keyword');
        $user = $service->fetchPage($keyword);
        return view('admin.user.index', compact('user', 'keyword'));
    }

    /**
     * show
     *
     * @param UserService $service
     * @param int|null $index
     * @param int $user_id
     * @return Factory|View
     */
    public function show(UserService $service, int $user_id, ?int $index)
    {
        $user = $service->findOrFail($user_id);
        return view('admin.user.show', compact('user','index'));
    }
}
