<?php

namespace Src\Domain\Admin\Controllers\User\UserInformDetail;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Services\UserFriendService;
use Src\Enum\ResultCode;

/**
 * Class UserFriendController
 *
 * @package Src\Domain\Admin\Controllers\User\UserInformDetail
 */
class UserFriendController
{
    /**
     * index
     *
     * @param UserFriendService $service
     * @param string $user_id
     * @return JsonResponse
     */
    public function index(UserFriendService $service, string $user_id): JsonResponse
    {
        $user = $service->fetch($user_id);
        return json_response(ResultCode::SUCCESS, $user);
    }

}
