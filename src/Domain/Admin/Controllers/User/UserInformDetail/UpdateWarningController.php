<?php

namespace Src\Domain\Admin\Controllers\User\UserInformDetail;

use Illuminate\Http\RedirectResponse;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserService;

/**
 * Class UpdateWarningController
 *
 * @package Src\Domain\Admin\Controllers\User\UserInformDetail
 */
class UpdateWarningController extends Controller
{
    /**
     * store
     *
     * @param UserService $service
     * @param int $user_id
     * @return RedirectResponse
     */
    public function store(UserService $service, int $user_id): RedirectResponse
    {
        $is_warning = (int) request()->query('is_warning', false);
        $feedback_id =  (int) request()->query('feedback_id', null);
        logger()->debug('is_warning',['is_warning' => $is_warning]);
        logger()->debug('feedback_id',['feedback_id' => $feedback_id]);

        $result = $service->updateWarning($is_warning, $feedback_id, $user_id);
        if(!$result){
            flash()->error(__('flash.update.failed'));
            return redirect()->route('admin.user.show', [$user_id, 0])->withInput();
        }
        flash()->info(__('flash.upload.succeeded'));
        return redirect()->route('admin.user.show', [$user_id, 0]);
    }

}
