<?php

namespace Src\Domain\Admin\Controllers\User\UserInformDetail;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Services\UserService;
use Src\Enum\ResultCode;

/**
 * Class UserPointController
 *
 * @package Src\Domain\Admin\Controllers\User\UserInformDetail
 */
class UserPointController
{
    /**
     * get buy Point
     *
     * @param UserService $service
     * @param int $user_id
     * @return JsonResponse
     */
    public function getBuyPoint(UserService $service, int $user_id): JsonResponse
    {
        $buy_point_history = $service->getBuyPointHistory($user_id);
        return json_response(ResultCode::SUCCESS, $buy_point_history);
    }

    /**
     * present Point
     *
     * @param UserService $service
     * @param int $user_id
     * @return JsonResponse
     */
    public function getPresentPoint(UserService $service, int $user_id): JsonResponse
    {
        $present_point_history = $service->getPresentPointHistory($user_id);
        return json_response(ResultCode::SUCCESS, $present_point_history);
    }

    /**
     * getReceivePoint
     *
     * @param UserService $service
     * @param int $user_id
     * @return JsonResponse
     */
    public function getReceivePoint(UserService $service, int $user_id): JsonResponse
    {
        $receive_point_history = $service->getReceivePointHistory($user_id);
        return json_response(ResultCode::SUCCESS, $receive_point_history);
    }
}
