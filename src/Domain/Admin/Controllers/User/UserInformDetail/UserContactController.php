<?php

namespace Src\Domain\Admin\Controllers\User\UserInformDetail;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Src\Domain\Admin\Services\UserContactService;
use Src\Domain\Admin\Services\UserService;
use Src\Enum\ResultCode;

/**
 * Class UserContactController
 *
 * @package Src\Domain\Admin\Controllers\User\UserInformDetail
 */
class UserContactController
{
    /**
     * index
     *
     * @param UserService $service
     * @param int $user_id
     * @return JsonResponse
     */
    public function index(UserService $service, int $user_id): JsonResponse
    {
        $user_contact = $service->getUserContact($user_id);
        return json_response(ResultCode::SUCCESS, $user_contact);
    }

    /**
     * show
     *
     * @param UserContactService $service
     * @return Factory|View
     */
    public function show(UserContactService $service)
    {
        $user_id = request()->query('user_id', null);
        $gender = request()->query('gender', null);
        $contact_id = request()->query('contact_id', null);

        $user_contact = $service->getUserContactDetail($contact_id);
        return view('admin.user.contact.show', compact('user_contact', 'user_id', 'gender'));
    }
}
