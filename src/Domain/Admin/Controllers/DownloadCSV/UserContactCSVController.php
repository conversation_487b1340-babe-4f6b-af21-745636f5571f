<?php

namespace Src\Domain\Admin\Controllers\DownloadCSV;

use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\DownloadCSVService;
use Src\Domain\Admin\Services\UserContactService;

/**
 * Class UserContactCSVController
 *
 * @package Src\Domain\Admin\Controllers\DownloadCSV
 */
class UserContactCSVController extends Controller
{
    /**
     * download
     *
     * @param UserContactService $user_contact_service
     * @param DownloadCSVService $download_csv_service
     * @return mixed
     */
    public function download(UserContactService $user_contact_service, DownloadCSVService $download_csv_service)
    {
        $user_contact_csv = $user_contact_service->getUserContactCSV();
        $download_csv = $download_csv_service->downloadUserContactCSV($user_contact_csv);
        return $download_csv;
    }

}
