<?php

namespace Src\Domain\Admin\Controllers\GuestContact;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\GuestContactService;

class ViewController extends Controller
{
    /**
     * Index
     *
     * @param GuestContactService $service
     * @return Factory|View
     */
    public function index(GuestContactService $service)
    {
        $guestContact = $service->fetchPage();

        return view('admin.guest_contact.index', compact('guestContact'));
    }

    /**
     * Show
     *
     * @param GuestContactService $service
     * @param int $guest_contact_id
     * @return Factory|View
     */
    public function show(GuestContactService $service, int $guest_contact_id)
    {
        $guest_contact = $service->findOrFail($guest_contact_id);

        return view('admin.guest_contact.detail', compact('guest_contact'));
    }
}
