<?php

namespace Src\Domain\Admin\Controllers\PointPlan;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\PointPlanService;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\PointPlan
 */
class ViewController extends Controller
{

    /**
     * index
     *
     * @param Request $request
     * @param PointPlanService $point_plan_service
     * @return Factory|View
     */
    public function index(Request $request, PointPlanService $point_plan_service)
    {
        $paginator = $point_plan_service->fetchPage();
        return view('admin.point_plan.index', compact('paginator'));
    }


}
