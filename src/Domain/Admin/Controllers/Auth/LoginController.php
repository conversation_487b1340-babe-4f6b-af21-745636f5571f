<?php

namespace Src\Domain\Admin\Controllers\Auth;

use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Src\Domain\Admin\Models\Auth\Form\LoginForm;
use Src\Domain\Admin\Requests\Auth\LoginRequest;

/**
 * Class LoginController
 * @package Src\Domain\Admin\Controller\Auth
 */
class LoginController extends Controller
{
    /**
     * @return Factory|View
     */
    public function show()
    {
        $form = new LoginForm(old());
        return view('admin.auth.login', compact('form'));
    }

    /**
     * Login
     *
     * @param LoginRequest $request
     * @return ResponseFactory|Response
     */
    public function login(LoginRequest $request)
    {
        $login_form = $request->validatedForm();
        $credentials = [
            'login_id' => $login_form->getLoginId(),
            'password' => $login_form->getPassword()
        ];

        $is_remember = (bool)$request->get('remember', true);
        if (auth('admin')->attempt($credentials, $is_remember)) {
            return redirect()->route('admin.home.index');
        }
        flash(__('auth.failed'))->error();
        return redirect()->back()->withInput();
    }

    /**
     * logout
     *
     * @return RedirectResponse
     */
    public function logout(): RedirectResponse
    {
        Auth::logout();
        return redirect()->route('admin.auth.show');
    }
}
