<?php

namespace Src\Domain\Admin\Controllers\Notification;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\NotificationService;

/**
 * Class ViewController
 *
 * @package Src\Domain\Admin\Controllers\SendNotification
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param NotificationService $service
     * @return Factory|View
     */
    public function index(NotificationService $service)
    {
        $notification = $service->fetchPage();
        return view('admin.notification.index', compact('notification'));
    }

    /**
     * show
     *
     * @param NotificationService $service
     * @param int $notification_id
     * @return Factory|View
     */
    public function show(NotificationService $service, int $notification_id)
    {
        $notification = $service->findOrFail($notification_id);
        return view('admin.notification.show', compact('notification'));
    }
}
