<?php

namespace Src\Domain\Admin\Controllers\Notification;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Models\Notification\NotificationForm;
use Src\Domain\Admin\Requests\Notification\BaseRequest;
use Src\Domain\Admin\Services\NotificationService;
use Src\Exception\AuthenticationException;

/**
 * Class CreateController
 *
 * @package Src\Domain\Admin\Controllers\SendNotification
 */
class CreateController extends Controller
{
    /**
     * create
     *
     * @return Factory|View
     */
    public function create()
    {
        $form = new NotificationForm(old());
        return view('admin.notification.create', compact('form'));
    }

    /**
     * store
     *
     * @param BaseRequest $request
     * @param NotificationService $service
     * @return RedirectResponse
     * @throws AuthenticationException
     */
    public function store(BaseRequest $request, NotificationService $service): RedirectResponse
    {
        $author = $this->author();
        if (!$service->store($request->validatedForm(), $author->getId())) {
            flash()->error(__('flash.store.failed'));
            return redirect()->route('admin.notification.create')->withInput();
        }
        flash()->info(__('flash.store.succeeded'));
        return redirect()->route('admin.notification.index');
    }
}
