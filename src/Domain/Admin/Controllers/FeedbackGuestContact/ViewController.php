<?php

namespace Src\Domain\Admin\Controllers\FeedbackGuestContact;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\FeedbackGuestContactService;

class ViewController extends Controller
{
    /**
     * @param FeedbackGuestContactService $service
     * @param int $feedback_id
     * @return Factory|View
     */
    public function show(FeedbackGuestContactService $service, int $feedback_id)
    {
        $feedback = $service->findOrFail($feedback_id);

        return view('admin.feedback_guest_contact.detail', compact('feedback'));
    }
}
