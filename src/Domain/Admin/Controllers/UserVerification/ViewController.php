<?php

namespace Src\Domain\Admin\Controllers\UserVerification;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserVerificationService;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\UserVerification
 */
class ViewController extends Controller
{
    /**
     * index
     *
     * @param UserVerificationService $service
     * @return Factory|View
     */
    public function index(UserVerificationService $service)
    {
        $user_verification = $service->fetchPage();
        return view('admin.user_verification.index', compact('user_verification'));
    }
}
