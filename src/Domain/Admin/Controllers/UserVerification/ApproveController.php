<?php

namespace Src\Domain\Admin\Controllers\UserVerification;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserVerificationService;

/**
 * Class ApproveController
 *
 * @package Src\Domain\Admin\Controllers\UserVerification
 */
class ApproveController extends Controller
{
    /**
     * @param Request $request
     * @param UserVerificationService $service
     * @return RedirectResponse
     */
    public function approve(Request $request, UserVerificationService $service)
    {
        if ($request->user_id != 0) {
            $user_ids = explode(',', $request->user_id);
        } else {
            $user_ids = 0;
        }
        if (!$service->approve($user_ids)) {
            return redirect()->route('admin.user_verification.index');
        }
        flash()->info(__('flash.update.succeeded'));
        return redirect()->route('admin.user_verification.index');
    }
}
