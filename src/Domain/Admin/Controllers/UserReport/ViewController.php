<?php

namespace Src\Domain\Admin\Controllers\UserReport;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserReportService;

/**
 * Class ViewController
 * @package Src\Domain\Admin\Controllers\UserReport
 */
class ViewController extends Controller
{

    /**
     * index
     *
     * @param Request $request
     * @param UserReportService $user_report_service
     * @return Factory|View
     */
    public function index(Request $request, UserReportService $user_report_service)
    {
        $userReport = $user_report_service->fetchPage();
        return view('admin.user_report.index', compact( 'userReport'));
    }

    /**
     * show
     *
     * @param UserReportService $user_report_service
     * @param int $user_report_id
     * @return Factory|View
     */
    public function show(UserReportService $user_report_service, int $user_report_id)
    {
        $user_report = $user_report_service->findOrFail($user_report_id);
        return view('admin.user_report.show', compact('user_report'));
    }
}
