<?php

namespace Src\Domain\Admin\Controllers\UserReport;

use Illuminate\Http\JsonResponse;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Services\UserReportService;

/**
 * Class DeleteController
 * @package Src\Domain\Admin\Controllers\UserReport
 */
class DeleteController extends Controller
{
    /**
     * delete
     *
     * @param UserReportService $user_report_service
     * @param int $user_report_id
     * @return JsonResponse
     */
    public function delete(UserReportService $user_report_service, int $user_report_id): JsonResponse
    {
        if (!$user_report_service->delete($user_report_id)) {
            return response()->json(['errors' => [[__('flash.delete.failed')]]], 500);
        }

        return response()->json();
    }
}
