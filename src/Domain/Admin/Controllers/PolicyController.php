<?php

namespace Src\Domain\Admin\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Src\Domain\Admin\Models\Privacy\PrivacySearchForm;
use Src\Domain\Admin\Requests\Privacy\CreateRequest;
use Src\Domain\Admin\Requests\Privacy\UpdateRequest;
use Src\Domain\Admin\Services\PrivacyService;

/**
 * Class PrivacyController
 * @package Src\Domain\Admin\Controllers
 */
class PrivacyController extends Controller
{
    /**
     * @var PrivacyService
     */
    protected PrivacyService $service;

    /**
     * Constructor to initialize the Privacy service.
     *
     * @param PrivacyService $service
     */
    public function __construct(PrivacyService $service)
    {
        $this->service = $service;
    }

    /**
     * Index
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $form = new PrivacySearchForm($request->all());
        $privacies = $this->service->fetchAll($form);
        return Inertia::render('Privacy/IndexPage', ['privacies' => $privacies]);
    }

    /**
     * Create
     *
     * @return Response
     */
    public function create(): Response
    {
        return Inertia::render('Privacy/CreatePage');
    }

    /**
     * Store
     *
     * @param CreateRequest $request
     * @return RedirectResponse
     */
    public function store(CreateRequest $request): RedirectResponse
    {
        if ($this->service->store($request->validatedForm())) {
            return to_route('admin.privacy.index')->with('success', __('flash.store.succeeded'));
        }
        return back()->with('error', __('flash.store.failed'));
    }

    /**
     * Show
     *
     * @param int $privacy_id
     * @return Response
     */
    public function show(int $privacy_id): Response
    {
        $privacy = $this->service->findOrFail($privacy_id);
        return Inertia::render('Privacy/DetailPage', ['privacy' => $privacy]);
    }

    /**
     * Edit
     *
     * @param int $privacy_id
     * @return Response
     */
    public function edit(int $privacy_id): Response
    {
        $privacy = $this->service->findOrFail($privacy_id);
        return Inertia::render('Privacy/EditPage', ['privacy' => $privacy]);
    }

    /**
     * Update
     *
     * @param int $privacy_id
     * @param UpdateRequest $request
     * @return RedirectResponse
     */
    public function update(int $privacy_id, UpdateRequest $request): RedirectResponse
    {
        $result = $this->service->update($privacy_id, $request->validatedForm());
        if ($result) {
            return to_route('admin.privacy.show', $privacy_id)->with('success', __('flash.update.succeeded'));
        }
        return back()->with('error', __('flash.update.failed'));
    }

    /**
     * Delete
     *
     * @param int $privacy_id
     * @return RedirectResponse
     */
    public function delete(int $privacy_id): RedirectResponse
    {
        $result = $this->service->delete($privacy_id);
        if ($result) {
            return to_route('admin.privacy.index')->with('success', __('flash.delete.succeeded'));
        }
        return back()->with('error', __('flash.delete.failed'));
    }
}
