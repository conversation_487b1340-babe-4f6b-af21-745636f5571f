<?php

namespace Src\Domain\Admin\Controllers\FeedbackReport;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\Admin\Services\FeedbackReportService;

/**
 * Class ViewController
 *
 * @package Src\Domain\Admin\Controllers\FeedbackReport
 */
class ViewController
{
    /**
     * show
     *
     * @param FeedbackReportService $feedback_report_service
     * @param int $feedback_id
     * @return Factory|View
     */
    public function show(FeedbackReportService $feedback_report_service, int $feedback_id)
    {
        $user_id = request()->query('user_id', null);
        $gender = request()->query('gender', null);

        $feedback_report = $feedback_report_service->findOrFail($feedback_id);

        return view('admin.feedback_report.show', compact('feedback_report', 'user_id', 'gender'));
    }

}
