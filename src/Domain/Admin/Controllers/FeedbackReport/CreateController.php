<?php

namespace Src\Domain\Admin\Controllers\FeedbackReport;

use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Src\Domain\Admin\Controllers\Controller;
use Src\Domain\Admin\Requests\FeedbackReport\CreateRequest;
use Src\Domain\Admin\Services\FeedbackReportService;
use Src\Domain\Admin\Services\UserReportService;
use Src\Enum\TabUserIndex;

/**
 * Class CreateController
 *
 * @package Src\Domain\Admin\Controllers\FeedbackReport
 */
class CreateController extends Controller
{
    /**
     * @param UserReportService $user_report_service
     * @return Factory|View
     */
    public function create(UserReportService $user_report_service)
    {
        $user_id = request()->query('user_id', null);
        $report_id = request()->query('report_id', null);
        $gender = request()->query('gender', null);

        $user_report = $user_report_service->getUserReportDetail($report_id);

        return view('admin.feedback_report.create', compact('user_report', 'user_id', 'gender'));
    }

    /**
     * store
     *
     * @param CreateRequest $request
     * @param FeedbackReportService $service
     * @return RedirectResponse
     * @throws \Src\Exception\AuthenticationException
     */
    public function store(CreateRequest $request, FeedbackReportService $service): RedirectResponse
    {
        $author = $this->author();
        $user_id = request()->query('user_id', null);
        $gender = (int) request()->query('gender', null);
        ($gender === 1) ? $index = TabUserIndex::MALE_USER_REPORT : $index = TabUserIndex::FEMALE_USER_REPORT;

        if (!$service->store($request->validatedForm(), $author->getId())) {
            flash()->error(__('flash.store.failed'));
            return redirect()->route('admin.user.show', compact('user_id', 'index'))->withInput();
        }
        flash()->info(__('flash.store.succeeded'));
        return redirect()->route('admin.user.show', compact('user_id', 'index'));
    }
}
