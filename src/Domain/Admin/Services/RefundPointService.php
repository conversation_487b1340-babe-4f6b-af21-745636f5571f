<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\FriendHistory;
use App\Eloquent\PartyHistory;
use App\Eloquent\PointHistory as EloquentPointHistory;
use App\Eloquent\User;
use App\Eloquent\RefundRatio as EloquentRefundRatio;
use App\Eloquent\MonthlyRefundPoint as EloquentMonthlyRefundPoint;
use Illuminate\Database\Eloquent\Builder;
use DB;
use Carbon\Carbon;
use Src\Domain\Constants\Point;
use Src\Domain\Constants\Ratio;
use Src\Enum\Gender;
use Src\Enum\PartyStatus;

/**
 * Class RefundPointService
 *
 * @package Src\Domain\Admin\Services
 */
class RefundPointService
{
    /**
     * Monthly refund point
     *
     * @return false|mixed
     */
    public function monthyRefundPoint()
    {
        $result = false;
        try {
            $result = DB::transaction(function () {
                $users = User::query()->getModel()
                    ->select('users.id as id', 'gender', 'login_id')
                    ->join('user_profiles', 'users.id', '=', 'user_id')
                    ->where('gender', '=', Gender::FEMALE)
                    ->get();

                foreach ($users as $user) {
                    $user_id = $user->id;
                    $total_point = $this->getTotalPointMonthly($user_id);
                    if ($total_point > 0) {
                        $friend = $this->getFriend($user_id);
                        $party_time = $this->getPartyTime($user_id);
                        $party_number = $this->getPartyNumber($user_id);
                        $ratio = Point::POINT_RATIO + $friend['ratio'] + $party_time['ratio'] + $party_number['ratio'];
                        $refund_point = $total_point * $ratio;
                        $refund_money = $refund_point * Point::RATIO_MONEY;

                        $refund_ratio_array = [
                            'user_id' => $user_id,
                            'month' => $this->getMonth(),
                            'ratio' => $ratio,
                            'criteria_friend_value' => $friend['value'],
                            'criteria_friend_ratio' => $friend['ratio'],
                            'criteria_party_time_value' => $party_time['value'],
                            'criteria_party_time_ratio' => $party_time['ratio'],
                            'criteria_party_number_value' => $party_number['value'],
                            'criteria_party_number_ratio' => $party_number['ratio']
                        ];
                        $refund_ratio = $this->refundRatioQuery()->createOrThrow($refund_ratio_array);
                        logger()->info('success create refund point.', ['id', $refund_ratio->id]);

                        $monthly_refund_point_array = [
                            'user_id' => $user_id,
                            'refund_ratio_id' => $refund_ratio->id,
                            'refund_money' => $refund_money,
                            'refund_point' => $refund_point,
                            'total_point' => $total_point
                        ];
                        $monthly_refund_point = $this->monthlyRefundPointQuery()->createOrThrow($monthly_refund_point_array);
                        logger()->info('success create monthly refund point', ['id', $monthly_refund_point->id]);
                    }
                }
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed create refund point.');
        }
        return $result;
    }

    /**
     * get Month refund
     *
     * @return string
     */
    public function getMonth()
    {
        $date = Carbon::now();
        $year = $date->year;
        $month = $date->month - 1;

        if ($month < 10) {
            $month = '0' . $month;
        }

        return $year . '-' . $month;
    }

    /**
     * get Total point monthly
     *
     * @param $user_id
     * @return int|mixed
     */
    public function getTotalPointMonthly($user_id)
    {
        $search = $this->getMonth();
        $revenues = $this->pointHistoryQuery()
            ->where('user_id', $user_id)
            ->where('point_at', 'like', $search . '%')
            ->get();

        $total = 0;
        foreach ($revenues as $revenue) {
            $total += $revenue->point;
        }
        return $total;
    }

    /**
     * get Friends
     *
     * @param $user_id
     * @return array
     */
    public function getFriend($user_id)
    {
        $search = $this->getMonth();
        $revenues = FriendHistory::query()
            ->where('receive_user_id', $user_id)
            ->whereNotNull('party_id')
            ->where('created_at', 'like', $search . '%')
            ->count();

        if (0 < $revenues && $revenues <= 9) {
            $ratio = Ratio::ONE_STAR;
        } elseif (10 <= $revenues && $revenues <= 14) {
            $ratio = Ratio::TWO_STAR;
        } elseif (15 <= $revenues && $revenues <= 19) {
            $ratio = Ratio::THREE_STAR;
        } elseif (20 <= $revenues && $revenues <= 24) {
            $ratio = Ratio::FOUR_STAR;
        } elseif ($revenues > 24) {
            $ratio = Ratio::FIVE_STAR;
        } else {
            $ratio = 0;
        }
        return [
            'value' => $revenues,
            'ratio' => $ratio
        ];
    }

    /**
     * get party time
     *
     * @param $user_id
     * @return array
     */
    public function getPartyTime($user_id)
    {
        $search = $this->getMonth();
        /** @var PartyHistory $party_histories */
        $party_histories = PartyHistory::queryModel()
            ->whereHas('participants', function ($query) use ($user_id, $search) {
                $query->where('user_id', $user_id);
            })->where('created_at', 'like', $search . '%')
            ->where('party_status', '=', PartyStatus::ENDED)
            ->get();

        $total = 0;
        foreach ($party_histories as $party_history) {
            $start_at = strtotime($party_history->start_at);
            $ended_at = strtotime($party_history->ended_at);
            $party_time = $ended_at - $start_at;
            $total += ($party_time / 3600);
        }
        return $this->calculateRatio($total);
    }

    /**
     * get party number
     *
     * @param $user_id
     * @return array
     */
    public function getPartyNumber($user_id)
    {
        $search = $this->getMonth();
        /** @var PartyHistory $count_party */
        $count_party = PartyHistory::queryModel()
            ->whereHas('participants', function ($query) use ($user_id, $search) {
                $query->where('user_id', $user_id);
            })->where('created_at', 'like', $search . '%')
            ->where('party_status', '=', PartyStatus::ENDED)
            ->count();
        return $this->calculateRatio($count_party);
    }

    /**
     * calculate ratio
     *
     * @param $value
     * @return array
     */
    public function calculateRatio($value): array
    {
        if (0 < $value && 7 >= $value) {
            $ratio = Ratio::ONE_STAR;
        } elseif (8 <= $value && $value <= 11) {
            $ratio = Ratio::TWO_STAR;
        } elseif (12 <= $value && $value <= 15) {
            $ratio = Ratio::THREE_STAR;
        } elseif (16 <= $value && $value <= 19) {
            $ratio = Ratio::FOUR_STAR;
        } elseif ($value > 19) {
            $ratio = Ratio::FIVE_STAR;
        } else {
            $ratio = 0;
        }
        return [
            'value' => $value,
            'ratio' => $ratio
        ];
    }

    /**
     * @return Builder|EloquentPointHistory
     */
    private function pointHistoryQuery()
    {
        return EloquentPointHistory::query()->getModel();
    }

    /**
     * @return Builder|EloquentRefundRatio
     */
    private function refundRatioQuery()
    {
        return EloquentRefundRatio::query()->getModel();
    }

    /**
     * @return Builder|EloquentMonthlyRefundPoint
     */
    private function monthlyRefundPointQuery()
    {
        return EloquentMonthlyRefundPoint::query()->getModel();
    }
}
