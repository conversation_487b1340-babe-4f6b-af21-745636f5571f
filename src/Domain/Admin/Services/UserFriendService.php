<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\User;
use App\Eloquent\UserFriend;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Admin\Models\User\UserDetail;
use Src\Domain\Admin\Models\UserFriend\UserFriendDetail;

/**
 * Class UserFriendService
 *
 * @package Src\Domain\Admin\Services
 */
class UserFriendService
{
    /**
     * fetch
     *
     * @param string $user_id
     * @return User[]|Collection
     */
    public function fetch(string $user_id): Collection
    {
        /** @var User $user */
        $user_friends = $this->userFriendQuery()->where('user_id', $user_id)->get();

        $user_friends->transform(static function ($user_friend) {
            return (new UserFriendDetail($user_friend))->toComponentFormValue();
        });
        return $user_friends;
    }

    /**
     * userFriend Query
     *
     * @return Builder|UserFriend
     */
    public function userFriendQuery()
    {
        return UserFriend::query()->getModel();
    }

}
