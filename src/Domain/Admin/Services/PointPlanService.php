<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\PointPlan as EloquentPointPlan;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\AbstractPaginator;
use Src\Domain\Admin\Models\PointPlan\PointPlanForm;
use Src\Domain\Admin\Models\PointPlan\PointPlan;
use DB;
use Src\Domain\Constants\Pagination;

/**
 * Class PointPlanService
 * @package Src\Domain\Admin\Services
 */
class PointPlanService
{

    /**
     * Fetch page
     *
     * @return LengthAwarePaginator|AbstractPaginator
     */
    public function fetchPage()
    {
        $query = $this->pointPlanQuery()
            ->orderByDesc('id');
        $paginator = $query->paginate(Pagination::PER_PAGE);

        /** @var LengthAwarePaginator|AbstractPaginator $paginator */
        $paginator->getCollection()->transform(static function ($point_plan) {
            return (new PointPlan($point_plan))->toComponentFormValue();
        });
        return $paginator;
    }










    /**
     * PointPlanQuery
     *
     * @return Builder|EloquentPointPlan
     */
    private function pointPlanQuery()
    {
        return EloquentPointPlan::query()->getModel();
    }
}
