<?php


namespace Src\Domain\Admin\Services;

use App\Eloquent\Feedback as EloquentFeedback;
use App\Mail\FeedbackGuestContact\SendFeedbackMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Mail;
use Src\Domain\Admin\Models\FeedbackGuestContact\FeedbackGuestContactDetail;
use Src\Domain\Admin\Models\FeedbackGuestContact\FeedbackGuestContactForm;
use DB;
use Src\Enum\ContactState;
use Src\Enum\SendStatus;

class FeedbackGuestContactService
{
    public function findOrFail(int $feedback_id)
    {
        /** @var EloquentFeedback $feedback */
        $feedback = $this->feedbackGuestContactQuery()->findOrFail($feedback_id);

        return new FeedbackGuestContactDetail($feedback);
    }

    public function store(FeedbackGuestContactForm $form, $creator_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $creator_id) {
               $store_data = array_merge($form->createAttributes(),
               [
                   'creator_id' => $creator_id
               ]);

               /** @var EloquentFeedback $feedback */
               $feedback = $this->feedbackGuestContactQuery()->createOrThrow($store_data);
                logger()->info('success to create guest contact feedback', ['feedback_id' => $feedback->id]);

                Mail::to($form->getGuestUserId())->send(new SendFeedbackMail([
                    'content' => $form->getContent(),
                ]));
                $feedback->guestContact->updateOrThrow([
                    'contact_status' => ContactState::SUPPORTED
                ]);

                $feedback->updateOrThrow([
                    'send_status' => SendStatus::SUCCESSFUL_SEND
                ]);
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('fail to create feedback', $form->toArray());
        }
        return $result;
    }

    /**
     * @return Builder|EloquentFeedback
     */
    public function feedbackGuestContactQuery()
    {
        return EloquentFeedback::query()->getModel();
    }
}
