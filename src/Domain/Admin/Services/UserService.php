<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Feedback;
use App\Eloquent\StorageFile;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Src\Domain\Admin\Models\Ticket\TicketHistoryDetail;
use Src\Domain\Admin\Models\User\UserDetail;
use Src\Domain\Admin\Models\User\UserForm;
use Src\Domain\Admin\Models\UserBlock\UserBlockDetail;
use Src\Domain\Admin\Models\UserContact\UserContactDetail;
use Src\Domain\Admin\Models\CSVDownload\UserCSVDetail;
use Src\Domain\Admin\Models\UserPoint\BuyPointHistoryDetail;
use Src\Domain\Admin\Models\UserPoint\PresentPointHistoryDetail;
use Src\Domain\Admin\Models\UserPoint\ReceivePointHistoryDetail;
use Src\Domain\Admin\Models\UserProfile\UserProfileDetail;
use Src\Domain\Admin\Models\UserReport\UserReportDetail;
use Src\Enum\PointUpdateDiv;
use Src\Enum\TicketUpdateDiv;
use Src\Traits\Utils\FileUpload\FileUploadable;
use Storage;

/**
 * Class UserService
 * @package Src\Domain\Admin\Services
 */
class UserService
{
    use FileUploadable;

    /**
     * fetchPage
     *
     * @param string|null $keyword
     * @return Collection
     */
    public function fetchPage(string $keyword = null): Collection
    {
        $query = $this->userQuery()::withTrashed()->get();
        $query->transform(static function ($user) {
            return (new UserDetail($user))->toComponentFormValue();
        });

        return $query;
    }

    /**
     * find Or Fail
     *
     * @param int $user_id
     * @return UserProfileDetail
     */
    public function findOrFail(int $user_id): UserProfileDetail
    {
        /** @var User $user */
        $user = $this->userQuery()::withTrashed()->with(['userProfile', 'userProfile.storageFile'])->findOrFail($user_id);
        return new UserProfileDetail($user);
    }

    /**
     * Store
     *
     * @param UserForm $form
     * @return bool
     */
    public function store(UserForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var User $user */
                $user = $this->userQuery()->createOrThrow($form->createAttributes());
                $avatar_attributes = $this->moveFileToRegularPath($form->getAvatar(), 'uploads/user', 'public');

                /** @var StorageFile $storage */
                $storage = $this->storageFileQuery()->createOrThrow($avatar_attributes);
                logger()->debug('success create storage file.', compact('avatar_attributes'));
                $user->userProfile()->create($form->createProfileAttributes($storage->id));
                logger()->debug('success create profile.', $form->toArray());
                logger()->info('success create user.', ['users.id' => $user->id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed create user.', $form->toArray());
        }
        return $result;
    }

    /**
     * Update
     *
     * @param UserForm $form
     * @param int $user_id
     * @return bool
     */
    public function update(UserForm $form, int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id, $form) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $old_avatar = $user->userProfile->storageFile->file_path;
                $old_avatar_uri = env('APP_URL') . "/storage/" . $old_avatar;

                $user->updateOrThrow($form->updateAttributes());
                $user->userProfile()->update($form->updateProfileAttributes());

                logger()->debug('success update profile.', $form->toArray());
                if ($form->getAvatar() && $form->getAvatar() !== $old_avatar_uri) {
                    $avatar_attributes = $this->moveFileToRegularPath($form->getAvatar(), 'uploads/user', 'public');
                    $user->userProfile->storageFile()->update($avatar_attributes);
                    Storage::disk('public')->delete($old_avatar);
                    logger()->debug('success update storage file.', compact('avatar_attributes'));
                }

                logger()->info('success update user.', compact($user_id));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update user', compact('user_id'));
        }
        return $result;
    }

    /**
     * update Warning
     *
     * @param int $is_warning
     * @param int $feedback_id
     * @param int $user_id
     * @return bool
     */
    public function updateWarning(int $is_warning, int $feedback_id, int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id, $is_warning, $feedback_id) {
                /** @var User $user */
                logger()->debug('update warning value',compact('is_warning'));
                $user = $this->userQuery()->findOrFail($user_id);
                /** @var Feedback $feedback */
                $feedback = $user->feedback()->findOrFail($feedback_id);

                $feedback->updateOrThrow(['enable_warning' => $is_warning]);
                logger()->debug('success update warning feedback',compact('is_warning'));
                logger()->info('success update feedback', compact('feedback_id'));
                $feedback->refresh();
                // Check Feedback Warning
                $feedback_warnings = $user->feedback()->where('enable_warning',1)->get();
                if (count($feedback_warnings) === 0) {
                    // Update warning user
                    $user->updateOrThrow(['is_warning' => false]);
                    logger()->debug('success update warning',compact('is_warning'));
                    logger()->info('success update user', compact('user_id'));
                }

                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed updated warning', compact('user_id'));
        }
        return $result;
    }

    /**
     * delete
     *
     * @param int $user_id
     * @return bool
     */
    public function delete(int $user_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_id) {
                /** @var User $user */
                $user = $this->userQuery()->findOrFail($user_id);
                $user->delete();
                logger()->info('success delete user.', compact('user_id'));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete user', compact('user_id'));
        }
        return $result;
    }

    /**
     * getUserTicket
     *
     * @param string $user_id
     * @return Collection
     */
    public function getUserTicket(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $ticket_history = $user->ticketHistories()->where('ticket_update_div', TicketUpdateDiv::BUY)->orderByDesc('created_at')->get();

        if ($ticket_history !== null) {
            $ticket_history->transform(static function ($history) {
                return (new TicketHistoryDetail($history))->toComponentFormValue();
            });
        }
        return $ticket_history;
    }

    /**
     * get Buy Point History
     *
     * @param string $user_id
     * @return Collection
     */
    public function getBuyPointHistory(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $point_history = $user->pointHistories()->where('point_update_div', PointUpdateDiv::BUY)->orderByDesc('created_at')->get();

        if ($point_history !== null) {
            $point_history->transform(static function ($history) {
                return (new BuyPointHistoryDetail($history))->toComponentFormValue();
            });
        }
        return $point_history;
    }

    /**
     * get User Block
     *
     * @param string $user_id
     * @return Collection
     */
    public function getUserBlock(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $user_blocks = $user->userBlocks()->orderByDesc('created_at')->get();

        if ($user_blocks !== null) {
            $user_blocks->transform(static function ($transform) {
                return (new UserBlockDetail($transform))->toComponentFormValue();
            });
        }
        return $user_blocks;
    }

    /**
     * get Present Point History
     *
     * @param string $user_id
     * @return User[]|Collection
     */
    public function getPresentPointHistory(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $present_point_history = $user->pointHistories()->where('point_update_div', PointUpdateDiv::PRESENT)->orderByDesc('created_at')->get();
        if ($present_point_history !== null) {
            $present_point_history->transform(static function ($transaction) {
                return (new PresentPointHistoryDetail($transaction))->toComponentFormValue();
            });
        }
        return $present_point_history;
    }

    /**
     * @param string $user_id
     * @return Collection
     */
    public function getReceivePointHistory(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $receive_point_history = $user->pointHistories()->where('point_update_div', PointUpdateDiv::RECEIVE)->orderByDesc('created_at')->get();
        // transform
        if ($receive_point_history !== null) {
            $receive_point_history->transform(static function ($transaction) {
                return (new ReceivePointHistoryDetail($transaction))->toComponentFormValue();
            });
        }
        return $receive_point_history;
    }

    /**
     * get User Report
     *
     * @param string $user_id
     * @return Collection
     */
    public function getUserReport(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $user_report = $user->userReports()->where('user_id', $user_id)
            ->orderByDesc('created_at')->get();

        if( $user_report !== null) {
            $user_report->transform(static function ($report) {
                return (new UserReportDetail($report))->toComponentFormValue();
            });
        }
        return $user_report;
    }

    /**
     * get User Contact
     *
     * @param string $user_id
     * @return Collection
     */
    public function getUserContact(string $user_id): Collection
    {
        /** @var User $user */
        $user = $this->userQuery()->findOrFail($user_id);
        $user_contact = $user->userContacts()->where('user_id', $user_id)->orderByDesc('created_at')->get();

        if($user_contact !== null) {
            $user_contact->transform(static function ($contact) {
                return (new UserContactDetail($contact))->toComponentFormValue();
            });
        }
        return $user_contact;
    }

    /**
     * getCSVDetail
     *
     * @return Collection
     */
    public function getCSVDetail(): Collection
    {
        $query = User::withTrashed()->get();
        $query->transform(static function($user) {
            return (new UserCSVDetail($user))->toComponentFormValue();
        });
        return $query;
    }

    /**
     * userQuery
     *
     * @return Builder|User
     */
    private function userQuery()
    {
        return User::query()->getModel();
    }

    /**
     * storage File Query
     *
     * @return Builder|User
     */
    private function storageFileQuery()
    {
        return StorageFile::query()->getModel();
    }
}
