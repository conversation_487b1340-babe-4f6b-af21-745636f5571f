<?php

namespace Src\Domain\Admin\Services;

use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Mail;
use Src\Domain\Admin\Models\UserFeedback\UserFeedbackForm;

/**
 * Class UserFeedbackService
 *
 * @package Src\Domain\Admin\Services
 */
class UserFeedbackService
{
    /**
     * store
     *
     * @param UserFeedbackForm $form
     * @return bool
     */
    public function store(UserFeedbackForm $form)
    {
        $result = false;
        try {
            $login_id = $form->getLoginId();
            $content = $form->getContent();

            Mail::to($login_id)->send((new Mailable())->text($content));
            logger()->info('succeed to send feedback', $form->toArray());
            return true;

        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed to send feedback', $form->toArray());
        }
        return $result;
    }
}
