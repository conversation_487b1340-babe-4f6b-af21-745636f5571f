<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Notification;
use App\Eloquent\Notification as EloquentNotification;
use App\Eloquent\User;
use App\Eloquent\UserNotification as EloquentUserNotification;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Admin\Models\Notification\NotificationDetail;
use Src\Domain\Admin\Models\Notification\NotificationForm;
use Src\Domain\Admin\Models\UserNotification\UserNotificationForm;
use Src\Domain\Api\Services\PushNotificationService;
use Src\Enum\EventType;
use Src\Enum\SendStatus;

/**
 * Class NotificationService
 *
 * @package Src\Domain\Admin\Services
 */
class NotificationService
{
    /**
     * fetchPage
     *
     * @return Collection
     */
    public function fetchPage(): Collection
    {
        $query = $this->notificationQuery()
            ->orderByDesc('created_at')
            ->get();
        $query->transform(static function($notification) {
            return (new NotificationDetail($notification))->toComponentFormValue();
        });

        return $query;
    }

    /**
     * find or fail
     *
     * @param int $notification_id
     * @return NotificationDetail
     */
    public function findOrFail(int $notification_id): NotificationDetail
    {
        /** @var EloquentNotification $notification */
        $notification = $this->notificationQuery()->findOrFail($notification_id);
        return new NotificationDetail($notification);
    }

    /**
     * store
     *
     * @param NotificationForm $form
     * @param int $creator_id
     * @return bool|mixed
     */
    public function store(NotificationForm $form, int $creator_id)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use($form, $creator_id) {
                $store_data = array_merge($form->createdAttributes(), ['creator_id' => $creator_id]);
                /** @var Notification $notification */
                $notification = $this->notificationQuery()->createOrThrow($store_data);
                logger()->info('success to create notification', ['notification_id' => $notification->id]);
                $foreign_table = $form->getForeignTable();
                if ($notification->sent_at->format('Y-m-d H:i') <= now()->addMinutes(3)->format('Y-m-d H:i')) {
                    $notification->updateOrThrow([
                        'send_status' => SendStatus::SUCCESSFUL_SEND ,
                        'send_schedule_at' => get_now_timestamp()
                    ]);
                    $notify_payload = [
                        'title' => $notification->title,
                        'body' => $notification->body
                    ];
                    $notify_data = [
                        'body' => $notification->body,
                        'event_type' => EventType::NOTIFICATION_NEW,
                    ];
                    // Create User Notification
                    $foreign_id = $notification->id;
                    $notification_div = $notification->notification_div;
                    /** @var EloquentUserNotification $user_notification */
                    $user_notification = $this->userNotificationQuery()->createOrThrow(UserNotificationForm::createNotificationAttributes($notification_div,
                        $foreign_table, $foreign_id, $notify_payload, $notify_data, $form->getPartyId(), $form->hasAction()));
                    logger()->debug('success to create user notification', ['user_notification' => $user_notification]);
                    $user_notification_id = $user_notification->id;

                    // Create User Notification creator
                    $user_notification_creator = $notification->userNotificationCreator()->create([
                        'user_notification_id' => $user_notification_id,
                        'foreign_table' => $foreign_table,
                    ]);
                    logger()->debug('success to create user notification creator', ['user_notification_creator' => $user_notification_creator]);

                    $users = User::all();
                    try {
                        /** @var User $user */
                        foreach ($users as $user) {
                            $user_notify_payload = [
                                'fcm_tokens' => array_except_null_value([$user->fcm_token]),
                                'title' => $notification->title,
                                'body' => $notification->body
                            ];
                            $user_notify_data =  [
                                'event_type' => EventType::NOTIFICATION_NEW,
                                'receive_user_ids' => [$user->id],
                                'title' => $notification->title,
                                'body' => $notification->body
                            ];

                            $user_notify_data['notification_id'] = $user_notification_id;
                            // Create Notification
                            UserNotificationService::createUserNotificationRelation($user, $user_notification_id, $user_notify_payload);

                            // Send push notification
                            PushNotificationService::sendPushNotificationMultiDevice($user_notify_payload, $user_notify_data);

                            logger()->info('success to create user_notification');
                        }
                    } catch (\Throwable $e) {
                        $notification->update(['send_status' => SendStatus::FAILED_SEND]);
                        logger()->error($e);
                        logger()->info('failed create user notification', $form->toArray());
                    }
                }
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed create notification', $form->toArray());
        }
        return $result;
    }

    /**
     * Create user notification
     *
     * @return bool
     */
    public function storeUserNotification()
    {
        $result = false;
        $foreign_table = (new Notification)->getTable();
        $notifications = EloquentNotification::all();
        foreach ($notifications as $notification) {
            if ($notification->sent_at->format('Y-m-d H:i') == now()->format('Y-m-d H:i')) {
                $users = User::all();
                foreach ($users as $user) {
                    try {
                        $notification->updateOrThrow([
                            'send_status' => SendStatus::SUCCESSFUL_SEND ,
                            'send_schedule_at' => get_now_timestamp()
                        ]);

                        $notify_payload = [
                            'fcm_tokens' => array_except_null_value([$user->fcm_token]),
                            'title' => $notification->title,
                            'body' => $notification->body
                        ];
                        $notify_data =  [
                            'event_type' => EventType::NOTIFICATION_NEW,
                            'receive_user_ids' => [$user->id],
                        ];

                        // Create Notification
                        $notification_id = UserNotificationService::createUserNotification($user, $notification->notification_div, $foreign_table, $notification->id, $notify_payload, $notify_data);
                        $notify_data['notification_id'] = $notification_id;

                        // Send socket
                        request_socket_api_and_push_notification('socket_api.notification.new', $notify_data, $notify_payload);

                        logger()->info('success to create user_notification');
                    } catch (\Throwable $e) {
                        $notification->update(['send_status' => SendStatus::FAILED_SEND]);
                        logger()->error($e);
                        logger()->info('failed create notification');
                    }
                }
            }
        }
        return $result;
    }

    /**
     * notificationQuery
     *
     * @return Builder|EloquentNotification
     */
    private function notificationQuery()
    {
        return EloquentNotification::query()->getModel();
    }

    /**
     * @return Builder|EloquentUserNotification
     */
    private function userNotificationQuery()
    {
        return EloquentUserNotification::query()->getModel();
    }
}
