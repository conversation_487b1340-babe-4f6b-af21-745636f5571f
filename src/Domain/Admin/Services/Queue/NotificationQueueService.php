<?php

namespace Src\Domain\Admin\Services\Queue;

use Src\Enum\EventType;

class NotificationQueueService
{
    /**
     * start
     *
     * @param int $user_id
     * @param int $number_unread_notification
     * @param array $notification
     * @return bool
     */
    public function sendNewBadge(int $user_id, int $number_unread_notification, array $notification)
    {
        request_socket_api('socket_api.notification.new', [
            'event_type' => EventType::NOTIFICATION_NEW,
            'receive_user_ids' => [$user_id],
            'number_unread_notification' => $number_unread_notification,
            'title' => $notification['title'],
            'body' => $notification['body']
        ]);
        return true;
    }
}
