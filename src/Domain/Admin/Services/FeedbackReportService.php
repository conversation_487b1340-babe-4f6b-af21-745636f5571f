<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Feedback as EloquentFeedback;
use App\Eloquent\UserReport;
use Illuminate\Database\Eloquent\Builder;
use Src\Enum\EnableWarning;
use Src\Enum\ReportDiv;
use Src\Domain\Admin\Models\FeedbackReport\FeedbackReportDetail;
use Src\Domain\Admin\Models\FeedbackReport\FeedbackReportForm;
use Src\Enum\EventType;
use Src\Enum\FeedbackDiv;
use Src\Enum\ReportStatus;
use Src\Enum\SendStatus;

/**
 * Class FeedbackReportService
 *
 * @package Src\Domain\Admin\Services
 */
class FeedbackReportService
{
    /**
     * store
     *
     * @param FeedbackReportForm $form
     * @param int $creator_id
     * @return bool|mixed
     */
    public function store(FeedbackReportForm $form, int $creator_id)
    {
        $result = false;
        try {
            $result = \DB::transaction(function () use ($form, $creator_id) {
                $store_data = array_merge($form->createAttributes(), [
                    'creator_id' => $creator_id,
                    'feedback_div' => FeedbackDiv::REPORT,
                ]);
                /** @var EloquentFeedback $feedback */
                $feedback = $this->feedbackQuery()->createOrThrow($store_data);
                logger()->debug('success to create feedback report', $form->toArray());
                logger()->info('success to create feedback report', ['id' => $feedback->id]);

                $foreign_table = $form->getForeignTable();

                if ($feedback->sent_at->format('Y-m-d H:i') <= now()->addMinute(3)->format('Y-m-d H:i')) {
                    // Update warning user status
                    if($feedback->enable_warning === EnableWarning::WARNING) {
                        $feedback->targetUser->updateOrThrow([
                            'is_warning' => EnableWarning::WARNING
                        ]);
                    }
                    // Update report status
                    $feedback->feedbackable->updateOrThrow([
                        'report_status' => ReportStatus::PROCESSED
                    ]);
                    // Update Send Status
                    $feedback->update([
                        'send_status' => SendStatus::SUCCESSFUL_SEND,
                        'send_schedule_at' => get_now_timestamp()
                    ]);
                    $user = $feedback->targetUser ;
                    try {
                            $user_id = $user->id;
                            $notify_payload = [
                                'fcm_tokens' => array_except_null_value([$user->fcm_token]),
                                'title' => ReportDiv::getDescription($feedback->feedbackable->report_div),
                                'body' => $feedback->content
                            ];
                            $notify_data =  [
                                'event_type' => EventType::NOTIFICATION_NEW,
                                'receive_user_ids' => [$user_id],
                            ];

                            // Create Notification
                            $notification_id = UserNotificationService::createUserNotification($user, $feedback->feedback_div, $foreign_table, $feedback->id, $notify_payload, $notify_data);
                            $notify_data['notification_id'] = $notification_id;

                            // Send socket
                            request_socket_api_and_push_notification('socket_api.notification.new', $notify_data, $notify_payload);
                            logger()->info('success to create user_notification');

                    } catch (\Throwable $e) {
                        $feedback->update(['send_status' => SendStatus::FAILED_SEND]);
                        logger()->error($e);
                        logger()->info('failed create user notification', $form->toArray());
                    }
                }
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('fail to create feedback report', $form->toArray());
        }
        return $result;
    }

    /**
     * findOrFail
     *
     * @param int $feedback_id
     * @return FeedbackReportDetail
     */
    public function findOrFail(int $feedback_id): FeedbackReportDetail
    {
        /** @var EloquentFeedback $feedback_report */
        $feedback_report = $this->feedbackQuery()->findOrFail($feedback_id);
        return new FeedbackReportDetail($feedback_report);
    }

    /**
     * @return Builder|EloquentFeedback
     */
    public function feedbackQuery()
    {
        return EloquentFeedback::query()->getModel();
    }
}
