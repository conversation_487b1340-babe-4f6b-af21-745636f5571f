<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Party as EloquentParty;
use App\Eloquent\PartyHistory;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Admin\Models\Party\Party;
use Illuminate\Database\Eloquent\Builder;
use DB;
use Src\Domain\Admin\Models\PartyHistory\PartyHistoryDetail;

class PartyService
{
    /**
     * Fetch page
     *
     * @return Collection
     */
    public function fetchPage(): Collection
    {
        $query = PartyHistory::query()->getModel()
            ->orderByDesc('created_at')
            ->get();
        $query->transform(static function ($party_history) {
            return (new PartyHistoryDetail($party_history))->toComponentFormValue();
        });
        return $query;
    }

    /**
     * Find or Fail
     *
     * @param int $party_id
     * @return Party
     */
    public function findOrFail(int $party_id): Party
    {
        /** @var EloquentParty $party */
        $party = $this->partyQuery()->findOrFail($party_id);
        return new Party($party);
    }

    /**
     * party query
     *
     * @return Builder|EloquentParty
     */
    private function partyQuery()
    {
        return EloquentParty::query()->getModel();
    }
}
