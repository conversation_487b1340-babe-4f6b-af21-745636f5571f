<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\UserReport as EloquentUserReport;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Src\Domain\Admin\Models\FeedbackReport\FeedbackReportDetail;
use Src\Domain\Admin\Models\UserReport\UserReportDetail;
use Src\Domain\Admin\Models\UserReport\UserReportForm;
use Src\Domain\Admin\Models\UserReport\UserReport;
use DB;

/**
 * Class UserReportService
 * @package Src\Domain\Admin\Services
 */
class UserReportService
{

    /**
     * Fetch page
     *
     * @return Collection
     */
    public function fetchPage(): Collection
    {
        $query = $this->userReportQuery()
            ->orderByDesc('id')->get();
        $query->transform(static function ($user_report) {
            return (new UserReport($user_report))->toComponentFormValue();
        });
        return $query;
    }

    /**
     * Find Or Fail
     *
     * @param int $user_report_id
     * @return UserReport
     */
    public function findOrFail(int $user_report_id): UserReport
    {
        /** @var EloquentUserReport $user_report */
        $user_report = $this->userReportQuery()->findOrFail($user_report_id);
        return new UserReport($user_report);
    }

    /**
     * getFeedbackForm
     *
     * @param int $user_report_id
     * @return string
     */
    public function getUserReportDetail(int $user_report_id): string
    {
        /** @var EloquentUserReport $user_report */
        $user_report = $this->userReportQuery()->findOrFail($user_report_id);
        return (new UserReportDetail($user_report))->toComponentValue();
    }

    /**
     * Update
     *
     * @param UserReportForm $form
     * @param int $user_report_id
     * @return bool
     */
    public function update(UserReportForm $form, int $user_report_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_report_id, $form) {
                /** @var EloquentUserReport $user_report */
                $user_report = $this->userReportQuery()->findOrFail($user_report_id);
                $user_report->updateOrThrow($form->updateAttributes());
                logger()->info('success update user_report.', compact($user_report_id));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update user_report', compact('user_report_id'));
        }
        return $result;
    }

    /**
     * Delete
     *
     * @param int $user_report_id
     * @return bool
     */
    public function delete(int $user_report_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($user_report_id) {
                /** @var EloquentUserReport $user_report */
                $user_report = $this->userReportQuery()->findOrFail($user_report_id);
                $user_report->delete();
                logger()->info('success delete user_report.', compact('user_report_id'));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete user_report', compact('user_report_id'));
        }
        return $result;
    }

    /**
     * UserReportQuery
     *
     * @return Builder|EloquentUserReport
     */
    private function userReportQuery()
    {
        return EloquentUserReport::query()->getModel();
    }
}
