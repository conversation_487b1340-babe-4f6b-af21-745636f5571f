<?php

namespace Src\Domain\Admin\Services;

use App\Eloquent\Account;
use App\Eloquent\User;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Src\Domain\Admin\Models\Account\AccountDetail;
use Src\Domain\Admin\Models\Account\AccountForm;
use Src\Domain\Admin\Models\Account\PasswordForm;
use Src\Exception\ErrorException;

/**
 * Class AccountService
 *
 * @package Src\Domain\Admin\Services
 */
class AccountService
{
    /**
     * fetch All
     *
     * @param string|null $keyword
     * @return Collection
     */
    public function fetchAll(string $keyword = null): Collection
    {
        $query = $this->accountQuery()
            ->where('login_id', 'like', "%$keyword%")
            ->orWhere('email', 'like', "%$keyword%")
            ->orderByDesc('id')
            ->get();
        $query->transform(static function ($account) {
            return (new AccountDetail($account))->toComponentFormValue();
        });

        return $query;
    }

    /**
     * find Or Fail
     *
     * @param int $author_id
     * @return AccountDetail
     */
    public function findOrFail(int $author_id): AccountDetail
    {
        /** @var Account $account */
        $account = $this->accountQuery()->findOrFail($author_id);
        return new AccountDetail($account);
    }

    /**
     * Update profile
     *
     * @param int $account_id
     * @param AccountForm $form
     * @return bool|mixed
     */
    public function update(int $account_id, AccountForm $form)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($account_id, $form) {
                /** @var Account $account */
                $account = $this->accountQuery()->findOrFail($account_id);
                $account->updateOrThrow($form->updateAttributes());
                logger()->info('success update profile.', ['account_id' => $account_id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update profile', compact('account_id'));
        }
        return $result;
    }

    /**
     * Store
     *
     * @param AccountForm $form
     * @return bool
     */
    public function store(AccountForm $form): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form) {
                /** @var Account $account */
                $account = $this->accountQuery()->createOrThrow($form->createAttributes());

                logger()->debug('success create account.', $form->toArray());
                logger()->info('success create account.', ['accounts.id' => $account->id]);
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed create account.', $form->toArray());
        }
        return $result;
    }

    /**
     * delete
     *
     * @param int $account_id
     * @return bool
     */
    public function delete(int $account_id): bool
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($account_id) {
                /** @var User $user */
                $user = $this->accountQuery()->findOrFail($account_id);
                $user->delete();
                logger()->info('success delete account.', compact('account_id'));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed delete account', compact('account_id'));
        }
        return $result;
    }

    /**
     * change Password
     *
     * @param int $account_id
     * @param PasswordForm $form
     * @return array
     */
    public function changePassword(int $account_id, PasswordForm $form): array
    {
        $result = false;
        $error = null;
        try {
            $result = DB::transaction(function () use ($account_id, $form, &$error) {
                /** @var Account $account */
                $account = $this->accountQuery()->findOrFail($account_id);
                if (!\Hash::check($form->getOldPassword(), $account->password)) {
                    $error = __('common.old-password-not-match');
                    throw new ErrorException('old password not match');
                }
                $account->update($form->updateAttributes());
                logger()->info('success update password.', compact('account_id'));
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->debug('failed update password', compact('account_id'));
        }
        return [$result, $error];
    }

    /**
     * account Query
     *
     * @return Builder|Account
     */
    private function accountQuery()
    {
        return Account::query()->getModel();
    }
}
