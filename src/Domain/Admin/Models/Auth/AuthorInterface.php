<?php

namespace Src\Domain\Admin\Models\Auth;

use App\Eloquent\Account;

/**
 * Interface AuthorInterface
 * @package Src\Domain\Admin\Models\Auth
 */
interface AuthorInterface
{
    /**
     * AuthorInterface constructor.
     * @param Account $account
     */
    public function __construct(Account $account);

    /**
     * @return int
     */
    public function getId(): int;

    /**
     * @return string
     */
    public function getLoginId(): string;

    /**
     * @return null|string
     */
    public function getName(): ?string;

    /**
     * @return bool
     */
    public function isPrivilege(): bool;

    /**
     * @param string $ability
     * @param string $argument
     * @return bool
     */
    public function can(string $ability, string $argument): bool;
}
