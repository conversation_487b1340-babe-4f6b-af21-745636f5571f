<?php

namespace Src\Domain\Admin\Models\Notification;

use App\Eloquent\Notification as EloquentNotification;
use Carbon\Carbon;
use Src\Domain\Model as BaseModel;
use Src\Enum\DateTimeFormat;
use Src\Enum\NotificationDiv;
use Src\Traits\FormatDate;

/**
 * Class NotificationDetail
 *
 * @package Src\Domain\Admin\Models\SendNotification
 */
class NotificationDetail extends BaseModel
{
    use FormatDate;
    /** @var EloquentNotification  */
    private $notification;

    /**
     * NotificationDetail constructor.
     * @param EloquentNotification $notification
     */
    public function __construct(EloquentNotification $notification)
    {
        $this->notification = $notification;
    }

    /**
     * Get Began At
     *
     * @return Carbon|null
     */
    public function getSentAt(): Carbon
    {
        return $this->notification->sent_at;
    }

    /**
     * Get Began At Format
     *
     * @return string
     */
    public function getSentAtFormat(): string
    {
        return format_date_time($this->getSentAt(), DateTimeFormat::DATETIME());
    }

    /**
     * Get isImportant
     *
     * @return int
     */
    public function getNotificationDiv():int
    {
        return $this->notification->notification_div;
    }

    /**
     * Get isImportant Name
     *
     * @return string
     */
    public function getNotificationDivName(): string
    {
        return NotificationDiv::getDescription($this->getNotificationDiv());
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return format_date_time($this->notification->created_at, DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function getCreator(): string
    {
        return data_get($this->notification->creator->account_name, 'account_name', '');
    }

    /**
     * Get Component Value
     *
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Get Component Form Value
     *
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->notification->id,
            'creatorId' => $this->notification->creator_id,
            'creator' => $this->getCreator(),
            'title' => $this->notification->title,
            'body' => $this->notification->body,
            'notificationDiv' => $this->getNotificationDiv(),
            'notificationDivName' => $this->getNotificationDivName(),
            'sentAt' => $this->getSentAtFormat(),
            'createdAt' => $this->getCreatedAt()
        ];
    }
}
