<?php

namespace Src\Domain\Admin\Models\Notification;

use App\Eloquent\Notification;
use Carbon\Carbon;
use Src\Domain\FormModel;
use Src\Enum\SendStatus;

/**
 * Class NotificationForm
 *
 * @package Src\Domain\Admin\Models\SendNotification\Form
 */
class NotificationForm extends FormModel
{
    /** @var string|null */
    protected $title;
    /** @var string|null */
    protected $body;
    /** @var int|null */
    protected $notificationDiv;
    /** @var string|null */
    protected $sentAt;
    /** @var string|null  */
    protected $party_id;

    protected $fields = [
        'title' => 'string',
        'body' => 'string',
        'notificationDiv' => 'int',
        'sentAt' => 'string',
        'send_status' => 'int'
    ];

    /**
     * NotificationForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->title = $input['title'];
        $this->body = $input['body'];
        $this->notificationDiv = $input['notificationDiv'];
        $this->sentAt = $input['sentAt'];
    }

    /**
     * Get Began At
     *
     * @return string
     */
    public function getSendAt(): string
    {
        return Carbon::parse($this->sentAt);
    }

    /**
     * @return string
     */
    public function getForeignTable(): string
    {
        return (new Notification)->getTable();
    }

    /**
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        return isset($this->party_id) ? $this->party_id : null;
    }

    /**
     * @return bool
     */
    public function hasAction(): bool
    {
        return false;
    }

    /**
     * Create Attributes
     *
     * @return array
     */
    public function createdAttributes(): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'notification_div' => $this->notificationDiv ? $this->notificationDiv : 0,
            'sent_at' => $this->getSendAt(),
            'send_status' => SendStatus::WAIT_SEND,
        ];
    }
}
