<?php

namespace Src\Domain\Admin\Models\Ticket;

use App\Eloquent\TicketHistory;
use Carbon\Carbon;
use Src\Domain\Model;
use Src\Enum\BuySituation;
use Src\Enum\CategoryTicketHistory;
use Src\Enum\DateTimeFormat;

/**
 * Class TicketHistoryDetail
 *
 * @package Src\Domain\Admin\Models\Ticket
 */
class TicketHistoryDetail extends Model
{
    private $ticket_history;

    /**
     * constructor
     *
     * TicketHistoryDetail constructor.
     * @param TicketHistory $ticket_history
     */
    public function __construct(TicketHistory $ticket_history)
    {
        $this->ticket_history = $ticket_history;
    }
    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->ticket_history->id;
    }
    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->ticket_history->user_id;
    }

    /**
     * @return string
     */
    public function getTicketPlanName(): ?string
    {
        return $this->ticket_history->name;
    }

    /**
     * @return string
     */
    public function getPrice(): string
    {
        return number_format($this->ticket_history->money).' 円';
    }

    /**
     * @return int
     */
    public function getPlanTicketNumber(): ?int
    {
        return $this->ticket_history->ticket;
    }

    /**
     * @return int|null
     */
    public function getPlanTicketTimeUse(): ?string
    {
        $time_minute = $this->ticket_history->time_minute;
        $time_hour = $this->ticket_history->time_hour;
        if($time_minute){
            return strval($time_minute) . ' 分';
        }
        if ($time_hour) {
            return strval($time_hour) . ' 時間';
        }
       return '';
    }

    /**
     * @return int|null
     */
    public function getBuySituation(): ?int
    {
        return $this->ticket_history->buy_situation;
    }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->ticket_history->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }
    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'user_id' => $this->getUserId(),
            'ticket_plan_name' => $this->getTicketPlanName(),
            'ticket' => $this->getPlanTicketNumber(),
            'time_use' => $this->getPlanTicketTimeUse(),
            'buy_situation' => BuySituation::getDescription($this->getBuySituation()),
            'price' => $this->getPrice(),
            'created_at' => $this->getCreatedAtFormat(),
        ];
    }
}
