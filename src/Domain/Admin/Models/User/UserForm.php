<?php

namespace Src\Domain\Admin\Models\User;

use Carbon\Carbon;
use Src\Domain\FormModel;

/**
 * Class UserForm
 * @package Src\Domain\Admin\Models\User
 */
class UserForm extends FormModel
{
    /** @var string|null */
    protected $password;
    /** @var string|null */
    protected $login_id;
    /** @var int|null */
    protected $gender;
    /** @var int|null */
    protected $gender_partner;
    /** @var string|null */
    protected $birthday;
    /** @var string|null */
    protected $avatar;
    /** @var string|null */
    protected $nickname;
    /** @var string|null */
    protected $occupation;

    protected $fields = [
        'password' => 'string',
        'login_id' => 'string',
        'occupation' => 'string',
        'gender' => 'int',
        'gender_partner' => 'int',
        'birthday' => 'string',
        'avatar' => 'string',
        'nickname' => 'string',
    ];

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->nickname = $input['nickname'];
        $this->occupation = $input['occupation'];
        $this->password = $input['password'];
        $this->login_id = $input['login_id'];
        $this->gender = $input['gender'];
        $this->gender_partner = $input['gender_partner'];
        $this->birthday = $input['birthday'];
        $this->avatar = $input['avatar'];
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return int|null
     */
    public function getGender(): ?int
    {
        return $this->gender;
    }

    /**
     * @return int|null
     */
    public function getGenderPartner(): ?int
    {
        return $this->gender_partner;
    }

    /**
     * @return Carbon|null
     */
    public function getBirthYear(): ?int
    {
        return now()->subYears($this->birthday)->format("Y");
    }

    /**
     * @return string|null
     */
    public function getBirthDay(): ?string
    {
        return $this->birthday;
    }

    /**
     * @return string|null
     */
    public function getAvatar(): ?string
    {
        return $this->avatar;
    }

    /**
     * @return string|null
     */
    public function getNickame(): ?string
    {
        return $this->nickname;
    }

    /**
     * @return string|null
     */
    public function getOccupation(): ?string
    {
        return $this->occupation;
    }

    /**
     * @return string
     */
    public function getMemberId(): string
    {
        $fuid = uniqid(1);
        $uid = substr($fuid,8,6);
        return $uid;
    }


    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'password' => bcrypt($this->getPassword()),
            'login_id' => $this->getLoginId(),
            'member_id' => $this->getMemberId(),
            'registered_at' => now()
        ];
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'login_id' => $this->getLoginId()
        ];
    }

    /**
     * create Profile Attributes
     *
     * @param int $avatar_id
     * @return array
     */
    public function createProfileAttributes(int $avatar_id): array
    {
        return [
            'nickname' => $this->getNickame(),
            'occupation' => $this->getOccupation(),
            'gender' => $this->getGender(),
            'gender_partner' => $this->getGenderPartner(),
            'birthday' => $this->getBirthDay(),
            'avatar_id' => $avatar_id
        ];
    }

    /**
     * create Profile Attributes
     *
     * @return array
     */
    public function updateProfileAttributes(): array
    {
        return [
            'gender' => $this->getGender(),
            'birthday' => $this->getBirthDay()
        ];
    }
}
