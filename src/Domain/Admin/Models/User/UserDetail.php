<?php

namespace Src\Domain\Admin\Models\User;

use App\Eloquent\User;
use Src\Enums\CertificateLevel;
use Src\Enums\EmergencyRelation;
use Src\Enums\Gender;
use Src\Enums\UserBankType;
use Src\Enums\UserStatus;

/**
 * Class UserDetail
 * @package Src\Domain\Admin\Models\User
 */
class UserDetail
{
    /**
     * @var User
     */
    protected User $user;

    /**
     * UserDetail constructor.
     * @param User $user The user instance to be detailed
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Magic method to dynamically handle getter methods for user properties.
     *
     * @param string $method The method name being called
     * @param array $args The arguments passed to the method
     * @return mixed|null The value of the user property or null if not found
     */
    public function __call(string $method, array $args)
    {
        $property = lcfirst(substr($method, 3));
        $snakeCase = strtolower(preg_replace('/[A-Z]/', '_$0', $property));
        return $this->user->$snakeCase ?? null;
    }

    /**
     * @return string|null
     */
    public function getAvatarUrl(): ?string
    {
        $path = optional($this->user->avatar)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string|null
     */
    public function getHealthCertificateUrl(): ?string
    {
        $path = optional($this->user->healthCertificate)->file_path;
        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string
     */
    public function getUserStatusName(): string
    {
        return UserStatus::getDescription($this->user->user_status);
    }

    /**
     * @return string
     */
    public function getJapaneseLevelName(): string
    {
        return CertificateLevel::getDescription($this->getJapaneseLevel());
    }

    /**
     * @return string
     */
    public function getGenderName(): string
    {
        return Gender::getDescription($this->getGender());
    }

    /**
     * @return string|null
     */
    public function getNationalityName(): ?string
    {
        return country_name($this->getNationality());
    }

    /**
     * @return string
     */
    public function getBankTypeName(): string
    {
        return UserBankType::getDescription($this->getBankType());
    }

    /**
     * @return string|null
     */
    public function getEmergencyRelation(): ?string
    {
        return $this->user->emergency_relation;
    }

    /**
     * @return string|null
     */
    public function getEmergencyRelationName(): ?string
    {
        if (is_null($this->getEmergencyRelation())) {
            return null;
        }
        return EmergencyRelation::getDescription($this->getEmergencyRelation());
    }

    /**
     * @return string|null
     */
    public function getBankName(): ?string
    {
        return optional($this->user->userBank)->bank_name;
    }

    /**
     * @return string|null
     */
    public function getBankBranch(): ?string
    {
        return optional($this->user->userBank)->bank_branch;
    }

    /**
     * @return string|null
     */
    public function getDepositType(): ?string
    {
        return optional($this->user->userBank)->deposit_type;
    }

    /**
     * @return string|null
     */
    public function getAccountName(): ?string
    {
        return optional($this->user->userBank)->account_name;
    }

    /**
     * @return string|null
     */
    public function getAccountNumber(): ?string
    {
        return optional($this->user->userBank)->account_number;
    }

    /**
     * @return string|null
     */
    public function getFrontCardUrl(): ?string
    {
        $fort_card = optional($this->user->residenceCard)->frontCard?->file_path;

        return $fort_card ? presigned_url($fort_card) : null;
    }

    public function getBackCardUrl(): ?string
    {
        $back_card = optional($this->user->residenceCard)->backCard?->file_path;

        return $back_card ? presigned_url($back_card) : null;
    }

    /**
     * @return string|null
     */
    public function getPeriodType(): ?string
    {
        return optional($this->user->residenceCard)->period_type;
    }

    /**
     * @return string|null
     */
    public function getSchoolName(): ?string
    {
        return optional($this->user->residenceCard)->school_name;
    }

    public function getIdentificationUrl(): ?string
    {
        $identification = optional($this->user->residenceCard)->identification?->file_path;

        return $identification ? presigned_url($identification) : null;
    }

    public function getPeriodOfStay(): ?string
    {
        return optional($this->user->residenceCard)->period_of_stay;
    }

    public function getPeriodExpireAt(): ?string
    {
        return optional($this->user->residenceCard)->period_expire_at;
    }

    /**
     * @return string|null
     */
    public function getPassportUrl(): ?string
    {
        $passport = optional($this->user->passport)->file_path;

        return $passport ? presigned_url($passport) : null;
    }

    /**
     * @return int
     */
    public function getJobParticipationCount(): int
    {
        return $this->user->job_participation_count;
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'code'  => $this->getCode(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'avatarUrl' => $this->getAvatarUrl(),
            'healthCertificateUrl' => $this->getHealthCertificateUrl(),
            'userStatus' => $this->getUserStatus(),
            'userStatusName' => $this->getUserStatusName(),
            'nameKana' => $this->getNameKana(),
            'nameKanji' => $this->getNameKanji(),
            'phoneNumber' => $this->getPhoneNumber(),
            'gender' => $this->getGender(),
            'genderName' => $this->getGenderName(),
            'birthday' => $this->getBirthday(),
            'nationality' => $this->getNationality(),
            'nationalityName' => $this->getNationalityName(),
            'hasCertificate' => $this->getHasCertificate(),
            'japaneseLevel' => $this->getJapaneseLevel(),
            'japaneseLevelName' => $this->getJapaneseLevelName(),
            'arrivalDate' => $this->getArrivalDate(),
            'zipCode' => $this->getZipCode(),
            'prefecture' => $this->getPrefecture(),
            'streetAddress' => $this->getStreetAddress(),
            'townAddress' => $this->getTownAddress(),
            'trainStationName' => $this->getTrainStationName(),
            'emergencyName' => $this->getEmergencyName(),
            'emergencyRelation' => $this->getEmergencyRelation(),
            'emergencyRelationName' => $this->getEmergencyRelationName(),
            'emergencyPhoneNumber' => $this->getEmergencyPhoneNumber(),
            'bankType' => $this->getBankType(),
            'bankTypeName' => $this->getBankTypeName(),
            'bankName' => $this->getBankName(),
            'bankBranch' => $this->getBankBranch(),
            'depositType' => $this->getDepositType(),
            'accountName' => $this->getAccountName(),
            'accountNumber' => $this->getAccountNumber(),
            'emailVerificationAt' => $this->getEmailVerificationAt(),
            'identificationAt' => $this->getIdentificationAt(),
            'isDisable' => $this->getIsDisable(),
            'frontCardUrl' => $this->getFrontCardUrl(),
            'backCardUrl' => $this->getBackCardUrl(),
            'periodType' => $this->getPeriodType(),
            'schoolName' => $this->getSchoolName(),
            'identificationUrl' => $this->getIdentificationUrl(),
            'periodOfStay' => $this->getPeriodOfStay(),
            'periodExpireAt' => $this->getPeriodExpireAt(),
            'passportUrl' => $this->getPassportUrl(),
            'passportNumber' => $this->getPassportNumber(),
            'passportExpiredAt' => $this->getPassportExpiredAt(),
            'jobParticipationCount' => $this->getJobParticipationCount()
        ];
    }
}
