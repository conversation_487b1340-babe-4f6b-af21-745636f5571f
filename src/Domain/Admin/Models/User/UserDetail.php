<?php

namespace Src\Domain\Admin\Models\User;

use App\Eloquent\Feedback;
use App\Eloquent\User;
use Carbon\Carbon;
use Src\Domain\Model as BaseModel;
use Src\Enum\ContactDiv;
use Src\Enum\DateTimeFormat;
use Src\Enum\EnableWarning;
use Src\Enum\Gender;
use Src\Enum\IdentificationType;
use Src\Enum\ReportDiv;
use Src\Enum\UserStatus;

/**
 * Class UserDetail
 * @package Src\Domain\Admin\Models\User
 */
class UserDetail extends BaseModel
{
    /**
     * @var User
     */
    private $user;

    /**
     * Summary constructor.
     * @param User $user
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * @return string
     */
    public function getMemberId(): string
    {
        return $this->user->member_id;
    }

    /**
     * @return int
     */
    public function getMobilePlatform(): int
    {
        return $this->user->mobile_platform;
    }

    /**
     * Get Created At
     *
     * @return Carbon|null
     */
    public function getCreatedAt(): Carbon
    {
        return $this->user->created_at;
    }

    /**
     * Get Created At Format
     *
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return Carbon|null
     */
    public function getBirthday()
    {
        return isset($this->user->userProfile->birthday) ? Carbon::parse($this->user->userProfile->birthday) : null;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getBirthdayFormat(): string
    {
        if($this->getBirthday()){
            return format_date_time($this->getBirthday(), DateTimeFormat::DATE());
        }
        return '';
    }

    /**
     * @return Carbon|null
     */
    public function getRegisterAt(): ?Carbon
    {
        return $this->user->registered_at;
    }

    /**
     * @return string
     */
    public function getRegisterAtFormat(): string
    {
        return format_date_time($this->getRegisterAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return Carbon|null
     */
    public function getWithdrawAt(): ?Carbon
    {
        return optional($this->user->userWithdrawal)->withdrawal_at;
    }

    /**
     * @return string|null
     */
    public function getWithdrawAtFormat(): ?string
    {
        if($this->getWithdrawAt()){
            return format_date_time($this->getWithdrawAt(), DateTimeFormat::DATETIME());
        }
        return '';
    }

    /**
     * @return Carbon|null
     */
    public function getBecomeMemberAt(): ?Carbon
    {
        return $this->user->official_member_at;
    }

    /**
     * @return string|null
     */
    public function getBecomeMemberAtFormat(): ?string
    {
        return format_date_time($this->getBecomeMemberAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return int
     */
    public function getUserStatus(): int
    {
        if ($this->user->user_status) {
            return $this->user->user_status;
        }
        return 1;
    }

    /**
     * @return string|null
     */
    public function getCardPicture(): ?string
    {
        if($this->user->userProfile){
            return $this->user->userProfile->card->file_url;
        }
        return '';
    }

    /**
     * @return int
     */
    public function getUserPoint(): int
    {
        if($this->user->userPoint){
            return $this->user->userPoint->point;
        }
        return 0;
    }

    /**
     * @return int
     */
    public function getUserTicket(): int
    {
        if($this->user->userTicket){
            return $this->user->userTicket->ticket;
        }
        return 0;
    }

    /**
     * @return string|null
     */
    public function getCardUrl(): ?string
    {
        if($this->user->userVerification){
            return  $this->user->userVerification->storageFile->file_url;
        }
        return null;
    }

    /**
     * @return int|null
     */
    public function getIdentificationType(): ?int
    {
        if($this->user->userVerification){
            return  $this->user->userVerification->identification_type;
        }
        return null;
    }

    /**
     * @return string
     */
    public function getIdentificationName(): string
    {
        if($this->getIdentificationType()){
            return IdentificationType::getDescription($this->getIdentificationType());
        }
        return '';
    }

    /**
     * @return bool
     */
    public function getWarning(): bool
    {
        return $this->user->is_warning;
    }

    /**
     * @return int
     */
    public function getGenderBelongPartner(): int
    {
        return $this->user->userProfile->gender_party;
    }

    /**
     * @return bool
     */
    public function getEmptyContact(): bool
    {
        if ($this->user->userContacts->isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->user->id,
            'memberId' => $this->getMemberId(),
            'isWarning' => $this->getWarning(),
            'mobilePlatform' => $this->getMobilePlatform(),
            'loginId' => $this->user->login_id,
            'status' => $this->getUserStatus(),
            'statusName' => UserStatus::getDescription($this->getUserStatus()),
            'createdAt' => $this->getCreatedAtFormat(),
            'gender' => $this->getGenderBelongPartner(),
            'birthday' => $this->getBirthdayFormat(),
            'occupation' => optional($this->user->userProfile)->occupation,
            'userId' => optional($this->user->userProfile)->user_id,
            'nickname' => optional($this->user->userProfile)->nickname,
            'avatar' => data_get($this->user, 'userProfile.storageFile.file_url'),
            'card' => $this->getCardUrl(),
            'identificationName' => $this->getIdentificationName(),
            'genderName' => Gender::getDescription($this->getGenderBelongPartner()),
            'ticket' => $this->getUserTicket(),
            'point' => $this->getUserPoint(),
            'emptyContact' => $this->getEmptyContact(),
            'registerAt' => $this->getRegisterAtFormat(),
            'becomeMemberAt' => $this->getBecomeMemberAtFormat(),
            'withdrawAt' => $this->getWithdrawAtFormat()
        ];
    }
}
