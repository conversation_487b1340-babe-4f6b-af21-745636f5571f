<?php

namespace Src\Domain\Admin\Models\UserContact;

use Src\Domain\Model;
use App\Eloquent\UserContact as EloquentUserContact;
use Src\Enum\ContactDiv;
use Src\Enum\ContactState;
use Src\Enum\FeedbackStatus;
use Src\Enum\Gender;
use Src\Enum\UserStatus;

/**
* Class UserContact
*
* @package Src\Domain\Admin\Models\UserContact
*/
class UserContact extends Model
{
    /**
     * @var EloquentUserContact
     */
    private $user_contact;

    /**
     * UserContact constructor.
     *
     * @param EloquentUserContact $user_contact
     */
    public function __construct(EloquentUserContact $user_contact)
    {
        $this->user_contact = $user_contact;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->user_contact->id;
    }

  /**
   * @return int
   */
    public function getUserId(): int
    {
      return $this->user_contact->user_id;
    }


    /**
     * @return string
     */
    public function getLoginId(): string
    {
        return isset($this->user_contact->user->login_id) ? $this->user_contact->user->login_id : '';
    }

    /**
     * @return string
     */
    public function getMemberId(): string
    {
        return isset($this->user_contact->user->member_id) ? $this->user_contact->user->member_id : '';
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->user_contact->contact_div;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->user_contact->body;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->user_contact->created_at->format("Y/m/d H:i");
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->user_contact->updated_at->format("Y/m/d H:i");
    }

  /**
   * @return int
   */
    public function getContactStatus(): int
    {
        return $this->user_contact->contact_status;
    }

    /**
     * @return int
     */
    public function getUserStatus(): int
    {
        return ($this->user_contact->user) ? $this->user_contact->user->user_status : null;
    }

    /**
     * @return string|null
     */
    public function getNicknameUser(): ?string
    {
        return ($this->user_contact->userProfile) ? $this->user_contact->userProfile->nickname : null;
    }

    /**
     * @return int|null
     */
    public function getGenderId(): ?int
    {
        return ($this->user_contact->userProfile) ? $this->user_contact->userProfile->gender_party : null;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'userId' => $this->getUserId(),
            'loginId' => $this->getLoginId(),
            'contactStatus' => $this->getContactStatus(),
            'contactStatusName' => ContactState::getDescription($this->getContactStatus()),
            'memberId' => $this->getMemberId(),
            'contactDiv' => $this->getContactDiv(),
            'contactDivName' => ContactDiv::getDescription($this->getContactDiv()),
            'body' => $this->getBody(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt(),
            'nicknameUser' => $this->getNicknameUser(),
            'genderId' => $this->getGenderId(),
            'gender' => Gender::getDescription($this->getGenderId())
        ];
    }
}
