<?php

namespace Src\Domain\Admin\Models\UserContact;

use Src\Domain\FormModel;

/**
* Class UserContactForm
*
* @package Src\Domain\Admin\Models\UserContact
*/
class UserContactForm extends FormModel
{

    /**
    * @var int
    */
    protected $id;

    /**
    * @var int
    */
    protected $contact_div;

    /**
     * @var int
     */
    protected $contact_status;

    /**
    * @var string
    */
    protected $body;

    /**
     * UserContactForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->contact_div = array_get_int($input, 'contact_div');
        $this->body = array_get_string($input, 'body');
        $this->contact_status = array_get_int($input,'contact_status');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->contact_div;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    /**
     * @return int
     */
    public function getContactStatus(): int
    {
        return intval($this->contact_status);
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'contact_div' => $this->getContactDiv(),
            'body' => $this->getBody(),
            'contact_status' => $this->getContactStatus(),
        ];
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'contact_status' => $this->getContactStatus(),
        ];
    }

}
