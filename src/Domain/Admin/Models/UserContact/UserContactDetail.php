<?php

namespace Src\Domain\Admin\Models\UserContact;

use App\Eloquent\UserContact as EloquentUserContact;
use Src\Domain\Model;
use Src\Enum\ContactDiv;
use Src\Enum\ContactState;
use Src\Enum\EnableWarning;
use Src\Enum\Gender;

/**
 * Class UserContactDetail
 *
 * @package Src\Domain\Admin\Models\UserContact
 */
class UserContactDetail extends Model
{
    /**
     * @var EloquentUserContact
     */
    private $user_contact;

    /**
     * UserContact constructor.
     *
     * @param EloquentUserContact $user_contact
     */
    public function __construct(EloquentUserContact $user_contact)
    {
        $this->user_contact = $user_contact;
    }

    /**
     * @return int
     */
    public function getWarning(): int
    {
        if($this->user_contact->feedback){
            return $this->user_contact->feedback->enable_warning;
        }
        return EnableWarning::NORMAL;
    }

    /**
     * @return int
     */
    public function getContactId(): int
    {
        return $this->user_contact->id;
    }

    /**
     * @return bool
     */
    public function hasFeedback(): bool
    {
        if($this->user_contact->feedback){
            return true;
        }
        return false;
    }

    /**
     * @return int|null
     */
    public function getFeedbackId(): ?int
    {
        if($this->user_contact->feedback){
            return $this->user_contact->feedback->id;
        }
        return null;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_contact->user_id;
    }

    /**
     * @return string
     */
    public function getLoginId(): string
    {
        return $this->user_contact->user->login_id;
    }

    /**
     * @return string
     */
    public function getMemberId(): string
    {
        return $this->user_contact->user->member_id;
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->user_contact->contact_div;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->user_contact->body;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->user_contact->created_at->format("Y/m/d H:i");
    }

    /**
     * @return string
     */
    public function getContactAt(): string
    {
        return $this->user_contact->created_at->format("Y/m/d H:i");
    }

    /**
     * @return string
     */
    public function getFeedbackAt(): string
    {
        if ($this->user_contact->feedback) {
            return $this->user_contact->feedback->created_at->format("Y/m/d H:i");
        }
        return '';
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->user_contact->updated_at->format("Y/m/d H:i");
    }

    /**
     * @return int
     */
    public function getContactStatus(): int
    {
        return $this->user_contact->contact_status;
    }

    /**
     * @return int
     */
    public function getUserStatus(): int
    {
        return $this->user_contact->user->user_status;
    }

    /**
     * @return string
     */
    public function getNicknameUser(): ?string
    {
        return $this->user_contact->userProfile->nickname;
    }

    /**
     * @return string
     */
    public function getContactMemberId(): string
    {
        return $this->user_contact->user->member_id;
    }


    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'hasFeedback' => $this->hasFeedback(),
            'contactId' => $this->getContactId(),
            'feedbackId' => $this->getFeedbackId(),
            'contactedUserId' => $this->getUserId(),
            'contactMemberId' => $this->getContactMemberId(),
            'loginId' => $this->getLoginId(),
            'contactStatus' => $this->getContactStatus(),
            'contactStatusName' => ContactState::getDescription($this->getContactStatus()),
            'contactWarning' => $this->getWarning(),
            'contactWarningName' => ($this->getWarning() === EnableWarning::WARNING) ? EnableWarning::getDescription($this->getWarning()) : '',
            'memberId' => $this->getMemberId(),
            'contactDiv' => $this->getContactDiv(),
            'contactDivName' => ContactDiv::getDescription($this->getContactDiv()),
            'content' => $this->getContent(),
            'contactAt' => $this->getContactAt(),
            'feedbackAt' => $this->getFeedbackAt(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt(),
            'nicknameUserContact' => $this->getNicknameUser(),
        ];
    }

}
