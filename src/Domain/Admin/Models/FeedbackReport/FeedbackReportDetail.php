<?php

namespace Src\Domain\Admin\Models\FeedbackReport;

use App\Eloquent\Feedback as EloquentFeedback;
use App\Eloquent\UserReport;
use DB;
use Src\Domain\Model as BaseModel;
use Src\Enum\DateTimeFormat;
use Src\Enum\EnableWarning;
use Src\Enum\Gender;
use Src\Enum\ReportDiv;
use Src\Enum\ReportStatus;
use Src\Enum\TimeReportDiv;

/**
 * Class ReportFeedbackDetail
 *
 * @package Src\Domain\Admin\Models\ReportFeedback
 */
class FeedbackReportDetail extends BaseModel
{

    /**
     * @var EloquentFeedback
     */
    private $feedback;
    /** @var UserReport $user_report*/
    private $user_report;

    /**
     * UserReport constructor.
     *
     * @param EloquentFeedback $feedback
     */
    public function __construct(EloquentFeedback $feedback)
    {
        $this->feedback = $feedback;
        $this->user_report = isset($feedback->feedbackable) ? $feedback->feedbackable : new UserReport();
    }

    /**
     * @return int
     */
    public function getReportId(): int
    {
        return $this->feedback->feedbackable->id;
    }

    /**
     * @return int|null
     */
    public function getFeedbackId(): ?int
    {
        if ($this->feedback) {
            return $this->feedback->id;
        }
        return null;
    }

    /**
     * @return bool
     */
    public function hasFeedback(): bool
    {
        if ($this->feedback) {
            return true;
        }
        return false;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_report->user_id;
    }

    /**
     * @return string
     */
    public function getMemberId(): ?string
    {
        return $this->user_report->user->member_id;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->user_report->user->login_id;
    }

    /**
     * @return int
     */
    public function getReportedUserId(): int
    {
        return $this->feedback->target_user_id;
    }

    /**
     * @return int
     */
    public function getConfirmedUserId(): int
    {
        return $this->user_report->user_id;
    }

    /**
     * @return int|null
     */
    public function getReportedMemberId(): ?int
    {
        if ($this->feedback->targetUser){
            return $this->feedback->targetUser->member_id;
        }
        return null;
    }

    /**
     * @return int|null
     */
    public function getGenderReportedUser(): ?int
    {
        if ($this->feedback->targetUser) {
            $gender = $this->feedback->targetUser->userProfile->gender_party;
            return $gender;
        }
        return null;
    }

    /**
     * @return string
     */
    public function getNicknameReportedUser(): ?string
    {
        if ($this->feedback->targetUser) {
            return $this->feedback->targetUser->userProfile->nickname;
        }
        return '';
    }

    /**
     * @return string
     */
    public function getNicknameConfirmedUser(): string
    {
        if ($this->user_report->user->userProfile) {
            return $this->user_report->user->userProfile->nickname;
        }
        return '';
    }

    /**
     * @return int
     */
    public function getWarning(): int
    {
        return $this->feedback->enable_warning;

    }

    /**
     * @return int
     */
    public function getReportStatus(): int
    {
        return $this->user_report->report_status;
    }

    /**
     * @return int
     */
    public function getReportDiv(): int
    {
        return $this->user_report->report_div;
    }

    /**
     * @return string
     */
    public function getReportContent(): string
    {
        return $this->user_report->content;
    }

    /**
     * @return string
     */
    public function getFeedbackContent(): string
    {
        return $this->feedback->content;
    }

    /**
     * @return int|null
     */
    public function getPartyId(): ?int
    {
        if($this->user_report){
            return $this->user_report->party_id;
        }
        return null;
    }

    /**
     * @return string|null
     */
    public function getPartyTime(): ?string
    {
        if($this->user_report->partyHistory){
            $started_at = $this->user_report->partyHistory->start_at ? strtotime($this->user_report->partyHistory->start_at) : strtotime($this->user_report->partyHistory->created_at);
            $ended_at = $this->user_report->partyHistory->ended_at ? strtotime($this->user_report->partyHistory->ended_at) : strtotime($this->user_report->partyHistory->ended_at);
            $party_time = $ended_at - $started_at;

            return intval($party_time / 60);
        }
       return null;
    }

    /**
     * @return int
     */
    public function getTimeReportDiv(): int
    {
        return $this->user_report->time_report_div;
    }

    /**
     * @return string|null
     */
    public function getTimePartyStart(): ?string
    {
        if($this->user_report->partyHistory){
            return $this->user_report->partyHistory->created_at->format('Y年m月d日 H:i');
        }
        return null;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return format_date_time($this->user_report->created_at, DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function getSentAtFormat(): string
    {
        return $this->feedback->sent_at->format('Y年m月d日 h:i');
    }

    /**
     * @return int
     */
    public function getGenderId(): int
    {
        return $this->user_report->user->userProfile->gender_party;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return $this->user_report->created_at->format('Y年m月d日 h:i');
    }

    /**
     * @return \Carbon\Carbon|null
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->user_report->updated_at;
    }

    /**
     * @param $gender
     * @return array
     */
    public function getMemberParty($gender): array
    {
        if($this->user_report->partyHistory){
            $users_id = $this->user_report->partyHistory->participants()
                ->pluck('user_id')->toArray();
            $user_profile = DB::table('users')
                ->join('user_profiles', 'user_profiles.user_id', '=', 'users.id')
                ->whereIn('user_profiles.user_id', $users_id)
                ->where('user_profiles.gender_party', '=', $gender)
                ->get();
            $attributes = [];
            foreach ($user_profile as $user) {
                $attributes[] = [
                    'userId' => $user->user_id,
                    'memberId' => $user->member_id,
                    'nickname' => $user->nickname
                ];
            }
            return $attributes;
        }
        return [];
    }

    /**
     * @return array
     */
    public function getMemberIsMale(): array
    {
        return $this->getMemberParty(Gender::MALE);
    }

    /**
     * getMemberIsFemale
     *
     * @return array
     */
    public function getMemberIsFemale(): array
    {
        return $this->getMemberParty(Gender::FEMALE);
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'sendId' => $this->getReportId(),
            'feedbackId' => $this->getFeedbackId(),
            'hasFeedback' => $this->hasFeedback(),
            'warning' => $this->getWarning(),
            'warningName' => EnableWarning::getDescription($this->getWarning()),
            'reportedUserId' => $this->getReportedUserId(),
            'confirmedUserId' => $this->getConfirmedUserId(),
            'reportedMemberId' => $this->getReportedMemberId(),
            'confirmedMemberId' => $this->getMemberId(),
            'nicknameReportedUser' => $this->getNicknameReportedUser(),
            'nicknameConfirmedUser' => $this->getNicknameConfirmedUser(),
            'memberIsMale' => $this->getMemberIsMale(),
            'memberIsFemale' => $this->getMemberIsFemale(),
            'email' => $this->getEmail(),
            'reportStatus' => $this->getReportStatus(),
            'reportStatusName' => ReportStatus::getDescription($this->getReportStatus()),
            'reportDiv' => $this->getReportDiv(),
            'reportDivName' => ReportDiv::getDescription($this->getReportDiv()),
            'reportContent' => $this->getReportContent(),
            'feedbackContent' => $this->getFeedbackContent(),
            'feedbackAt' => $this->getSentAtFormat(),
            'reportDate' => $this->getCreatedAtFormat(),
            'timePartyStart' => $this->getTimePartyStart(),
            'partyTime' => $this->getPartyTime(),
            'timeReport' => TimeReportDiv::getDescription($this->getTimeReportDiv()),
            'genderId' => $this->getGenderId(),
            'createdAt' => $this->getCreatedAtFormat(),
        ];
    }
}
