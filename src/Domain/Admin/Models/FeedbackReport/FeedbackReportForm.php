<?php


namespace Src\Domain\Admin\Models\FeedbackReport;

use App\Eloquent\Feedback;
use App\Eloquent\UserReport;
use Carbon\Carbon;
use Src\Domain\FormModel;
use Src\Enum\FeedbackDiv;
use Src\Enum\SendStatus;

/**
 * Class ReportFeedbackForm
 *
 * @package Src\Domain\Admin\Models\ReportFeedback
 */
class FeedbackReportForm extends FormModel
{
    /** @var int|null */
    protected $enable_warning;
    /** @var string */
    protected $content;
    /** @var int */
    protected $send_status;
    /** @var string|Carbon */
    protected $sent_at;
    /** @var string|Carbon */
    protected $send_schedule_at;
    /** @var int */
    protected $feedback_div;
    /** @var int */
    protected $foreign_id;
    /** @var int */
    protected $target_user_id;

    protected $fields = [
        'enable_warning' => 'string',
        'content' => 'string',
        'send_status' => 'integer',
        'sent_at' => 'string',
        'send_schedule_st' => 'string',
        'feedback_div' => 'integer',
        'foreign_id' => 'string',
        'target_user_id' => 'string',
    ];

    /**
     * FeedbackReportForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->enable_warning = (int) array_get_string($input, 'enable_warning');
        $this->content = array_get_string($input,'content');
        $this->sent_at = array_get_string($input,'sent_at');
        $this->target_user_id = (int) array_get_int($input,'target_user_id');
        $this->foreign_id = (int) array_get_int($input,'foreign_id');
    }

    /**
     * @return string
     */
    public function getSentAt(): string
    {
        return Carbon::parse($this->sent_at);
    }

    /**
     * @return int
     */
    public function getTargetUserId(): int
    {
        return $this->target_user_id;
    }

    /**
     * @return int
     */
    public function getForeignId(): int
    {
        return $this->foreign_id;
    }

    /**
     * @return string
     */
    public function getForeignTable(): string
    {
        return (new UserReport())->getMorphClass();
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'enable_warning' => $this->enable_warning,
            'content' => $this->content,
            'foreign_table' => $this->getForeignTable(),
            'foreign_id' => $this->getForeignId(),
            'target_user_id' => $this->getTargetUserId(),
            'sent_at' => $this->getSentAt(),
            'send_status' => SendStatus::WAIT_SEND,
        ];
    }

}
