<?php

namespace Src\Domain\Admin\Models\UserFriend;

use App\Eloquent\MessageConversation;
use App\Eloquent\UserFriend;
use Carbon\Carbon;
use Src\Domain\Model as BaseModel;
use Src\Enum\DateTimeFormat;
use Src\Enum\Gender;
use Src\Enum\UserStatus;

/**
 * Class UserFriendDetail
 *
 * @package Src\Domain\Admin\Models\UserFriend
 */
class UserFriendDetail extends BaseModel
{
    /** @var UserFriend $user_friend */
    private $user_friend;

    /**
     * constructor
     *
     * UserFriendDetail constructor.
     * @param UserFriend $user_friend
     */
    public function __construct(UserFriend $user_friend)
    {
        $this->user_friend = $user_friend;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_friend->user_id;
    }

    /**
     * @return int
     */
    public function getFriendId()
    {
        return $this->user_friend->friend_id;
    }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->user_friend->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function getPartyId(): ?string
    {
        return ($this->user_friend->party_id !== null) ? strval($this->user_friend->party_id) : 'ヌル';
    }

    /**
     * @return string
     */
    public function getFriendMemberId(): string
    {
        return isset($this->user_friend->user->member_id) ? $this->user_friend->user->member_id : '';
    }

    /**
     * @return string|null
     */
    public function getNickName(): ?string
    {
        return isset($this->user_friend->user->userProfile) ? $this->user_friend->user->userProfile->nickname : null;
    }

    /**
     * @return int|null
     */
    public function getUserStatus(): ?int
    {
        return isset($this->user_friend->user->user_status) ? $this->user_friend->user->user_status : null;
    }

    /**
     * @return Carbon|null
     */
    public function getBirthDay(): ?Carbon
    {
        $date = isset($this->user_friend->user->userProfile) ? $this->user_friend->user->userProfile->birthday : null;
        return Carbon::parse($date);
    }

    /**
     * @return string|null
     */
    public function gerBirthDayFormat(): ?string
    {
        return format_date_time($this->getBirthDay(), DateTimeFormat::DATE());
    }

    /**
     * @return Carbon|null
     */
    public function getRegisterAt(): ?Carbon
    {
        return isset($this->user_friend->user->registered_at) ? $this->user_friend->user->registered_at : null;
    }

    /**
     * @return string|null
     */
    public function getRegisterAtFormat(): ?string
    {
        return format_date_time($this->getRegisterAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return Carbon|null
     */
    public function getWithdrawalAt(): ?Carbon
    {
        return isset($this->user_friend->user->userWithdrawal) ? $this->user_friend->user->userWithdrawal->withdrawal_at : null;
    }

    /**
     * @return string|null
     */
    public function getWithdrawAtFormat(): ?string
    {
        return format_date_time($this->getWithdrawalAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return int|null
     */
    public function getGenderBelongPartner(): ?int
    {
        if (isset($this->user_friend->user->userProfile)) {
            return $this->user_friend->user->userProfile->gender_party;
        }
        return null;
    }

    /**
     * @return string
     */
    public function getGenderName(): string
    {
        return Gender::getDescription($this->getGenderBelongPartner());
    }

    /**
     * @return bool
     */
    public function checkMessage(): bool
    {
        if (isset($this->user_friend->user)) {
            $message_conversations = $this->user_friend->user->messageConversations()->get();

            /** @var MessageConversation $message_conversation */
            foreach ($message_conversations as $message_conversation) {
                if ($message_conversation->messageParticipants()->where('user_id', $this->getUserId())->exists()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     *
     *
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'userId' => $this->getUserId(),
            'friendId' => $this->getFriendId(),
            'hasMessage' => $this->checkMessage(),
            'gender' => $this->getGenderBelongPartner(),
            'genderName' => $this->getGenderName(),
            'status' => $this->getUserStatus(),
            'statusName' => UserStatus::getDescription($this->getUserStatus()),
            'nickname' => $this->getNickName(),
            'memberId' => $this->getFriendMemberId(),
            'partyId' => $this->getPartyId(),
            'birthday' => $this->gerBirthDayFormat(),
            'createdAt' => $this->getCreatedAtFormat(),
            'registerAt' => $this->getRegisterAtFormat(),
            'withdrawAt' => $this->getWithdrawAtFormat()
        ];
    }
}
