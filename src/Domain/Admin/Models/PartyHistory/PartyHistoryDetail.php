<?php

namespace Src\Domain\Admin\Models\PartyHistory;

use App\Eloquent\PartyHistory as EloquentPartyHistory;
use App\Eloquent\StorageFile;
use App\Eloquent\User;
use DB;
use Src\Domain\Model;
use Src\Enum\Gender;
use Src\Enum\PartyType;

/**
 * Class PartyHistoryDetail
 *
 * @package Src\Domain\Admin\Models\PartyHistory
 */
class PartyHistoryDetail extends Model
{
    /** @var EloquentPartyHistory */
    private $party_history;
    /** @var User */
    private $creator;

    /**
     * PartyHistoryDetail constructor.
     *
     * @param EloquentPartyHistory $party_history
     */
    public function __construct(EloquentPartyHistory $party_history)
    {
        $this->party_history = $party_history;
        $this->creator = isset($party_history->creator) ? $party_history->creator : new User();
    }


    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->party_history->id;
    }

    /**
     * @return int
     */
    public function getGroupType(): int
    {
        return $this->party_history->group_type;
    }

    /**
     * @return int|null
     */
    public function getPartyStatus(): ?int
    {
        return isset($this->party_history->party) ? $this->party_history->party->party_status : null;
    }

    /**
     * @return int
     */
    public function getCreatorId(): int
    {
        return $this->party_history->creator_id;
    }

    /**
     * @return string|null
     */
    public function getCreator(): ?string
    {
        if ($this->creator->userProfile) {
            return $this->creator->userProfile->nickname;
        }
        return null;
    }

    /**
     * @return string|null
     */
    public function getCreatorMemberId(): ?string
    {
        return $this->creator->member_id;
    }

    /**
     * @param $gender
     * @return array
     */
    public function getMemberParty($gender):array
    {
        $users_id = $this->party_history->participants()
            ->pluck('user_id')->toArray();
        $user_profile = DB::table('users')
            ->join('user_profiles', 'user_id', '=', 'users.id')
            ->whereIn('user_profiles.user_id', $users_id)
            ->where('user_profiles.gender_party', '=', $gender)
            ->get();
        $attributes = [];
        foreach ($user_profile as $user) {
            $attributes[] = [
                'userId' => $user->user_id,
                'memberId' => $user->member_id,
                'nickname' => $user->nickname
            ];
        }
        return $attributes;
    }

    /**
     * @return array
     */
    public function getMemberIsMale(): array
    {
        return $this->getMemberParty(Gender::MALE);
    }

    /**
     * @return array
     */
    public function getMemberIsFemale(): array
    {
        return $this->getMemberParty(Gender::FEMALE);
    }

    /**
     * @return string|null
     */
    public function getPartyTime(): ?string
    {
        $started_at = strtotime($this->party_history->start_at);
        $ended_at = strtotime($this->party_history->ended_at);
        $party_time = $ended_at -  $started_at;
        return intval($party_time / 60);
    }

    /**
     * @return int
     */
    public function getPartyType(): int
    {
        return $this->party_history->party_type;
    }

    /**
     * @return string
     */
    public function getPartyTypeName(): string
    {
       return in_array($this->getPartyType(), PartyType::getValues()) ? PartyType::getDescription($this->getPartyType()) : '';
    }

    /**
     * @return int
     */
    public function getInviteMemberType(): int
    {
        return $this->party_history->invite_member_type;
    }

    /**
     * @return \Carbon\Carbon|null
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->party_history->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return $this->party_history->created_at->format('Y年m月d日 H:i');
    }

    /**
     * @return bool
     */
    public function hasVideo(): bool
    {
        if (isset($this->party_history->partyHistoryRecord->record_file_id)) {
            /** @var StorageFile $storage_file */
            $storage_file = $this->party_history->partyHistoryRecord->recordFile;
            if (isset($storage_file->file_url)) {
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * @return string
     */
    public function getVideoURL(): string
    {
        $url = "";
        if ($this->hasVideo()) {
            $url = $this->party_history->partyHistoryRecord->recordFile->file_url;
        }
        return $url;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'hasVideo' => $this->hasVideo(),
            'videoUrl' => $this->getVideoURL(),
            'groupType' => $this->getGroupType(),
            'partyType' => $this->getPartyType(),
            'partyTypeName' => $this->getPartyTypeName(),
            'partyStatus' => $this->getPartyStatus(),
            'creatorId' => $this->getCreatorId(),
            'creatorMemberId' => $this->getCreatorMemberId(),
            'creator' => $this->getCreator(),
            'partyTime' => $this->getPartyTime(),
            'inviteMemberType' => $this->getInviteMemberType(),
            'createdAt' => $this->getCreatedAt(),
            'createdAtFormat' => $this->getCreatedAtFormat(),
            'memberIsMale' => $this->getMemberIsMale(),
            'memberIsFemale' => $this->getMemberIsFemale()
        ];
    }
}
