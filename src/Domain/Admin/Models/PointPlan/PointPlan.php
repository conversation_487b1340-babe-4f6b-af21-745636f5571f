<?php

namespace Src\Domain\Admin\Models\PointPlan;

use Src\Domain\Model;
use App\Eloquent\PointPlan as EloquentPointPlan;

/**
* Class PointPlan
*
* @package Src\Domain\Admin\Models\PointPlan
*/
class PointPlan extends Model
{
    /**
     * @var EloquentPointPlan
     */
    private $point_plan;

    /**
     * PointPlan constructor.
     *
     * @param EloquentPointPlan $point_plan
     */
    public function __construct(EloquentPointPlan $point_plan)
    {
        $this->point_plan = $point_plan;
    }
    
    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->point_plan->id;
    }

    /**
     * @return string
     */
    public function getIosPackageId(): string
    {
        return $this->point_plan->ios_package_id;
    }

    /**
     * @return string
     */
    public function getAndroidPackageId(): string
    {
        return $this->point_plan->android_package_id;
    }

    /**
     * @return int
     */
    public function getPoint(): int
    {
        return $this->point_plan->point;
    }

    /**
     * @return int
     */
    public function getMoney(): int
    {
        return $this->point_plan->money;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'iosPackageId' => $this->getIosPackageId(),
            'androidPackageId' => $this->getAndroidPackageId(),
            'point' => $this->getPoint(),
            'money' => $this->getMoney()
        ];
    }
}
