<?php

namespace Src\Domain\Admin\Models\FeedbackGuestContact;

use Src\Domain\Model;
use App\Eloquent\Feedback as EloquentFeedback;
use Src\Enum\ContactDiv;
use Src\Enum\ContactStatus;
use Src\Enum\DateTimeFormat;

class FeedbackGuestContactDetail extends Model
{
    private $feedback;

    public function __construct(EloquentFeedback $feedback)
    {
        $this->feedback = $feedback;
    }

    public function getGuestContactId(): int
    {
        return $this->feedback->guestContact->id;
    }

    public function getGuestContactName(): string
    {
        return $this->feedback->guestContact->name;
    }

    public function getGuestContactEmail(): string
    {
        return $this->feedback->guestContact->email;
    }

    public function getContactDiv(): int
    {
        return $this->feedback->guestContact->contact_div;
    }

    public function getContactContent(): string
    {
        return $this->feedback->guestContact->body;
    }

    public function getContactStatus(): int
    {
        return $this->feedback->guestContact->contact_status;
    }

    public function getContactCreatedAt(): string
    {
        return format_date_time($this->feedback->guestContact->created_at, DateTimeFormat::DATETIME());
    }

    public function getFeedbackContent(): string
    {
        return $this->feedback->content;
    }

    public function getFeedbackId(): int
    {
        return $this->feedback->id;
    }

    public function getFeedbackAt(): string
    {
        return format_date_time($this->feedback->created_at, DateTimeFormat::DATETIME());
    }

    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    public function toComponentFormValue(): array
    {
        return [
            'contactId' => $this->getGuestContactId(),
            'guestContactName' => $this->getGuestContactName(),
            'guestContactEmail' => $this->getGuestContactEmail(),
            'guestContactDiv' => ContactDiv::getDescription($this->getContactDiv()),
            'guestContactStatus' => ContactStatus::getDescription($this->getContactStatus()),
            'guestContactContent' => $this->getContactContent(),
            'contactCreatedAt' => $this->getContactCreatedAt(),
            'feedbackId' => $this->getFeedbackId(),
            'feedbackContent' => $this->getFeedbackContent(),
            'feedbackAt' => $this->getFeedbackAt()
        ];
    }


}
