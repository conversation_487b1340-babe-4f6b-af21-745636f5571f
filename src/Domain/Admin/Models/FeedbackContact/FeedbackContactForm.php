<?php

namespace Src\Domain\Admin\Models\FeedbackContact;

use App\Eloquent\Feedback;
use App\Eloquent\UserContact;
use Carbon\Carbon;
use Src\Domain\FormModel;
use Src\Enum\SendStatus;

class FeedbackContactForm extends FormModel
{
    /** @var int|null */
    protected $enable_warning;
    /** @var string */
    protected $content;
    /** @var int */
    protected $send_status;
    /** @var string|Carbon */
    protected $sent_at;
    /** @var string|Carbon */
    protected $send_schedule_at;
    /** @var int */
    protected $feedback_div;
    /** @var int */
    protected $foreign_id;
    /** @var int */
    protected $target_user_id;

    protected $fields = [
        'enable_warning' => 'string',
        'content' => 'string',
        'send_status' => 'integer',
        'sent_at' => 'string',
        'send_schedule_st' => 'string',
        'feedback_div' => 'integer',
        'foreign_id' => 'string',
        'target_user_id' => 'integer',
    ];

    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->enable_warning = array_get_int($input, 'enable_warning');
        $this->content = array_get_string($input,'content');
        $this->sent_at = array_get_string($input,'sent_at');
        $this->foreign_id = (int) array_get_string($input,'foreign_id');
    }

    /**
     * @return string
     */
    public function getSentAt(): string
    {
        return Carbon::parse($this->sent_at);
    }

    /**
     * @return string
     */
    public function getForeignTable(): string
    {
        return (new UserContact())->getMorphClass();
    }

    /**
     * @return int
     */
    public function getForeignId(): int
    {
        return $this->foreign_id;
    }

    /**
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'enable_warning' => $this->enable_warning,
            'content' => $this->content,
            'feedback_div' => $this->feedback_div,
            'foreign_id' => $this->getForeignId(),
            'foreign_table' => $this->getForeignTable(),
            'sent_at' => $this->getSentAt(),
            'send_status' => SendStatus::WAIT_SEND,
        ];
    }

}
