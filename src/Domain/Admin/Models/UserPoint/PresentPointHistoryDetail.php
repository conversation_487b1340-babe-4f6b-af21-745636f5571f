<?php

namespace Src\Domain\Admin\Models\UserPoint;

use App\Eloquent\PointHistory;
use Carbon\Carbon;
use Src\Domain\Model;
use Src\Enum\DateTimeFormat;
use Src\Enum\PresentPointDiv;

/**
 * Class PresentPointHistoryDetail
 *
 * @package Src\Domain\Admin\Models\UserPoint
 */
class PresentPointHistoryDetail extends Model
{
    /** @var PointHistory $point_history */
    private $point_history;

    /**
     * constructor
     *
     * PresentPointHistoryDetail constructor.
     * @param PointHistory $point_history
     */
    public function __construct(PointHistory $point_history)
    {
        $this->point_history = $point_history;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->point_history->id;
    }

    /**
     * @return null|string
     */
    public function getUserReceiveName(): ?string
    {
        return ($this->point_history->receiveUser !== null) ? $this->point_history->receiveUser->userProfile->nickname : '';
    }

    /**
     * @return string
     */
    public function getMemeberIdReceive(): ?string
    {
        return ($this->point_history->receiveUser !== null) ? $this->point_history->receiveUser->member_id : '';
    }

    /**
     * @return int
     */
    public function getPresentPoint(): string
    {
        return number_format($this->point_history->point).' pt';
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->point_history->user_id;
    }

    /**
     * @return null|int
     */
    public function getPartyId(): ?string
    {
        return ($this->point_history->party_id !== null) ? strval($this->point_history->party_id) : 'ヌル';
    }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->point_history->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        //
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'memberIdReceive' => $this->getMemeberIdReceive(),
            'userReceive' => $this->getUserReceiveName(),
            'point' => $this->getPresentPoint(),
            'partyId' => $this->getPartyId(),
            'userId' => $this->getUserId(),
            'createdAt' => $this->getCreatedAtFormat(),
        ];
    }
}
