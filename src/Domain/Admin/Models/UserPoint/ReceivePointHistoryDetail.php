<?php


namespace Src\Domain\Admin\Models\UserPoint;


use App\Eloquent\PointHistory;
use Carbon\Carbon;
use Src\Enum\DateTimeFormat;

class ReceivePointHistoryDetail
{
    /** @var PointHistory $point_history */
    private $point_history;

    /**
     * constructor
     *
     * PresentPointHistoryDetail constructor.
     * @param PointHistory $point_history
     */
    public function __construct(PointHistory $point_history)
    {
        $this->point_history = $point_history;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->point_history->id;
    }

    /**
     * @return null|string
     */
    public function getUserPresentName(): ?string
    {
        return ($this->point_history->presentUser !== null) ? $this->point_history->presentUser->userProfile->nickname : '';
    }

    /**
     * @return string
     */
    public function getMemeberIdPresent(): ?string
    {
        return ($this->point_history->presentUser !== null) ? $this->point_history->presentUser->member_id : '';
    }

    /**
     * @return int
     */
    public function getReceivePoint(): string
    {
        return number_format($this->point_history->point).' pt';
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->point_history->user_id;
    }

    /**
     * @return null|int
     */
    public function getPartyId(): ?string
    {
        return ($this->point_history->party_id !== null) ? strval($this->point_history->party_id) : 'ヌル';
    }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->point_history->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        //
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'memberIdPresent' => $this->getMemeberIdPresent(),
            'userPresent' => $this->getUserPresentName(),
            'point' => $this->getReceivePoint(),
            'partyId' => $this->getPartyId(),
            'userId' => $this->getUserId(),
            'createdAt' => $this->getCreatedAtFormat(),
        ];
    }
}
