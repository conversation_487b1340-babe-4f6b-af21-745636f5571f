<?php

namespace Src\Domain\Admin\Models\UserPoint;
use App\Eloquent\PointHistory;
use Carbon\Carbon;
use Src\Domain\Model;
use Src\Enum\DateTimeFormat;

/**
 * Class BuyPointHistoryDetail
 *
 * @package Src\Domain\Admin\Models\UserPoint
 */
class BuyPointHistoryDetail extends Model
{
    /** @var PointHistory $buy_point_history */
    private $buy_point_history;

    /**
     * constructor
     *
     * BuyPointHistoryDetail constructor.
     * @param PointHistory $point_history
     */
    public function __construct(PointHistory $point_history)
    {
        $this->buy_point_history = $point_history;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->buy_point_history->id;
    }

    /**
     * @return string
     */
    public function getBuyPoint(): string
    {
        return number_format($this->buy_point_history->point).' pt';
    }

    /**
     * @return string
     */
   public function getPrice(): string
   {
       $price = 10 * $this->buy_point_history->point;
       return number_format($price).'  ¥';
   }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->buy_point_history->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'point' => $this->getBuyPoint(),
            'price' => $this->getPrice(),
            'created_at' => $this->getCreatedAtFormat(),
        ];
    }
}
