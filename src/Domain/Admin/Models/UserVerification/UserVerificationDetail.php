<?php

namespace Src\Domain\Admin\Models\UserVerification;

use App\Eloquent\User;
use App\Eloquent\UserProfile;
use App\Eloquent\UserVerification as EloquentUserVerification;
use Src\Domain\Model as BaseModel;
use Src\Enum\DateTimeFormat;
use Src\Enum\Gender;
use Src\Enum\IdentificationType;
use Src\Traits\FormatDate;

/**
 * Class UserVerificationDetail
 *
 * @package Src\Domain\Admin\Models\UserVerification
 */
class UserVerificationDetail extends BaseModel
{
    use FormatDate;
    /** @var EloquentUserVerification  */
    private $user_verification;
    /** @var User $user */
    private $user;
    /** @var UserProfile */
    private $user_profile;

    /**
     * UserVerificationDetail constructor.
     * @param EloquentUserVerification $user_verification
     */
    public function __construct(EloquentUserVerification $user_verification)
    {
        $this->user_verification = $user_verification;
        $this->user = isset($user_verification->user) ? $user_verification->user : new User();
        $this->user_profile = isset($this->user->userProfile) ? $this->user->userProfile : new UserProfile();
    }

    /**
     * @return string
     */
    public function getIdentificationName(): string
    {
        return IdentificationType::getDescription($this->user_verification->identification_type);
    }

    /**
     * @return string
     */
    public function getBirthDayUser(): ?string
    {
        return $this->user_profile->birthday ;
    }

    /**
     * @return string
     */
    public function getNickname(): ?string
    {
        return $this->user_profile->nickname;
    }

    /**
     * @return int
     */
    public function getGender(): ?int
    {
        return $this->user_profile->gender;
    }

    /**
     * @return string|null
     */
    public function getMemberId(): ?string
    {
        return $this->user->member_id;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return format_date_time($this->user_verification->created_at, DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string {
        return format_date_time($this->user_verification->created_at, DateTimeFormat::DATE());
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): ?string
    {
        return format_date_time($this->user_verification->updated_at, DateTimeFormat::DATETIME());
    }

    /**
     * Get Component Value
     *
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Get Component Form Value
     *
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'userId' => $this->user_verification->user_id,
            'identificationType' => $this->user_verification->identification_type,
            'identificationName' => $this->getIdentificationName(),
            'birthday' => $this->getBirthDayUser(),
            'nickname' => $this->getNickname(),
            'memberId' => $this->getMemberId(),
            'gender' => Gender::getDescription($this->getGender()),
            'image' => data_get($this->user_verification, 'storageFile.file_url'),
            'createdAt' => $this->getCreatedAt(),
            'createdAtFormat' => $this->getCreatedAtFormat(),
            'updatedAt' => $this->getUpdatedAt()
        ];
    }
}
