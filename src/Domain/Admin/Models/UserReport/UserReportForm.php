<?php

namespace Src\Domain\Admin\Models\UserReport;

use Src\Domain\FormModel;

/**
* Class UserReportForm
*
* @package Src\Domain\Admin\Models\UserReport
*/
class UserReportForm extends FormModel
{

    /**
    * @var int
    */
    protected $id;

    /**
    * @var int
    */
    protected $user_id;

    /**
    * @var int
    */
    protected $report_div;

    /**
    * @var string
    */
    protected $content;

    /**
     * @var int
     */
    protected $report_status;

    /**
     * UserReportForm constructor.
     * @param array $input
     */
    public function __construct(array $input = [])
    {
        $this->user_id = array_get_int($input, 'user_id');
        $this->report_div = array_get_int($input, 'report_div');
        $this->content = array_get_string($input, 'content');
        $this->report_status = array_get_int($input, 'report_status');
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * @return int
     */
    public function getReportDiv(): int
    {
        return $this->report_div;
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->content;
    }

    public function getReportStatus(): int
    {
        return $this->report_status;
    }

    /**
     * common Attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'report_div' => $this->getReportDiv(),
            'content' => $this->getContent()
        ];
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }

    /**
     * update Attributes
     *
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'report_status' => $this->getReportStatus()
        ];
    }

}
