<?php

namespace Src\Domain\Admin\Models\UserReport;

use App\Eloquent\Feedback;
use App\Eloquent\User;
use App\Eloquent\UserContact;
use App\Eloquent\UserReport;
use Carbon\Carbon;
use DB;
use Src\Domain\Model;
use Src\Enum\DateTimeFormat;
use Src\Enum\EnableWarning;
use Src\Enum\Gender;
use Src\Enum\ReportDiv;
use Src\Enum\ReportStatus;
use Src\Enum\TimeReportDiv;

/**
 * Class UserReportDetail
 *
 * @package Src\Domain\Admin\Models\UserReport
 */
class UserReportDetail extends Model
{
    /** @var UserReport $user_report */
    private $user_report;
    /** @var Feedback $feedback_report */
    private $feedback_report;
    /** @var User $reported_user */
    private $reported_user;
    /** @var User $confirmed_user */
    private $confirmed_user;

    /**
     * constructor
     *
     * UserReportDetail constructor.
     * @param UserReport $user_report
     */
    public function __construct(UserReport $user_report)
    {
        $this->user_report = $user_report;
        isset($user_report->feedback) ? $this->feedback_report = $user_report->feedback : $this->feedback_report = new Feedback();
        isset($user_report->user) ? $this->confirmed_user = $user_report->user : $this->confirmed_user = new User();
        isset($user_report->reportedUser) ? $this->reported_user = $user_report->reportedUser : $this->reported_user = new User();
    }

    /**
     * @return int|null
     */
    public function getWarning(): ?int
    {
        return isset($this->feedback_report->enable_warning) ? $this->feedback_report->enable_warning : EnableWarning::NORMAL;
    }

    /**
     * @return string
     */
    public function getSendId(): string
    {
        return $this->user_report->id;
    }

    /**
     * @return int|null
     */
    public function getFeedbackId(): ?int
    {
        return $this->feedback_report->id;
    }

    /**
     * @return bool
     */
    public function hasFeedback(): bool
    {
        return isset($this->feedback_report->id) ? true : false;
    }

    /**
     * @return int
     */
    public function getUserId(): int
    {
        return $this->user_report->user_id;
    }

    /**
     * @return string
     */
    public function getMemberId(): ?string
    {
        return $this->user_report->user->member_id;
    }

    /**
     * @return string|null
     */
    public function getNicknameConfirmedUser(): ?string
    {
        return $this->confirmed_user->userProfile->nickname;
    }

    /**
     * @return string|null
     */
    public function getUserReportedId(): ?string
    {
        return $this->user_report->reported_user_id;
    }

    /**
     * @return int
     */
    public function getPartyId(): int
    {
        return $this->user_report->party_id;
    }

    /**
     * @return int
     */
    public function getReportStatus(): int
    {
        return $this->user_report->report_status;
    }

    /**
     * @return string
     */
    public function getReportStatusName(): string
    {
        return ReportStatus::getDescription($this->getReportStatus());
    }

    /**
     * @return int
     */
    public function getReportDiv(): int
    {
        return $this->user_report->report_div;
    }

    /**
     * @return string
     */
    public function getReportDivName(): string
    {
        return ReportDiv::getDescription($this->user_report->report_div);
    }

    /**
     * @return int|null
     */
    public function getReportWaring(): ?int
    {
        return $this->feedback_report->enable_warning;
    }

    /**
     * @return string
     */
    public function getReportWarningName(): string
    {
        if ($this->getReportWaring()) {
            return EnableWarning::getDescription($this->getReportWaring());
        }
        return EnableWarning::getDescription(EnableWarning::NORMAL);
    }

    /**
     * @return string
     */
    public function getContent(): string
    {
        return $this->user_report->content;
    }

    /**
     * @return Carbon
     */
    public function getCreatedAt(): Carbon
    {
        return $this->user_report->created_at;
    }

    /**
     * @return string
     */
    public function getReportDateFormat(): string
    {
        return $this->user_report->created_at->format('Y/m/d H:i');
    }

    /**
     * @return string
     */
    public function getFeedbackAtFormat(): string
    {
        return isset($this->feedback_report->created_at) ? $this->feedback_report->created_at->format('Y/m/d H:i') : '';
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string|null
     */
    public function getTimePartyStart(): ?string
    {
        if($this->user_report->partyHistory){
            return $this->user_report->partyHistory->created_at->format('Y年m月d日 H:i');
        }
        return null;
    }

    /**
     * @return int
     */
    public function getTimeReportDiv(): int
    {
        return $this->user_report->time_report_div;
    }

    /**
     * @return int|null
     */
    public function getPartyTime(): ?int
    {
        if($this->user_report->partyHistory){
            $started_at = $this->user_report->partyHistory->start_at ? strtotime($this->user_report->partyHistory->start_at) : strtotime($this->user_report->partyHistory->created_at);
            $ended_at = strtotime($this->user_report->partyHistory->ended_at) ;
            $party_time = $ended_at - $started_at;

            return intval($party_time / 60);
        }
        return null;
    }

    /**
     * @param $gender
     * @return array
     */
    public function getMemberParty($gender)
    {
        if($this->user_report->partyHistory){
            $users_id = $this->user_report->partyHistory->participants()
                ->pluck('user_id')->toArray();
            $user_profile = DB::table('users')
                ->join('user_profiles', 'user_profiles.user_id', '=', 'users.id')
                ->whereIn('user_profiles.user_id', $users_id)
                ->where('user_profiles.gender_party', '=', $gender)
                ->get();
            $attributes = [];
            foreach ($user_profile as $user) {
                $attributes[] = [
                    'userId' => $user->user_id,
                    'memberId' => $user->member_id,
                    'nickname' => $user->nickname
                ];
            }
            return $attributes;
        }
        return [];
    }

    /**
     * @return array
     */
    public function getMemberIsMale(): array
    {
        return $this->getMemberParty(Gender::MALE);
    }

    /**
     * @return array
     */
    public function getMemberIsFemale(): array
    {
        return $this->getMemberParty(Gender::FEMALE);
    }

    /**
     * @return int|null
     */
    public function getGenderReportedId(): ?int
    {
        return $this->reported_user->userProfile->gender_party;
    }

    /**
     * @return string|null
     */
    public function getNicknameReportedUser(): ?string
    {
        return $this->reported_user->userProfile->nickname;
    }

    /**
     * @return string|null
     */
    public function getReportedMemberId(): ?string
    {
        return $this->reported_user->member_id;
    }

    /**
     * @return int|null
     */
    public function getGender(): ?int
    {
        return $this->confirmed_user->userProfile->gender_party;
    }

    /**
     * @return string| null
     */
    public function getUserMemberId(): ?string
    {
        return $this->confirmed_user->member_id;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'reportWarning' => $this->getWarning(),
            'reportWarningName' => ($this->getWarning() === EnableWarning::WARNING) ? EnableWarning::getDescription($this->getWarning()) : '',
            'sendId' => $this->getSendId(),
            'feedbackId' => $this->getFeedbackId(),
            'hasFeedback' => $this->hasFeedback(),
            'partyId' => $this->getPartyId(),
            'confirmedUserId' => $this->getUserId(),
            'reportedUserId' => $this->getUserReportedId(),
            'reportStatus' => $this->getReportStatus(),
            'reportStatusName' => $this->getReportStatusName(),
            'reportDiv' => $this->getReportDiv(),
            'reportDivName' => $this->getReportDivName(),
            'content' => $this->getContent(),
            'timePartyStart' => $this->getTimePartyStart(),
            'partyTime' => $this->getPartyTime(),
            'timeReport' => TimeReportDiv::getDescription($this->getTimeReportDiv()),
            'reportAt' => $this->getReportDateFormat(),
            'feedbackAt' => $this->getFeedbackAtFormat(),
            'memberIsMale' => $this->getMemberIsMale(),
            'memberIsFemale' => $this->getMemberIsFemale(),
            'genderReportedId' => $this->getGenderReportedId(),
            'nicknameReportedUser' => $this->getNicknameReportedUser(),
            'nicknameConfirmedUser' => $this->getNicknameConfirmedUser(),
            'reportedMemberId' => $this->getReportedMemberId(),
            'confirmedMemberId' => $this->getMemberId(),
            'genderId' => $this->getGender(),
            'createdAt' => $this->getCreatedAtFormat()
        ];
    }
}
