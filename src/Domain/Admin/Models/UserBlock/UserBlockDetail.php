<?php


namespace Src\Domain\Admin\Models\UserBlock;

use App\Eloquent\User;
use Carbon\Carbon;
use Src\Domain\Model as BaseModel;
use Src\Enum\DateTimeFormat;
use Src\Enum\UserStatus;

/**
 * Class UserBlockDetail
 *
 * @package Src\Domain\Admin\Models\UserBlock
 */
class UserBlockDetail extends BaseModel
{
    /** @var User */
    private $user;

    /**
     * constructor
     *
     * UserBlockDetail constructor.
     * @param User $user_block
     */
    public function __construct(User $user_block)
    {
        $this->user = $user_block;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->user->id;
    }

    /**
     * @return int
     */
    public function getBlockUserStatus(): int
    {
        return $this->user->user_status;
    }

    /**
     * @return string
     */
    public function getBlockUserName(): string
    {
        if ($this->user->userProfile->nickname) {
            return $this->user->userProfile->nickname;
        }
        return '';
    }

    /**
     * @return Carbon|null
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->user->pivot->created_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'userId' => $this->getUserId(),
            'createdAt' => $this->getCreatedAtFormat(),
            'blockUserStatus' => UserStatus::getDescription($this->getBlockUserStatus()),
            'blockUserName' => $this->getBlockUserName(),
        ];
    }
}
