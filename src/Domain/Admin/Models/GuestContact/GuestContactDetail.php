<?php

namespace Src\Domain\Admin\Models\GuestContact;

use Src\Domain\Model;
use App\Eloquent\GuestContact as EloquentGuestContact;
use Src\Enum\ContactDiv;
use Src\Enum\DateTimeFormat;

class GuestContactDetail extends Model
{
    /**
     * @var EloquentGuestContact
     */
    private $guest_contact;

    /**
     * GuestContact constructor.
     *
     * @param EloquentGuestContact $guest_contact
     */
    public function __construct(EloquentGuestContact $guest_contact)
    {
        $this->guest_contact = $guest_contact;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->guest_contact->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->guest_contact->name;
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->guest_contact->email;
    }

    /**
     * @return int
     */
    public function getContactDiv(): int
    {
        return $this->guest_contact->contact_div;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->guest_contact->body;
    }

    public function getContactStatus(): int
    {
        return $this->guest_contact->contact_status;
    }

    /**
     * @return \Carbon\Carbon|null
     */
    public function getCreatedAt(): ?\Carbon\Carbon
    {
        return $this->guest_contact->created_at;
    }

    /**
     * @return \Carbon\Carbon|null
     */
    public function getUpdatedAt(): ?\Carbon\Carbon
    {
        return $this->guest_contact->updated_at;
    }

    /**
     * @return string
     */
    public function getCreatedAtFormat(): string
    {
        return format_date_time($this->getCreatedAt(), DateTimeFormat::DATETIME());
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'email' => $this->getEmail(),
            'contactDiv' => $this->getContactDiv(),
            'contactDivName' => ContactDiv::getDescription($this->getContactDiv()),
            'body' => $this->getBody(),
            'contactStatus' => $this->getContactStatus(),
            'createdAt' => $this->getCreatedAtFormat()
        ];
    }
}
