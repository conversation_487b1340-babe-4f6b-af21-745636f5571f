<?php


namespace Src\Domain\Admin\Models\CSVDownload;

use App\Eloquent\PointHistory;
use App\Eloquent\TicketHistory;
use App\Eloquent\User;
use App\Eloquent\UserBlock;
use App\Eloquent\UserContact;
use App\Eloquent\UserReport;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\Model as BaseModel;
use Src\Enum\ContactDiv;
use Src\Enum\DeviceRegister;
use Src\Enum\Gender;
use Src\Enum\MethodRegister;
use Src\Enum\MobilePlatform;
use Src\Enum\PointUpdateDiv;
use Src\Enum\TicketUpdateDiv;
use Src\Enum\UserStatus;

class UserContactCSVDetail extends BaseModel
{
    /** @var UserContact */
    private $user_contact;
    /** @var User */
    private $user;

    /**
     * construct
     *
     * UserContactCSVDetail constructor.
     * @param UserContact $user_contact
     */
    public function __construct(UserContact $user_contact)
    {
        $this->user_contact = $user_contact;
        isset($user_contact->user) ? $this->user = $user_contact->user : $this->user = new User();
    }

    /**
     * @return int
     */
    public function getContactId(): int
    {
        return $this->user_contact->id;
    }

    /**
     * @return string
     */
    public function getContactMemberId()
    {
        return isset($this->user->member_id) ? $this->user->member_id : '';
    }

    /**
     * @return int|null
     */
    public function getUserStatus(): ?int
    {
        return  $this->user->user_status;
    }

    /**
     * @return string
     */
    public function getContactAt()
    {
        return $this->user_contact->created_at->format('Y/m/d H:i');
    }

    /**
     * @return int
     */
    public function getContactDiv()
    {
        return $this->user_contact->contact_div;
    }

    /**
     * @return string
     */
    public function getContactContent()
    {
        return $this->user_contact->body;
    }

    /**
     * @return int|null
     */
    public function getContactUserGender()
    {
        return isset($this->user->userProfile) ? $this->user->userProfile->gender_party : null;
    }

    /**
     * @return int|null
     */
    public function getContactPartnerGender()
    {
        return isset($this->user->userProfile) ? $this->user->userProfile->gender_partner : null;
    }

    /**
     * @return string
     */
    public function getNickname()
    {
        return isset($this->user->userProfile) ? $this->user->userProfile->nickname : '';
    }

    /**
     * @return string
     */
    public function getBirthDay()
    {
        return isset($this->user->userProfile) ? $this->user->userProfile->birthday : '';
    }

    /**
     * @return string
     */
    public function getOccupation()
    {
        return isset($this->user->userProfile) ? $this->user->userProfile->occupation : '';
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        if ($this->user->method_register === MethodRegister::EMAIL &&
            $this->user->userProfile->gender_party === Gender::FEMALE) {
            return $this->user->login_id;
        }
        return '';
    }

    /**
     * @return int
     */
    public function getDeviceRegister()
    {
        return $this->user->device_register;
    }

    /**
     * @return int
     */
    public function getMobilePlatform()
    {
        return $this->user->mobile_platform ;

    }

    /**
     * @return int|null
     */
    public function getMethodRegister()
    {
        return $this->user->method_register;
    }

    /**
     * @return string
     */
    public function getMethodRegisterValue()
    {
        return isset($this->user->login_id) ? $this->user->login_id : '';
    }

    /**
     * @return int
     */
    public function getTotalBuyPoint(): int
    {
        $total = 0;
        $buy_points = $this->user->pointHistories()->where('point_update_div', PointUpdateDiv::BUY)->get();
        if (isset($buy_points)) {
            /** @var PointHistory $buy_point */
            foreach ($buy_points as $buy_point) {
                $total = $total + $buy_point->point;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalPresentPoint(): int
    {
        $total = 0;
        $present_points = $this->user->pointHistories()->where('point_update_div', PointUpdateDiv::PRESENT)->get();
        if (isset($present_points)) {
            /** @var PointHistory $present_point */
            foreach ($present_points as $present_point) {
                $total = $total + $present_point->point;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalReceivePoint(): int
    {
        $total = 0;
        $receive_points = $this->user->pointHistories()->where('point_update_div', PointUpdateDiv::RECEIVE)->get();
        if (isset($receive_points)) {
            /** @var PointHistory $receive_point */
            foreach ($receive_points as $receive_point) {
                $total = $total + $receive_point->point;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalBuyTicket(): int
    {
        $total = 0;
        $buy_tickets = $this->user->ticketHistories()->where('ticket_update_div', TicketUpdateDiv::BUY)->get();
        if (isset($buy_tickets)) {
            /** @var TicketHistory $buy_ticket */
            foreach ($buy_tickets as $buy_ticket) {
                $total = $total + $buy_ticket->ticket;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalBuyTicketMoney(): int
    {
        $total = 0;
        $buy_tickets = $this->user->ticketHistories()->where('ticket_update_div', TicketUpdateDiv::BUY)->get();
        if (isset($buy_tickets)) {
            /** @var TicketHistory $buy_ticket */
            foreach ($buy_tickets as $buy_ticket) {
                $total = $total + $buy_ticket->money;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalTicketUseExtend(): int
    {
        $total = 0;
        $use_tickets = $this->user->ticketHistories()->where('ticket_update_div', TicketUpdateDiv::USE)->get();
        if (isset($use_tickets)) {
            /** @var TicketHistory $use_ticket */
            foreach ($use_tickets as $use_ticket) {
                $total = $total + $use_ticket->ticket;
            }
        }
        return $total;
    }

    /**
     * @return int
     */
    public function getTotalBlockApply(): int
    {
        $user_blocks = $this->user->userBlocks;
        return isset($user_blocks) ? count($user_blocks) : 0;
    }

    /**
     * @return int
     */
    public function getTotalBlockReceive(): int
    {
        $block_users = UserBlock::query()->where('block_user_id', $this->user->id)->get();
        return isset($block_users) ? count($block_users) : 0;
    }

    /**
     * @return int
     */
    public function getTotalMaleFriend(): int
    {
        $male_friends = $this->user->userFriends()->whereHas('userProfile', function (Builder $query) {
            $query->where('gender_party', Gender::MALE);
        })->get();
        return isset($male_friends) ? count($male_friends) : 0;
    }

    /**
     * @return int
     */
    public function getTotalFemaleFriend(): int
    {
        $female_friends = $this->user->userFriends()->whereHas('userProfile', function (Builder $query) {
            $query->where('gender_party', Gender::FEMALE);
        })->get();
        return isset($female_friends) ? count($female_friends) : 0;
    }

    /**
     * @return int
     */
    public function getTotalReportApply(): int
    {
        $user_reports = $this->user->userReports;
        return isset($user_reports) ? count($user_reports) : 0;
    }

    /**
     * @return int
     */
    public function getTotalReportReceive(): int
    {
        $report_users = UserReport::query()->where('reported_user_id', $this->user->id)->get();
        return isset($report_users) ? count($report_users) : 0;
    }

    /**
     * @return string
     */
    public function getWarningStatus(): string
    {
        return ($this->user->is_warning) ? '警告' : '';
    }

    /**
     * @return string
     */
    public function getRegisterAt(): string
    {
        return isset($this->user->registered_at) ? $this->user->registered_at->format('Y/m/d H:i') : '';
    }

    /**
     * @return string
     */
    public function getOfficialMemberAt(): string
    {
        return isset($this->user->official_member_at) ? $this->user->official_member_at->format('Y/m/d H:i') : '';
    }

    /**
     * @return string
     */
    public function getWithDrawAt(): string
    {
        return isset($this->user->userWithdrawal) ? $this->user->userWithdrawal->withdrawal_at->format('Y/m/d H:i') : '';
    }

    /**
     * toComponentValue
     *
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * toComponentFormValue
     *
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'contactId' => $this->getContactId(),
            'contactMemberId' => $this->getContactMemberId(),
            'userStatusName' => UserStatus::getDescription($this->getUserStatus()),
            'contactAt' => $this->getContactAt(),
            'contactDiv' => ContactDiv::getDescription($this->getContactDiv()),
            'contactContent' => $this->getContactContent(),
            'contactUserGender' => Gender::getDescription($this->getContactUserGender()),
            'contactPartnerGender' => Gender::getDescription($this->getContactPartnerGender()),
            'nickname' => $this->getNickname(),
            'birthday' => $this->getBirthDay(),
            'occupation' => $this->getOccupation(),
            'email' => $this->getEmail(),
            'deviceRegister' => DeviceRegister::getDescription($this->getDeviceRegister()),
            'mobilePlatform' => MobilePlatform::getDescription($this->getMobilePlatform()),
            'methodRegister' => MethodRegister::getDescription($this->getMethodRegister()),
            'methodRegisterValue' => $this->getMethodRegisterValue(),
            'totalBuyPoint' => $this->getTotalBuyPoint(),
            'totalPresentPoint' => $this->getTotalPresentPoint(),
            'totalReceivePoint' => $this->getTotalReceivePoint(),
            'totalBuyTicket' => $this->getTotalBuyTicket(),
            'totalBuyTicketMoney' => $this->getTotalBuyTicketMoney(),
            'totalTicketUseExtend' => $this->getTotalTicketUseExtend(),
            'totalBlockApply' => $this->getTotalBlockApply(),
            'totalBlockReceive' => $this->getTotalBlockReceive(),
            'totalMaleFriend' => $this->getTotalMaleFriend(),
            'totalFemaleFriend' => $this->getTotalFemaleFriend(),
            'totalReportApply' => $this->getTotalReportApply(),
            'totalReportReceive' => $this->getTotalReportReceive(),
            'warningStatus' => $this->getWarningStatus(),
            'registerAt' => $this->getRegisterAt(),
            'officialMemberAt' => $this->getOfficialMemberAt(),
            'withDrawAt' => $this->getWithDrawAt(),
        ];
    }
}
