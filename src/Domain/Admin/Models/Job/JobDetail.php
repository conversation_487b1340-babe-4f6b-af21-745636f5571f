<?php

namespace Src\Domain\Admin\Models\Job;

use App\Eloquent\Job;
use Carbon\Carbon;
use Src\Domain\FormModel;
use Src\Enums\ApprovalStatus;
use Src\Enums\JobType;

/**
 * Class JobDetail
 * @package Src\Domain\Admin\Models\Job
 */
class JobDetail extends FormModel
{
    /**
     * @var Job
     */
    protected Job $job;

    /**
     * JobDetail constructor.
     * @param Job $job
     */
    public function __construct(Job $job)
    {
        $this->job = $job;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->job->id;
    }

    /**
     * @return string
     */
    public function getRecruitmentType(): string
    {
        return $this->job->recruitment_type;
    }

    /**
     * @return string
     */
    public function getEmployerName(): string
    {
        return $this->job->employer_name;
    }

    /**
     * @return string
     */
    public function getEmployerEmail(): string
    {
        return $this->job->employer_email;
    }

    /**
     * @return string
     */
    public function getEmployerPhoneNumber(): string
    {
        return $this->job->employer_phone_number;
    }

    /**
     * @return int
     */
    public function getCategoryId(): int
    {
        return $this->job->category_id;
    }

    /**
     * @return string
     */
    public function getCategoryName(): string
    {
        return $this->job->category->name ?? '';
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->job->type;
    }

    /**
     * @return string|null
     */
    public function getTypeName(): ?string
    {
        return JobType::getDescription($this->getType());
    }

    /**
     * @return bool
     */
    public function getIsPublic(): bool
    {
        return $this->job->is_public;
    }

    /**
     * @return bool
     */
    public function getIsInstant(): bool
    {
        return $this->job->is_instant;
    }

    /**
     * @return int
     */
    public function getThumbnailId(): int
    {
        return $this->job->thumbnail_id;
    }

    /**
     * @return string|null
     */
    public function getThumbnailUrl(): ?string
    {
        $path = optional($this->job->thumbnail)->file_path;

        return $path ? presigned_url($path) : null;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->job->title;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->job->description;
    }

    /**
     * @return string|null
     */
    public function getBenefits(): ?string
    {
        return $this->job->benefits;
    }


    /**
     * @return string
     */
    public function getTimeStart(): string
    {
        return date('H:i', strtotime($this->job->time_start));
    }

    /**
     * @return string|null
     */
    public function getTimeEnd(): ?string
    {
        return date('H:i', strtotime($this->job->time_end));
    }

    /**
     * @return int|null
     */
    public function getAge(): ?int
    {
        return $this->job->age;
    }

    /**
     * @return string|null
     */
    public function getGender(): ?string
    {
        return $this->job->gender;
    }

    /**
     * @return int|null
     */
    public function getQuantity(): ?int
    {
        return $this->job->quantity ?? 0;
    }

    /**
     * @return string
     */
    public function getCertificateLevel(): string
    {
        return $this->job->certificate_level;
    }

    /**
     * @return string
     */
    public function getPrefecture(): string
    {
        return $this->job->prefecture;
    }

    /**
     * @return string
     */
    public function getAddress(): string
    {
        return $this->job->address;
    }

    /**
     * @return string
     */
    public function getSalaryType(): string
    {
        return $this->job->salary_type;
    }

    /**
     * @return int|null
     */
    public function getSalary(): ?int
    {
        return $this->job->salary;
    }

    /**
     * @return string
     */
    public function getTravelFeeType(): string
    {
        return $this->job->travel_fee_type;
    }

    /**
     * @return int|null
     */
    public function getTravelFee(): ?int
    {
        return $this->job->travel_fee;
    }

    /**
     * @return string
     */
    public function getRecruitStartAt(): string
    {
        return $this->job->recruit_start_at;
    }

    /**
     * @return string
     */
    public function getRecruitExpiredAt(): string
    {
        return $this->job->recruit_expired_at;
    }

    /**
     * @return string
     */
    public function getJobStartAt(): string
    {
        return $this->job->job_start_at;
    }

    public function getIsFilled(): bool
    {
        return $this->job->is_filled;
    }

    /**
     * @return string
     */
    public function getCreatedAt(): string
    {
        return $this->job->created_at->format('Y-m-d H:i:s');
    }

    /**
     * @return string
     */
    public function getUpdatedAt(): string
    {
        return $this->job->updated_at->format('Y-m-d H:i:s');
    }

    public function getImages(): array
    {
        $images = $this->job->images;
        return $images->map(function ($image) {
            return [
                'id' => $image->id,
                'imageUrl' => presigned_url($image->image->file_path),
            ];
        })->toArray();
    }

    /**
     * @return int
     */
    public function getCountApplied(): int
    {
        return $this->job->jobApplications()->where('approval_status', '===', ApprovalStatus::APPROVED)->count();
    }

    /**
     * @return array
     */
    public function toComponent(): array
    {
        return [
            'id' => $this->getId(),
            'recruitmentType' => $this->getRecruitmentType(),
            'employerEmail' => $this->getEmployerEmail(),
            'employerName' => $this->getEmployerName(),
            'employerPhoneNumber' => $this->getEmployerPhoneNumber(),
            'categoryId' => $this->getCategoryId(),
            'categoryName' => $this->getCategoryName(),
            'type' => $this->getType(),
            'typeName' => $this->getTypeName(),
            'isPublic' => $this->getIsPublic(),
            'isInstant' => $this->getIsInstant(),
            'thumbnailId' => $this->getThumbnailId(),
            'thumbnailUrl' => $this->getThumbnailUrl(),
            'title' => $this->getTitle(),
            'description' => $this->getDescription(),
            'benefits' => $this->getBenefits(),
            'timeStart' => $this->getTimeStart(),
            'timeEnd' => $this->getTimeEnd(),
            'age' => $this->getAge(),
            'gender' => $this->getGender(),
            'quantity' => $this->getQuantity(),
            'certificateLevel' => $this->getCertificateLevel(),
            'prefecture' => $this->getPrefecture(),
            'address' => $this->getAddress(),
            'salaryType' => $this->getSalaryType(),
            'salary' => $this->getSalary(),
            'travelFeeType' => $this->getTravelFeeType(),
            'travelFee' => $this->getTravelFee(),
            'recruitStartAt' => $this->getRecruitStartAt(),
            'recruitExpiredAt' => $this->getRecruitExpiredAt(),
            'jobStartAt' => $this->getJobStartAt(),
            'isFilled' => $this->getIsFilled(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt(),
            'images' => $this->getImages(),
            'countApplied' => $this->getCountApplied()
        ];
    }
}
