<?php

namespace Src\Domain\Admin\Models\Account;

use Src\Domain\FormModel;

/**
 * Class AccountForm
 * @package Src\Domain\Admin\Models\Account
 */
class AccountForm extends FormModel
{
    /** @var string|null */
    protected $login_id;
    /** @var string|null */
    protected $password;
    /** @var string|null */
    protected $email;
    /** @var string|null */
    protected $account_name;

    protected $fields = [
        'account_name' => 'string',
        'login_id' => 'string',
        'password' => 'string',
        'email' => 'string',
    ];

    /**
     * InputForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->account_name = $input['account_name'];
        $this->login_id = $input['login_id'];
        $this->password = $input['password'];
        $this->email = $input['email'];
    }

    /**
     * @return string|null
     */
    public function getLoginId(): ?string
    {
        return $this->login_id;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getAccountName(): ?string
    {
        return $this->account_name;
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @return array
     */
    public function updateAttributes(): array
    {
        return [
            'login_id' => $this->getLoginId(),
            'email' => $this->getEmail(),
            'account_name' => $this->getAccountName()
        ];
    }

    /**
     * create Attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return [
            'password' => bcrypt($this->getPassword()),
            'email' => $this->getEmail(),
            'login_id' => $this->getLoginId(),
            'account_name' => $this->getAccountName(),
            'registered_at' => now()
        ];
    }
}
