<?php

namespace Src\Domain\Admin\Models\UserNotification;

use App\Eloquent\UserNotification as EloquentUserNotification;

/**
 * Class UserNotification
 *
 * @package Src\Domain\Admin\Models\UserNotification
 */
class UserNotification
{
    /**
     * @var EloquentUserNotification
     */
    private $user_notification;

    /**
     * UserNotification constructor.
     *
     * @param EloquentUserNotification $user_notification
     */
    public function __construct(EloquentUserNotification $user_notification)
    {
        $this->user_notification = $user_notification;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->user_notification->id;
    }

    /**
     * @return int
     */
    public function getNotificationDiv(): int
    {
        return $this->user_notification->notification_div;
    }

    /**
     * @return string
     */
    public function getForeignTable(): ?string
    {
        return $this->user_notification->foreign_table;
    }

    /**
     * @return int
     */
    public function getForeignId(): ?int
    {
        return $this->user_notification->foreign_id;
    }

    /**
     * @return int
     */
    public function getPartyId(): ?int
    {
        return $this->user_notification->party_id;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->user_notification->title;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->user_notification->body;
    }

    /**
     * @return string|null
     */
    public function getData(): ?string
    {
        return $this->user_notification->data;
    }

    /**
     * @return string
     */
    public function getNotificationAt(): string
    {
        return $this->user_notification->notification_at;
    }

    /**
     * @return int
     */
    public function getIsReadDetail(): int
    {
        return $this->user_notification->pivot->is_read_detail;
    }

    /**
     * @return bool
     */
    public function getHasAction(): bool
    {
        return $this->user_notification->has_action;
    }

    /**
     * @return string
     */
    public function toComponentValue(): string
    {
        return json_encode($this->toComponentFormValue());
    }

    /**
     * Return data for list
     *
     * @return array
     */
    public function toComponentFormValue(): array
    {
        return [
            'id' => $this->getId(),
            'notification_div' => $this->getNotificationDiv(),
            'title' => $this->getTitle(),
            'body' => $this->getBody(),
            'notification_at' => $this->getNotificationAt(),
            'is_read_detail' => $this->getIsReadDetail(),
        ];
    }

    /**
     * Return data for detail
     *
     * @return array
     */
    public function toDetailFormValue(array $attach_data): array
    {
        return array_merge($this->toComponentFormValue(), [
            'foreign_table' => $this->getForeignTable(),
            'foreign_id' => $this->getForeignId(),
            'party_id' => $this->getPartyId(),
            'has_action' => $this->getHasAction(),
            'data' => $this->getData(),
        ], $attach_data);
    }
}
