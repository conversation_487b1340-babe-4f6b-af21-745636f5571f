<?php

namespace Src\Domain\Admin\Requests\FeedbackReport;

use Src\Domain\Admin\Models\FeedbackReport\FeedbackReportForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class CreateRequest
 *
 * @package Src\Domain\Admin\Requests\FeedbackReport
 */
class CreateRequest extends FormRequest
{
    /**
     * rules
     *
     * @return array
     */
    public function rules(){
        return [
            'enable_warning' => [
                'required'
            ],
            'sent_at' => [
                'required'
            ],
            'content' => [
                'required'
            ],
            'target_user_id' => [
                'required'
            ],
            'foreign_id' => [
                'required'
            ]
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'enable_warning' => __('models/user_feedback.field.enable_warning'),
            'sent_at' => __('models/user_feedback.field.sent_at'),
            'content' => __('models/user_feedback.field.content'),
            'target_user_id' => __('models/user_feedback.field.target_user_id'),
            'foreign_id' => __('models/user_feedback.field.foreign_id'),
        ];
    }

    /**
     * @return mixed
     */
    public function validatedForm()
    {
        return new FeedbackReportForm($this->validated());
    }

}
