<?php

namespace Src\Domain\Admin\Requests\FeedbackGuestContact;

use Src\Domain\Admin\Models\FeedbackGuestContact\FeedbackGuestContactForm;
use Src\Domain\Admin\Requests\FormRequest;

class CreateRequest extends FormRequest
{
    /**
     * rules
     *
     * @return array
     */
    public function rules() : array
    {
        return [
            'content' => [
                'required',
                'string'
            ],
            'guest_user_id' => [],
            'foreign_id' => []
        ];
    }

    /**
     * attributes
     *
     * @return array
     */
    public function attributes()
    {
        return[
            'content' => __('models/notification.field.content'),
        ];
    }

    /**
     * @return mixed|FeedbackGuestContactForm
     */
    public function validatedForm()
    {
        return new FeedbackGuestContactForm($this->validated());
    }
}
