<?php

namespace Src\Domain\Admin\Requests\User;

use Illuminate\Validation\Rule;

/**
 * Class CreateRequest
 * @package Src\Domain\Admin\Requests\User
 */
class CreateRequest extends BaseRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'password' => [
                'required',
                'string',
                'min:8',
                'max:45'
            ],
            'login_id' => [
                Rule::unique('users', 'login_id'),
                'required',
                'string',
                'max:100'
            ],
        ]);
    }
}
