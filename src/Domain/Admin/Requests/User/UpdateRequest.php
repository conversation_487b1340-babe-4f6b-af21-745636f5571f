<?php

namespace Src\Domain\Admin\Requests\User;

use Illuminate\Validation\Rule;

/**
 * Class UpdateRequest
 * @package Src\Domain\Admin\Requests\User
 */
class UpdateRequest extends BaseRequest
{
    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        $user_id = $this->route()->parameter('user_id');
        return array_merge(parent::rules(), [
            'password' => [
                'nullable',
                'string',
                'min:8',
                'max:45'
            ],
            'login_id' => [
                Rule::unique('users', 'login_id')->ignore($user_id),
                'required',
                'string',
                'max:100'
            ],
        ]);
    }
}
