<?php

namespace Src\Domain\Admin\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Src\Domain\Admin\Models\Auth\LoginForm;

/**
 * Class LoginRequest
 * @package Src\Domain\Admin\Requests\Auth
 */
class LoginRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'login_id' => [
                'required',
                'max:20'
            ],
            'password' => [
                'required',
                'string',
                'max:255'
            ],
        ];
    }

    /**
     * Attribute
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'login_id' => __('models/auth.login.login_id'),
            'password' => __('models/auth.login.password'),
        ];
    }

    /**
     * @return LoginForm
     */
    public function validatedForm(): LoginForm
    {
        return new LoginForm($this->validated());
    }
}
