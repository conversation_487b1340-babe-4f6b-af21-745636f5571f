<?php

namespace Src\Domain\Admin\Requests\Job;

use Src\Domain\Admin\Models\Job\JobForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class UpdateRequest
 * @package Src\Domain\Admin\Requests\Job
 */
class UpdateRequest extends BaseRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return array_merge(parrent::rules(), [
            'thumbnail' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
            ],
            'images' => [
                'nullable',
                'array'
            ],
            'images.*' => [
                'image',
                'mimes:jpeg,png,jpg',
            ],
            'deleted_image_ids' => [
                'nullable',
                'array'
            ],
            'deleted_image_ids.*' => [
                'integer',
                'exists:t_job_images,image_id'
            ],
        ]);
    }

    /**
     * Attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return parent::attributes();
    }
}
