<?php

namespace Src\Domain\Admin\Requests\UserReport;

use Src\Domain\Admin\Requests\FormRequest;
use Src\Domain\Admin\Models\UserReport\UserReportForm;

/**
 * Base BaseRequest
 *
 * @package Src\Domain\Admin\Requests\UserReport
 */
class BaseRequest extends FormRequest
{

    /**
     * Base rules
     *
     * @return array
     */
    protected function rules(): array
    {
        return [
            'report_status' => [
                'required'
            ]
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'report_status' => __('models/user_report.field.report_status')
        ];
    }

     /**
      * @return UserReportForm
      */
    public function validatedForm(): UserReportForm
    {
        return new UserReportForm($this->validated());
    }

}
