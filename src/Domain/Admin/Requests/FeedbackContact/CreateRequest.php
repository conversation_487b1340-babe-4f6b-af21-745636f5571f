<?php

namespace Src\Domain\Admin\Requests\FeedbackContact;

use Src\Domain\Admin\Models\FeedbackContact\FeedbackContactForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class CreateRequest
 *
 * @package Src\Domain\Admin\Requests\FeedbackContact
 */
class CreateRequest extends FormRequest
{
    /**
     * rules
     *
     * @return array
     */
    public function rules(){
        return [
            'enable_warning' => [
                'required'
            ],
            'sent_at' => [
                'required'
            ],
            'content' => [
                'required'
            ],
            'foreign_id' => [
                'required'
            ]
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'enable_warning' => __('models/user_feedback.field.enable_warning'),
            'sent_at' => __('models/user_feedback.field.sent_at'),
            'content' => __('models/user_feedback.field.content'),
            'foreign_id' => __('models/user_feedback.field.foreign_id'),
        ];
    }

    /**
     * @return mixed
     */
    public function validatedForm()
    {
        return new FeedbackContactForm($this->validated());
    }
}
