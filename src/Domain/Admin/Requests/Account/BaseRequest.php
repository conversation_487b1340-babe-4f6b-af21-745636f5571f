<?php

namespace Src\Domain\Admin\Requests\Account;

use Src\Domain\Admin\Models\Account\AccountForm;
use Src\Domain\Admin\Requests\FormRequest;

/**
 * Class BaseRequest
 *
 * @package Src\Domain\Admin\Requests\Account
 */
class BaseRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize() : bool
    {
        return true;
    }

    /**
     * Rules
     *
     * @return array
     */
    public function rules() : array
    {
        return [
            'login_id' => [
                'required',
                'string',
                'max:100'
            ],
            'email' => [
                'required',
                'email'
            ],
            'password' => [
                'required',
                'password',
                'min:8'
            ],
            'account_name' => [
                'required',
                'string',
                'max:100'
            ]
        ];
    }


    /**
     * base attributes
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'login_id' => __('models/account.field.login_id'),
            'password' => __('models/account.field.password'),
            'email' => __('models/account.field.email'),
            'account_name' => __('models/account.field.account_name'),
        ];
    }

    /**
     * @inheritdoc
     */
    public function validatedForm()
    {
        return new AccountForm($this->validated());
    }

}
