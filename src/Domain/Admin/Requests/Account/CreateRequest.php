<?php

namespace Src\Domain\Admin\Requests\Account;

use Illuminate\Validation\Rule;

/**
 * Class CreateRequest
 *
 * @package Src\Domain\Admin\Requests\Account
 */
class CreateRequest extends BaseRequest
{
    /**
     * rules
     *
     * @return array
     */
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'password' => [
                'required',
                'string',
                'min:8',
                'max:45'
            ],
            'email' => [
                Rule::unique('accounts', 'email'),
                'email',
                'required',
                'string',
                'max:100'
            ],
        ]);
    }

}
