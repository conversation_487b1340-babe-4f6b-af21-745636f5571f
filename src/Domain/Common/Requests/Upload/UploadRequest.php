<?php

namespace Src\Domain\Common\Requests\Upload;

use Illuminate\Http\UploadedFile;
use Src\Domain\Admin\Requests\FormRequest;
use Src\Domain\Common\Forms\Upload\UploadForm;

/**
 * Class UploadRequest
 * @package Src\Domain\Common\Requests\Upload
 */
class UploadRequest extends FormRequest
{
    /**
     * Authorization
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Rule create Request
     *
     * @return array
     */
    public function validationData(): array
    {
        $data = $this->all();
        if (array_key_exists('files', $data)) {
            $file_size = 0;
            /** @var UploadedFile $file */
            foreach ($data['files'] as $file) {
                if ($file instanceof UploadedFile) {
                    $file_size += $file->getSize();
                }
            }
            $data['total_file_size'] = ceil($file_size / 1024);
        }

        return $data;
    }

    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'old_tmp_path' => [
                'nullable',
                'string'
            ],
            'file' => [
                'file',
                'max:' . 10 * 1024
            ],
            'files' => [
                'array'
            ],
            'files.*' => [
                'file',
                'max:' . 10 * 1024
            ],
            'total_file_size' => [
                'nullable',
                'integer',
            ],
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'files' => __('common.file'),
            'file' => __('common.file'),
            'file.*' => __('common.file'),
        ];
    }

    /**
     * Return validated form
     *
     * @return UploadForm
     */
    public function validatedForm(): UploadForm
    {
        return new UploadForm($this->validated());
    }
}
