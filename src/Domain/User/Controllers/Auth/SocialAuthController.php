<?php

namespace Src\Domain\User\Controllers\Auth;

use App\Http\Controllers\Controller;
use Socialite;
use Src\Domain\User\Services\SocialUserService;

class SocialAuthController extends Controller
{
    public function redirect($provider)
    {
//        dd($provider);
        return Socialite::driver($provider)->redirect();
    }

    public function callback($provider)
    {
        [$user, $token] = SocialUserService::createOrGetUser($provider);

        if( !$user){
            return 'Cant login because user null';
        }

//        auth()->login($user);
        return array_merge($user->toArray(), ['token' => $token]);
//        return redirect()->to('/home');
    }
}
