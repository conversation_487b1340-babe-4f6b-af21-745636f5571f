<?php


namespace Src\Domain\User\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Src\Domain\User\Requests\User\PasswordResetRequest;
use Src\Domain\User\Services\PasswordService;
use Src\Enum\ResultCode;

/**
 * Class PasswordController
 * @package Src\Domain\User\Controllers\User
 */
class PasswordController extends Controller
{
    /**
     * @return Factory|View
     */
    public function index()
    {
        return view('user.password_reset.index');
    }

    /**
     * @return Factory|View
     */
    public function success()
    {
        return view('user.password_reset.success');
    }

    /**
     * @param string $token
     * @param PasswordResetRequest $request
     * @param PasswordService $service
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store( string $token,PasswordResetRequest $request, PasswordService $service)
    {
        $result = $service->reset($request->validatedForm(), $token);
        if ($result === ResultCode::ERROR) {
            return redirect()->back()->with('token', __('flash.reset_password.token_invalid'));
        }
        if (!$result) {
            flash()->error(__('flash.store.failed'));
            return redirect()->back();
        }
        return redirect()->route('user.reset_password.success');
    }
}
