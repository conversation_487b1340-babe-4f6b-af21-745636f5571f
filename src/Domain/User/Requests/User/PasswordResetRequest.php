<?php

namespace Src\Domain\User\Requests\User;

use Src\Domain\User\Models\User\PasswordResetForm;
use Src\Domain\User\Requests\FormRequest;

class PasswordResetRequest extends FormRequest
{
    /**
     * Base rules
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'password' => [
                'required',
                'min:8',
                'max:45',
                'regex:/(?=.*[a-zA-z])(?=.*\d)[A-Za-z\d]{8,}$/',
                'confirmed'
            ]
        ];
    }

    /**
     * Base attributes
     *
     * @return array
     */
    public function attributes(): array
    {
        return [
            'password' => __('models/user.field.password'),
        ];
    }

    /**
     * @return PasswordResetForm
     */
    public function validatedForm(): PasswordResetForm
    {
        return new PasswordResetForm($this->validated());
    }
}
