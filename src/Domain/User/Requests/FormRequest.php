<?php

namespace Src\Domain\User\Requests;

use App\Rules\PhoneNumber;
use Illuminate\Foundation\Http\FormRequest as Base;
use Illuminate\Validation\Rule;
use Src\Enum\LoginIdType;

/**
 * Class FormRequest
 * @package Src\Domain\User\Requests
 */
abstract class FormRequest extends Base
{
    /**
     * @return mixed
     */
    abstract public function validatedForm();

    /**
     * Get rules for login_id field
     *
     * @return array
     */
    protected function getRuleForLoginId()
    {
        $rules = [
            Rule::unique('users', 'login_id'),
            'required',
            'max:100'
        ];

        if ($this->input('login_id_type') == LoginIdType::EMAIL) {
            $rules = array_merge($rules, [
                'string',
                'email'
            ]);
        } elseif ($this->input('login_id_type') == LoginIdType::PHONE_NUMBER) {
            $rules = array_merge($rules, [
                new PhoneNumber()
            ]);
        }

        return $rules;
    }
}
