<?php

namespace Src\Domain\User\Services;

use App\Eloquent\SocialUser;
use App\Eloquent\User;
use DB;
use <PERSON><PERSON>\Socialite\Contracts\User as ProviderUser;
use Laravel\Socialite\Facades\Socialite;
use phpDocumentor\Reflection\Types\This;
use Src\Domain\Api\Services\Traits\UserTrait;
use Src\Enum\LoginIdType;
use Src\Utils\GenId;

class SocialUserService
{
    use UserTrait;

    public static function createOrGetUser($provider)
    {
        $result = [null, null];
        try {
            $result = DB::transaction(static function () use ( $provider) {
                logger()->debug('CALL_TO', [$provider]);
                /** @var ProviderUser $provider_user */
                $provider_user = Socialite::driver($provider)->user();
//        $provider_user = Socialite::driver($provider)->stateless()->result();

                logger()->debug('TOKEN', [$provider_user->token]);
                dd($provider_user->token);
                $social_user = SocialUser::whereProvider($provider)
                    ->whereProviderUserId($provider_user->getId())
                    ->first();

                if ($social_user) {
                    logger()->debug('FOUND_SOCIAL', [$social_user]);
                    return [$social_user->user, $provider_user->token];
                } else {
                    $email = $provider_user->getEmail() ?? $provider_user->getNickname();
                    $social_user = new SocialUser([
                        'provider_user_id' => $provider_user->getId(),
                        'provider' => $provider
                    ]);
                    $user = User::whereEmail($email)->first();

                    if (!$user) {
                        $user = User::create([
                            'login_id' => $email,
                            'login_id_type' => LoginIdType::EMAIL,
                            'name' => $provider_user->getName(),
                            'password' => bcrypt(str_random(8)),
                            'member_id' => $this->genNewMemberId(),
                            'mobile_platform' => 1,
                            'registered_at' => now()
                        ]);
                    }

                    $social_user->user()->associate($user);
                    $social_user->save();

                    return [$user, $provider_user->token];
                }
            });
        } catch (\Throwable $e) {
            $result = [null, null];
            logger()->error($e);
            logger()->debug('failed login', [$provider]);
        }

        return $result;

    }
}
