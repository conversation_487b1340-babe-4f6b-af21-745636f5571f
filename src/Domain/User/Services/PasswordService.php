<?php

namespace Src\Domain\User\Services;

use App\Eloquent\PasswordReset as EloquentPasswordReset;
use App\Eloquent\User;
use Illuminate\Database\Eloquent\Builder;
use Src\Domain\User\Models\User\PasswordResetForm;
use DB;
use Carbon\Carbon;
use Src\Enum\ResultCode;

class PasswordService
{
    /**
     * Reset Password
     *
     * @param PasswordResetForm $form
     * @param $token
     * @return false|mixed
     */
    public function reset(PasswordResetForm $form, $token)
    {
        $result = false;
        try {
            $result = DB::transaction(function () use ($form, $token) {
                $password_reset = $this->passwordResetQuery()
                    ->where('token', $token)
                    ->where('created_at', '>=', now()->subMinutes(RESET_PASSWORD))
                    ->first();
                if (!$password_reset) {
                    return ResultCode::ERROR;
                }

                if (Carbon::parse($password_reset->created_at)->addMinutes(RESET_PASSWORD)->isPast()) {
                    $password_reset->delete();

                    return response()->json([
                        'message' => 'This password reset token is invalid.',
                    ], 422);
                }

                $user = User::query()->getModel()->where('login_id', $password_reset->login_id)->firstOrFail();
                $user->update($form->createAttributes());
                $password_reset->delete();
                return true;
            });
        } catch (\Throwable $e) {
            logger()->error($e);
            logger()->info('failed reset password.', $form->toArray());
        }
        return $result;
    }

    /**
     * @return Builder|EloquentPasswordReset
     */
    private function passwordResetQuery()
    {
        return EloquentPasswordReset::query()->getModel();
    }
}
