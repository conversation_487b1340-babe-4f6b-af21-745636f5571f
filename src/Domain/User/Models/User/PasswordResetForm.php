<?php

namespace Src\Domain\User\Models\User;

use Src\Domain\FormModel;

class PasswordResetForm extends FormModel
{
    protected $password;

    /**
     * PasswordResetForm constructor.
     * @param array $input
     */
    public function __construct(array $input)
    {
        $input = $this->castFields($input);
        $this->password = $input['password'];
    }

    /**
     * @return string|null
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * common attributes
     *
     * @return array
     */
    private function commonAttributes(): array
    {
        return [
            'password' => bcrypt($this->getPassword())
        ];
    }

    /**
     * create attributes
     *
     * @return array
     */
    public function createAttributes(): array
    {
        return $this->commonAttributes();
    }
}
