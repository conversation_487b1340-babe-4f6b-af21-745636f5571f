<?php

namespace Src\Traits;

use Carbon\Carbon;

/**
 * Trait FormatDate
 * @package Src\Traits
 */
trait FormatDate
{
    /**
     * formatDateTime
     *
     * @param Carbon|null $time
     * @return string
     */
    public function formatDateTime(Carbon $time = null): string
    {
        return null === $time ? __('admin.default-no-data') : $time->format(__('form.date-format.datetime'));
    }

    /**
     * concatNameAndDateTime
     * 
     * @param string $name
     * @param string $time
     * @return string
     */
    public function concatNameAndDateTime(string $name, string $time): string
    {
        return sprintf('%s (%s)', $name, $time);
    }
}
