<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class GroupType
 *
 * @package Src\Enum
 *
 * @method static static ONE_PARTNER()
 * @method static static TWO_PARTNER()
 * @method static static THREE_PARTNER()
 * @method static static FOUR_PARTNER()
 */
final class GroupType extends Enum implements LocalizedEnum
{
    
    /**
     * One Partner
     */
    const ONE_PARTNER = 1;

    /**
     * Two Partner
     */
    const TWO_PARTNER = 2;

    /**
     * Three Partner
     */
    const THREE_PARTNER = 3;

    /**
     * Four Partner
     */
    const FOUR_PARTNER = 4;

}