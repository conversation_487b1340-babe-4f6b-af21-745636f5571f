<?php


namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class ApprovalStatus
 * @method static static WAIT_APPROVAL()
 * @method static static DECLINED()
 * @method static static APPROVED()
 * @package Src\Enum
 */
class ApprovalStatus extends Enum implements LocalizedEnum
{
    /**
     * WAIT_APPROVAL
     */
    const WAIT_APPROVAL = 1;

    /**
     * DECLINED
     */
    const DECLINED = 2;

    /**
     * APPROVED
     */
    const APPROVED = 3;

}
