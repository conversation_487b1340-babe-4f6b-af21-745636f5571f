<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class PartyParticipantStatus
 *
 * @package Src\Enum
 *
 * @method static static MATCHED()
 * @method static static ACCEPTED_JOIN()
 * @method static static DECLINED_JOIN()
 * @method static static PARTYING()
 * @method static static LEAVED()
 * @method static static EXPIRED()
 */
final class PartyParticipantStatus extends Enum implements LocalizedEnum
{
    /**
     * MATCHED
     */
    const MATCHED = 1;

    /**
     * ACCEPTED_JOIN
     */
    const ACCEPTED_JOIN = 2;

    /**
     * ACCEPTED_JOIN
     */
    const DECLINED_JOIN = 3;

    /**
     * PARTYING
     */
    const PARTYING = 4;

    /**
     * Leaved
     */
    const LEAVED = 5;

    /**
     * Leaved
     */
    const EXPIRED = 6;

    /**
     * @return int[]
     */
    public static function getListNotInParty(){
        return [self::DECLINED_JOIN, self::LEAVED, self::EXPIRED];
    }

    /**
     * @return int[]
     */
    public static function getListInParty()
    {
        return [self::PARTYING, self::LEAVED, self::EXPIRED];
    }

}
