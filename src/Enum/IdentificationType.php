<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class IdentificationType
 *
 * @method static static DRIVER_LICENSE()
 * @method static static PASSPORT()
 * @method static static MY_NUMBER()
 * @method static static HEALTH_INSURANCE_CARD()
 * @package Src\Enum
 */
class IdentificationType extends Enum implements LocalizedEnum
{
    /**
     * DRIVER_LICENSE
     */
    const DRIVER_LICENSE = 1;

    /**
     * Passport
     */
    const PASSPORT = 2;

    /**
     * MY_NUMBER
     */
    const MY_NUMBER = 3;

    /**
     * HEALTH_INSURANCE_CARD
     */
    const HEALTH_INSURANCE_CARD = 4;

}
