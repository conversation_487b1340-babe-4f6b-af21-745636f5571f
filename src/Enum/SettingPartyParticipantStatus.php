<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class SettingPartyStatus
 *
 * @package Src\Enum
 *
 * @method static static INVITED()
 * @method static static JOINED()
 * @method static static LEAVED()
 */
final class SettingPartyParticipantStatus extends Enum implements LocalizedEnum
{

    /**
     * Invited
     */
    const INVITED = 1;

    /**
     * Joined
     */
    const JOINED = 2;

    /**
     * Leaved
     */
    const LEAVED = 3;

}
