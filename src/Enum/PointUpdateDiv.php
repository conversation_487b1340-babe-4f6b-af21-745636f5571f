<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class PointUpdateDiv
 * @method static static BUY()
 * @method static static PRESENT()
 * @method static static RECEIVE()
 * @package Src\Enum
 */
class PointUpdateDiv extends Enum implements LocalizedEnum
{
    /**
     * Buy point
     */
    const BUY = 1;

    /**
     * Present point
     */
    const PRESENT = 2;

    /**
     * receive point
     */
    const RECEIVE = 3;
}
