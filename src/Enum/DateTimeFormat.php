<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Date Time Format
 *
 * @method static DateTimeFormat FULL()
 * @method static DateTimeFormat DATETIME()
 * @method static DateTimeFormat DATE()
 * @method static DateTimeFormat TIME()
 * @method static DateTimeFormat DATETIME_RECURRING()
 * @method static DateTimeFormat DATETIME_FULL_JP()
 *
 * @package Src\Enum
 */
class DateTimeFormat extends Enum implements LocalizedEnum
{
    /**
     * full
     */
    public const FULL = 1;

    /**
     * datetime
     */
    public const DATETIME = 2;

    /**
     * date
     */
    public const DATE = 3;

    /**
     * time
     */
    public const TIME = 4;

    /**
     * date time recurring
     */
    public const DATETIME_RECURRING = 5;

    /**
     * date time with japanese character
     */
    public const DATETIME_FULL_JP = 6;

}
