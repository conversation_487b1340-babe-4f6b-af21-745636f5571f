<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class SendStatus
 *
 * @method static static WAIT_SEND()
 * @method static static SUCCESSFUL_SEND()
 * @method static static FAILED_SEND()
 * @package Src\Enum
 */
class SendStatus extends Enum implements LocalizedEnum
{
    /**
     * Wait send
     */
    public const WAIT_SEND = 1;

    /**
     * Successful send
     */
    public const SUCCESSFUL_SEND = 2;

    /**
     * Failed send
     */
    public const FAILED_SEND = 3;

}
