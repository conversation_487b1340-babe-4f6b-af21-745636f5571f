<?php


namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class MethodRegister
 * @method static static EMAIL()
 * @method static static PHONE()
 * @method static static FACEBOOK()
 * @method static static GOOGLE()
 * @method static static APPLE()
 * @package Src\Enum
 */
class MethodRegister extends Enum implements LocalizedEnum
{
    /**
     * EMAIL
     */
    const EMAIL = 1;

    /**
     * PHONE
     */
    const PHONE = 2;

    /**
     * FACEBOOK
     */
    const FACEBOOK = 3;

    /**
     * FACEBOOK
     */
    const GOOGLE = 4;

    /**
     * FACEBOOK
     */
    const APPLE = 5;
}
