<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class NotificationDiv
 *
 * @method static static APPROVED_IDENTIFICATION_PAPER()
 * @method static static DECLINED_IDENTIFICATION_PAPER()
 * @method static static INVITE_ADD_FRIEND()
 * @method static static CONFIRM_PRESENT_POINT()
 * @method static static INVITE_JOIN_SETTING_PARTY()
 * @method static static MATCHING_PARTY()
 * @method static static IMPORTANT()
 * @method static static CAMPAIGN()
 * @package Src\Enum
 */
class NotificationDiv extends Enum implements LocalizedEnum
{
    /**
     * Approved identification paper
     */
    public const APPROVED_IDENTIFICATION_PAPER = 1;

    /**
     * Declined identification paper
     */
    public const DECLINED_IDENTIFICATION_PAPER = 2;

    /**
     * Invite add friend
     */
    public const INVITE_ADD_FRIEND = 3;

    /**
     * Add friend
     */
    public const ADD_FRIEND = 4;

    /**
     * Invite join setting party
     */
    public const INVITE_JOIN_SETTING_PARTY = 5;

    /**
     * Matching party
     */
    public const MATCHING_PARTY = 6;

    /**
     * Important
     */
    public const IMPORTANT = 7;

    /**
     * Campaign
     */
    public const CAMPAIGN = 8;


}
