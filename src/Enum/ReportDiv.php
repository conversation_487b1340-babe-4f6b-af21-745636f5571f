<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class ReportDiv
 *
 * @package Src\Enum
 *
 * @method static static MONEY_PURPOSE()
 * @method static static COMMERCIAL_PURPOSE()
 * @method static static SEXUAL_PURPOSE()
 * @method static static DEFAMATION()
 * @method static static PROFANITY()
 * @method static static BLACKMAIL()
 * @method static static VIOLATION_PUBLIC_ORDER_MORAL()
 * @method static static SEMI_NUDE()
 */
final class ReportDiv extends Enum implements LocalizedEnum
{
    
    /**
     * Money Purpose
     */
    const MONEY_PURPOSE = 1;

    /**
     * Commercial Purpose
     */
    const COMMERCIAL_PURPOSE = 2;

    /**
     * Sexual Purpose
     */
    const SEXUAL_PURPOSE = 3;

    /**
     * Defamation
     */
    const DEFAMATION = 4;

    /**
     * Profanity
     */
    const PROFANITY = 5;

    /**
     * Blackmail
     */
    const BLACKMAIL = 6;

    /**
     * Violation Public Order Moral
     */
    const VIOLATION_PUBLIC_ORDER_MORAL = 7;

    /**
     * Semi Nude
     */
    const SEMI_NUDE = 8;

}
