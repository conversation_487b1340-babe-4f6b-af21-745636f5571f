<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class ReportStatus
 * @package Src\Enum
 *
 * @method static static UNPROCESSED()
 * @method static static PROCESSED()
 * @method static static CAN_NOT_PROCESS()
 * @method static static WITHDRAW_MEMBER_CONTACT()
 */
class ReportStatus extends Enum implements LocalizedEnum
{
    /**
     * No process
     */
    const UNPROCESSED = 1;

    /**
     * processed
     */
    const PROCESSED = 2;

    /**
     * Can not process
     */
    const CAN_NOT_PROCESS = 3;

    /**
     * Leave
     */
//    const WITHDRAW_MEMBER_CONTACT = 4;
}
