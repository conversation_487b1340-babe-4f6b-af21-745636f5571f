<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class UserStatusDiv
 * @package Src\Enum
 *
 * @method static static INITIAL()
 * @method static static FINISH_UPDATE_PROFILE()
 * @method static static FINISH_SEND_IDENTIFICATION()
 * @method static static OFFICIAL_MEMBER()
 * @method static static WITHDRAW_MEMBER()
 * @method static static STOP_SERVICE()
 */
final class UserStatus extends Enum implements LocalizedEnum
{

    /**
     * Initial
     */
    const INITIAL = 1;

    /**
     * Finish update profile
     */
    const FINISH_UPDATE_PROFILE = 2;

    /**
     * Finish send identification
     */
    const FINISH_SEND_IDENTIFICATION = 3;

    /**
     * Official member
     */
    const OFFICIAL_MEMBER = 4;

    /**
     * Withdraw member
     */
    const WITHDRAW_MEMBER = 5;

    /**
     * Stop service
     */
    const STOP_SERVICE = 6;

}
