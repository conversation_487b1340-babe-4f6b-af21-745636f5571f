<?php


namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class UseTicketType
 * @method static static ONE_TICKET()
 * @method static static TWO_TICKET()
 * @package Src\Enum
 */
class UseTicketType extends Enum implements LocalizedEnum
{
    /**
     * One ticket
     */
    const ONE_TICKET = 1;

    /**
     * Two ticket
     */
    const TWO_TICKET = 2;

}
