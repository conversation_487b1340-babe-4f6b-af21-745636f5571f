<?php

namespace Src\Enum\Traits;

use BadMethodCallException;
use Src\Utils\AbstractEnum as E;

/**
 * Enumクラスの拡張メソッドトレイト
 * コード値名を取得する。
 *
 * 取得する名前は、言語ファイル（code.php）から取得します。
 *
 * @package Src\Enum\Traits
 * @mixin E
 */
trait HasCodeName {

    /**
     * コード値の名前を取得
     *
     * @return string
     */
    public function getName(): string
    {
        return __(sprintf('code.%s.%s', snake_case(class_basename($this)), strtolower($this->getKey())));
    }

    /**
     * コード値の名前リストを取得
     *
     * @param array $filter
     * @return array
     */
    public static function getList(array $filter = []): array
    {
        $result = [];
        $items = self::toArray();
        foreach ($items as $key => $value) {
            $result[$value] = __(sprintf('code.%s.%s', snake_case(class_basename(get_called_class())), strtolower($key)));
        }
        if (0 < count($filter)) {
            $result = array_intersect_key($result, array_combine($filter, $filter));
        }
        return $result;
    }

    /**
     * コード値のキーからENUMを取得する
     *
     * @param string $code
     * @return E|null
     */
    public static function getEnumInstanceFromCode(string $code): ?E
    {
        try {
            $static_method = strtoupper($code);
            return self::$static_method();
        } catch (BadMethodCallException $e) {
            logger()->warning($e);
            return null;
        }
    }

    /**
     * Get list for vue component
     *
     * @return string
     */
    public static function getListForVueComponent(): string
    {
        return json_encode(self::getList());
    }

    /**
     * JSON用の配列にします
     *
     * {id: code, name: string}の配列
     *
     * @param array $filter
     * @return array
     */
    public static function toJsonArray(array $filter = []): array
    {
        $result = [];
        $items = self::toArray();
        foreach ($items as $key => $value) {
            $result[] = array('id' => $value, 'name' => __(sprintf('code.%s.%s', snake_case(class_basename(static::class)), strtolower($key))));
        }
        if (!empty($filter)) {
            $required = \array_combine($filter, $filter);
            $result = \array_filter($result, static function ($val) use ($required) {
                return isset($required[$val['id']]);
            });
        }
        return $result;
    }
}

