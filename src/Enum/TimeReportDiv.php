<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class ApplyTimeAfter
 * @package Src\Enum
 *
 * @method static static IMMEDIATELY()
 * @method static static AFTER_TEN_MINUTES()
 * @method static static AFTER_TWENTY_MINUTES()
 * @method static static AFTER_THIRTY_MINUTES()
 * @method static static AFTER_FORTY_MINUTES()
 * @method static static AFTER_FIFTY_MINUTES()
 * @method static static AFTER_SIXTY_MINUTES()
 * @method static static AFTER_HALF_PAST_ONE_HOURS()
 * @method static static AFTER_TWO_HOURS()
 * @method static static AFTER_HALF_PAST_TWO_HOURS()
 * @method static static AFTER_THREE_HOURS()
 * @method static static AFTER_HALF_PAST_THREE_HOURS()
 * @method static static AFTER_FOUR_HOURS()
 */
class TimeReportDiv extends Enum implements LocalizedEnum
{
    /**
     * Immediately
     */
    const IMMEDIATELY = 0;

    /**
     * After 10 minutes
     */
    const AFTER_TEN_MINUTES = 1;

    /**
     * After 20 minutes
     */
    const AFTER_TWENTY_MINUTES = 2;

    /**
     * After 30 minutes
     */
    const AFTER_THIRTY_MINUTES = 3;

    /**
     * After 40 minutes
     */
    const AFTER_FORTY_MINUTES = 4;

    /**
     * After 50 minutes
     */
    const AFTER_FIFTY_MINUTES = 5;

    /**
     * After 60 minutes
     */
    const AFTER_SIXTY_MINUTES = 6;

    /**
     * After 1 hour 30 minutes
     */
    const AFTER_HALF_PAST_ONE_HOURS = 7;

    /**
     * After 2 hours
     */
    const AFTER_TWO_HOURS = 8;

    /**
     * After 2 hours 30 minutes
     */
    const AFTER_HALF_PAST_TWO_HOURS = 9;

    /**
     * After 3 hours
     */
    const AFTER_THREE_HOURS = 10;

    /**
     * After 3 hours 30 minutes
     */
    const AFTER_HALF_PAST_THREE_HOURS = 11;

    /**
     * After 4 hours
     */
    const AFTER_FOUR_HOURS = 12;
}
