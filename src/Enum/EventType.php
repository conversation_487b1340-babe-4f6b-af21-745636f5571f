<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class EventType
 *
 * @package App\Enum
 */
final class EventType extends Enum implements LocalizedEnum
{
    // Point
    const POINT_CONFIRM = 'point.confirm';
    const POINT_SUBTRACTED = 'point.subtracted';

    // Friend
    const FRIEND_INVITE = 'friend.invite';
    const FRIEND_ADD_FRIEND_SUCCESS = 'friend.add_friend_success';

    // Message
    const MESSAGE_UPDATE_MESSAGE = 'message.update_message';
    const MESSAGE_UPDATE_CONVERSATION = 'message.update_conversation';

    // Setting Party
    const SETTING_PARTY_INVITE = 'setting_party.invite';
    const SETTING_PARTY_DELETE = 'setting_party.delete';
    const SETTING_PARTY_DELETE_TIMEOUT = 'setting_party.delete_timeout';
    const SETTING_PARTY_DECLINE = 'setting_party.decline';
    const SETTING_PARTY_LEAVE = 'setting_party.leave';

    // Party
    const PARTY_MATCHED = 'party.matched';
    const PARTY_STARTED = 'party.started';
    const PARTY_NOTICE_END = 'party.notice_end';
    const PARTY_END = 'party.end';
    const PARTY_MATCHING_TIMEOUT = 'party.matching_timeout';
    const PARTY_STAGE_USER_EXTENDED = 'party.stage_user_extended';
    const PARTY_STAGE_USER_EXPIRED = 'party.stage_user_expired';

    // Party
    const PARTY_ASK_EXTEND = 'party.ask_extend';
    const PARTY_ACCEPTED_EXTEND = 'party.accepted_extend';
    const PARTY_USER_EXTENDED = 'party.user_extended';

    // Notification
    const NOTIFICATION_NEW = 'notification.new';

    // Identification
    const APPROVED = 'identification.approve';
    const DECLINED = 'identification.decline';
}
