<?php

namespace Src\Enum;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * Class Age
 *
 * @method static static MIN()
 * @method static static MAX()
 * @package App\Enum
 */
final class Age extends Enum implements LocalizedEnum
{
    /**
     * MIN
     */
    const MIN = 18;

    /**
     * MAX
     */
    const MAX = 60;

    public static function isOptionAge(int $from_age, $to_age)
    {
        return $from_age == Age::MIN && $to_age == Age::MAX;
    }
}
