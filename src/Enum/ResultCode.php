<?php

namespace Src\Enum;

use <PERSON>Samp<PERSON>\Enum\Enum;

/**
 * Result Code
 *
 * @method static ResultCode SUCCESS()
 * @method static ResultCode ERROR()
 * @method static ResultCode ERROR_INPUT()
 * @method static ResultCode TOKEN_INVALID()
 *
 * @package Src\Enum
 */
class ResultCode extends Enum
{
    /**
     * success
     */
    public const SUCCESS = 0;

    /**
     * error
     */
    public const ERROR = 9;

    /**
     * error input valid
     */
    public const ERROR_INPUT = 7;

    /**
     * error
     */
    public const TOKEN_INVALID = 8;

    /**
     * LoginId not exist
     */
    public const LOGIN_ID_NOT_EXIST = 2000;

    public const PASSWORD_DUPLICATE = 3000;

    //------------------------- Verification ---------------------
    /**
     * ERROR_INVALID_VERIFICATION_CODE
     */
    public const ERROR_INVALID_VERIFICATION_CODE = 1000;

    //---------------------------------------------------------------
    /**
     * @param $result_code
     * @return bool
     */
    public static function isError($result_code): bool
    {
        return $result_code !== self::SUCCESS;
    }
}
