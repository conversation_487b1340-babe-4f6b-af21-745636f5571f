<?php

namespace Src\Utils\Agora\GenToken;
include('src/RtcTokenBuilder.php');

use Carbon\Carbon;
use RtcTokenBuilder;

/**
 * Class AgoraGenToken
 * @package Src\Utils\Agora\GenToken
 */
class AgoraGenToken
{
    /**
     * genAgoraTokens
     *
     * @param string $channelId
     * @param array $uids
     * @param Carbon $expire_at
     * @return array
     */
    public static function genAgoraTokens(string $channelId, array $uids, Carbon $expire_at)
    {
        $tokens = [];
        foreach ($uids as $uid) {
            $token = AgoraGenToken::genChannelToken($channelId, $uid, $expire_at);
            $tokens[] = $token;
        }

        return $tokens;
    }

    /**
     * genChannelToken
     *
     * @param string $channelId
     * @param int $uid
     * @param Carbon $expire_at
     * @return array
     */
    public static function genChannelToken(string $channelId, int $uid, Carbon $expire_at)
    {
        $appID = env('AGORA_APP_ID', '');
        $appCertificate = env('AGORA_APP_CERTIFICATE', '');
        $role = RtcTokenBuilder::RoleAttendee;

        $token = RtcTokenBuilder::buildTokenWithUid($appID, $appCertificate, $channelId, $uid, $role, $expire_at->timestamp);

        return self::createFormatAgoraTokensResponse($channelId, $uid, $token, $expire_at->format('Y-m-d H:i:s'));
    }

    /**
     * createFormatAgoraTokensResponse
     *
     * @param string $channelId
     * @param int $uid
     * @param string $token
     * @param $expired_at
     * @return array
     */
    public static function createFormatAgoraTokensResponse(string $channelId, int $uid, string $token, $expired_at)
    {
        return [
            'channel_id' => $channelId,
            'uid' => $uid,
            'token' => $token,
            'expired_at' => $expired_at
        ];
    }

}
