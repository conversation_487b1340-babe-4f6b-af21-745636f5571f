<?php

namespace Src\Utils;

use Src\Enum\ResultStatus;

/**
 * Class CustomResponse
 * @package Src\Utils
 */
class CustomResponse
{
    /** @var int */
    public $result_code;

    /** @var mixed */
    public $data;

    /** @var array|null */
    public $error;

    /**
     * CustomResponse constructor.
     * @param int $result_code
     * @param $result_data
     * @param null $result_error
     */
    public function __construct(int $result_code, $result_data, $result_error = null)
    {
        $this->result_code = $result_code;
        $this->data = $result_data;
        $this->error = $result_error;
    }

    /**
     * formatResponseSuccess
     *
     * @param array $data
     * @return array
     */
    public static function formatResponseSuccess(array $data): array
    {
        return [
            'result_status' => ResultStatus::SUCCESS,
            'data' => $data,
        ];
    }

    /**
     * getResponseData
     *
     * @return array
     */
    public function getResponseData(): array
    {
        return [
            'result_status' => $this->result_code,
            'data' => $this->data,
        ];
    }

}
