<?php


namespace Src\Utils;

use Illuminate\Support\Str;
use Src\Enum\DateFormat;

/**
 * Class GenId
 * @package Src\Utils
 */
class GenId
{

    //===================== Gen Id ==================================

    /**
     * genMemberId
     *
     * @param array $exist_ids
     * @return string
     */
    public static function genMemberId(array $exist_ids)
    {
        do {
            $new_id = self::randomNumber(7);
        } while (in_array($new_id, $exist_ids));

        return $new_id;
    }

    /**
     * genVerifyCode
     *
     * @param $exist_ids
     * @return string
     */
    public static function genVerifyCode($exist_ids)
    {
        do {
            $new_id = self::randomNumber(6);
        } while (in_array($new_id, $exist_ids));

        return $new_id;
    }

    /**
     * genRecordUid
     *
     * @return int
     */
    public static function genRecordUid(): int
    {
        return (int)self::randomNumber(8);
    }

    /**
     * genEventId
     *
     * @return string
     */
    public static function genEventId()
    {
        return Str::random(50) . Util::getCurrentFullDataString();
    }

    //===================== Gen Token ==================================

    /**
     * genPointToken
     *
     * @return string
     */
    public static function genPointToken()
    {
        return Str::random(86).Util::getCurrentFullDataString();
    }

    /**
     * genInviteFriendToken
     *
     * @return string
     */
    public static function genInviteFriendToken()
    {
        return Str::random(86).Util::getCurrentFullDataString();
    }

    //===================== Gen Other ==================================

    /**
     * genPartyChannelId
     *
     * @param $party_id
     * @return string
     */
    public static function genPartyChannelId($party_id)
    {
        return 'party_' . $party_id;
    }

    /**
     * filePrefix
     *
     * @return int
     */
    public static function filePrefix(): int
    {
        return (int)self::randomNumber(6);
    }

    //======================== Utils =============================

    /**
     * randomNumber
     *
     * @param $length
     * @return string
     */
    private static function randomNumber($length)
    {
        $result = '';

        for ($i = 0; $i < $length; $i++) {
            $result .= mt_rand(0, 9);
        }

        return $result;
    }

}
