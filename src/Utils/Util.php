<?php


namespace Src\Utils;

use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Translation\Translator;
use Src\Enum\UseTicketType;
use Throwable;

/**
 * Class Util
 * @package Src\Utils
 */
class Util
{
    /**
     * calTimeFromTicket
     *
     * @param $ticket
     * @return array
     */
    public static function calTimeFromTicket($ticket)
    {
        $time_hour = 0;
        $time_minute = 0;

        if ($ticket === 1) {
            $time_minute = 30;
        } else {
            $time = ($ticket / 2);
            if (is_int($time)) {
                $time_hour = $time;
            } else {
                $time_hour = (int)$time;
                $time_minute = 30;
            }
        }

        $time_text = $ticket . '枚';
        if ($time_hour > 0) {
            $time_text .= ' ' . $time_hour . '時間';
        }

        if ($time_minute > 0) {
            $time_text .= ' ' . $time_minute . '分';
        }

        return [
            'ticket_time_hour' => $time_hour,
            'ticket_time_minute' => $time_minute,
            'ticket_time_text' => $time_text
        ];
    }

    /**
     * getTimeEndFromUseTicketType
     *
     * @param $use_ticket_type
     * @param Carbon|null $from_time
     * @return Carbon|\Illuminate\Support\Carbon
     */
    public static function getTimeEndFromUseTicketType($use_ticket_type, Carbon $from_time = null)
    {
        $minute = 0;
        if ($use_ticket_type === UseTicketType::ONE_TICKET) {
            $minute = PARTY_NUMBER_MINUTE_FOR_ONE_TICKET;
        } else if ($use_ticket_type === UseTicketType::TWO_TICKET) {
            $minute = PARTY_NUMBER_MINUTE_FOR_ONE_TICKET * 2;
        }

        return $from_time ? $from_time->addMinutes($minute) : now()->addMinutes($minute);
    }

    /**
     * getErrorCode
     *
     * @param string $path_message
     * @return int
     */
    public static function getErrorCode(string $path_message)
    {
        return (int)__($path_message . '.code');
    }

    /**
     * getErrorMessage
     *
     * @param string $path_message
     * @param array $data_message
     * @return array|Application|Translator|string|null
     */
    public static function getErrorMessage(string $path_message, array $data_message = [])
    {
        return __($path_message . '.message', $data_message);
    }

    /**
     * formatSocketPushData
     *
     * @param string $event_tye
     * @param array $receive_user_ids
     * @param array $fcm_tokens
     * @param array $addition_data
     * @param string $message_path
     * @param array $message_placeholder
     * @param string|null $title
     * @return array
     * @throws Throwable
     */
    public static function formatSocketPushData(string $event_tye, array $receive_user_ids, array $fcm_tokens, array $addition_data
        , string $message_path, array $message_placeholder = [], string $title = null)
    {
        $title = !$title ? __('push_notification.' . $message_path . '.title') : $title;
        $body = __('push_notification.' . $message_path . '.body', $message_placeholder);

        $notify_payload = [
            'fcm_tokens' => array_except_null_value($fcm_tokens),
            'title' => $title,
            'body' => remove_newline_character($body)
        ];

        $notify_data = array_merge([
            'event_type' => $event_tye,
            'event_id' => GenId::genEventId(),
            'receive_user_ids' => $receive_user_ids,
            'body' => $body,
        ], $addition_data);

        return [$notify_payload, $notify_data];
    }

    /**
     * formatSocketPushDataWithTitleBody
     *
     * @param string $event_tye
     * @param array $receive_user_ids
     * @param array $fcm_tokens
     * @param array $addition_data
     * @param string $title
     * @param string $body
     * @return array
     * @throws Throwable
     */
    public static function formatSocketPushDataWithTitleBody(string $event_tye, array $receive_user_ids, array $fcm_tokens, array $addition_data
        , string $title, string $body)
    {

        $notify_payload = [
            'fcm_tokens' => array_except_null_value($fcm_tokens),
            'title' => $title,
            'body' => remove_newline_character($body)
        ];

        $notify_data = array_merge([
            'event_type' => $event_tye,
            'event_id' => GenId::genEventId(),
            'receive_user_ids' => $receive_user_ids,
            'body' => $body,
        ], $addition_data);

        return [$notify_payload, $notify_data];
    }

    /**
     * formatSocketData
     *
     * @param array $receive_user_ids
     * @param array $addition_data
     * @return array
     */
    public static function formatSocketData(array $receive_user_ids, array $addition_data)
    {
        return array_merge([
            'receive_user_ids' => $receive_user_ids
        ], $addition_data);
    }

    /**
     * getCurrentFullDataString
     *
     * @return string
     */
    public static function getCurrentFullDataString(){
        return now()->format('YmdHis');
    }

    /**
     * calculateTime
     *
     * @param Carbon $start_date
     * @param Carbon $end_date
     * @return string|null
     */
    public static function calculateTime(?Carbon $start_date, ?Carbon $end_date): ?string
    {
       if($start_date && $end_date) {
           return $start_date->diffInHours($end_date) . ':' . $start_date->diff($end_date)->format('%I:%S');
       }else{
           return null;
       }
    }

}
