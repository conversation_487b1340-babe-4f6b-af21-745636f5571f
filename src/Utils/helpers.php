<?php

use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Pagination\AbstractPaginator;
use Src\Domain\Api\Services\PushNotificationService;
use Src\Enum\DateTimeFormat;
use Src\Enum\ResultCode;
use Src\Exception\APIRuntimeException;
use Src\Utils\GenId;
use Src\Utils\Util;

if (!function_exists('snake_case')) {
    /**
     * Convert a string to snake case.
     *
     * @param string $value
     * @param string $delimiter
     * @return string
     *
     * @deprecated Str::snake() should be used directly instead. Will be removed in Laravel 5.9.
     */
    function snake_case($value, $delimiter = '_')
    {
        return Str::snake($value, $delimiter);
    }
}

if (!function_exists('format_date_time')) {
    /**
     * Format date time
     *
     * @param Carbon|DateTime $value
     * @param DateTimeFormat $type
     * @return string
     */
    function format_date_time($value, \Src\Enum\DateTimeFormat $type)
    {
        try {
            switch ($type) {
                case DateTimeFormat::FULL():
                    return $value->format(__('common.date-format.full'));
                case DateTimeFormat::DATETIME():
                    return $value->format(__('common.date-format.datetime'));
                case DateTimeFormat::DATE():
                    return $value->format(__('common.date-format.date'));
                case DateTimeFormat::TIME():
                    return $value->format(__('common.date-format.time'));
                default:
                    return $value;
            }
        } catch (\Throwable $e) {
            return $value;
        }
    }
}

if (!function_exists('json_response')) {
    /**
     * Json response api
     *
     * @param $result_code
     * @param null $result_detail
     * @param null $result_error
     * @return \Illuminate\Http\JsonResponse
     */
    function json_response($result_code, $result_detail = null, $result_error = null)
    {
        $obj_to_json = [
            'result_code' => $result_code,
            'result_detail' => $result_detail,
//            'result_error' => $result_error,
        ];

        if (ResultCode::isError($result_code) && $result_error) {
            if (is_string($result_error)) {
                $response_error['code'] = Util::getErrorCode('api_errors.main.internal_server_error');
                $response_error['message'] = $result_error;
            } else {
                $response_error = $result_error;
            }

            /*$response_error = [
                'message' => null,
//                'detail' => null,
            ];
            if (is_string($result_error)) {
                $response_error['message'] = $result_error;
            } else {
                if (isset($result_error['message'])) {
                    $response_error = $result_error;
                } else {
                    $response_error['detail'] = $result_error;
                }
            }*/

            $obj_to_json['result_error'] = $response_error;
        }

        return response()->json($obj_to_json);
    }
}

if (!function_exists('pagination_formatter')) {
    /**
     * @param LengthAwarePaginator|AbstractPaginator $paginator $paginator
     * @param array $options
     * @return array
     */
    function pagination_formatter($paginator, array $options)
    {
        $paginator_data = $paginator->toArray();
        $result = [];
        $result['data'] = $paginator_data['data'] ?? [];
        $result['paginator'] = [
            'current_page' => $paginator_data['current_page'],
            'first_page_url' => $paginator_data['first_page_url'],
            'from' => $paginator_data['from'],
            'last_page' => $paginator_data['last_page'],
            'last_page_url' => $paginator_data['last_page_url'],
            'next_page_url' => $paginator_data['next_page_url'],
            'path' => $paginator_data['path'],
            'per_page' => $paginator_data['per_page'],
            'prev_page_url' => $paginator_data['prev_page_url'],
            'to' => $paginator_data['to'],
            'total' => $paginator_data['total']
        ];

        if (!empty($options)) {
            $result = array_merge($result, $options);
        }

        return $result;
    }
}
if (!function_exists('array_get_int')) {
    /**
     * Arr::get()して、nullでなければintへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|int
     */
    function array_get_int(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (int)$value;
    }
}
if (!function_exists('array_get_float')) {
    /**
     * Arr::get()して、nullでなければfloatへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|float
     */
    function array_get_float(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (float)$value;
    }
}
if (!function_exists('array_get_string')) {
    /**
     * Arr::get()して、nullでなければstringへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|string
     */
    function array_get_string(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (string)$value;
    }
}
if (!function_exists('array_get_bool')) {
    /**
     * Arr::get()して、nullでなければboolへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|bool
     */
    function array_get_bool(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (bool)$value;
    }
}
if (!function_exists('array_get_array')) {
    /**
     * Arr::get()して、nullでなければarrayへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|array
     */
    function array_get_array(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        return (null === $value) ? null : (array)$value;
    }
}
if (!function_exists('array_get_carbon')) {
    /**
     * Arr::get()して、nullでなければCarbonへキャストします。
     *
     * @param array $array
     * @param string $key
     * @param mixed $default
     * @return null|\Carbon\Carbon
     */
    function array_get_carbon(array $array, string $key, $default = null)
    {
        $value = Arr::get($array, $key, $default);
        $result = null;
        if ($value instanceof \Carbon\Carbon) {
            $result = $value;
        } else if (is_string($value)) {
            // フォーマットチェックまではとりあえずしない。
            $result = \Carbon\Carbon::parse($value);
        }
        return $result;
    }
}
if (!function_exists('str_random')) {
    /**
     * Generate a more truly "random" alpha-numeric string.
     *
     * @param int $length
     * @return string
     *
     * @throws \RuntimeException
     */
    function str_random($length = 16)
    {
        return Str::random($length);
    }
}

if (!function_exists('request_socket_api_and_push_notification')) {

    function request_socket_api_and_push_notification($config_path, $data, $notification)
    {
        logger()->debug('request_socket_api_and_push_notification', [$notification]);
//        $data['event_id'] = GenId::genEventId();
        $is_method_post = true;
        try {
            logger()->debug("socket params: ", $data);
            // Send socket
            $url = config('socket_api.base_url') . config($config_path);
            logger()->debug('URL_SOCKET', [$url]);
            if ($is_method_post) {
                $response = Http::post($url, $data);
            } else {
                $response = Http::get($url, $data);
            }

            if ($response->ok()) {
                logger()->debug("Success request to socket api: $url", [$response->json()]);
            } else {
                logger()->error("Error request to socket api: $url");
            }

            // Send push notification
            if( $notification && !empty($notification)){
                PushNotificationService::sendPushNotificationMultiDevice($notification, $data);
            }
        } catch (\Throwable $e) {
            logger()->error('ERROR_CONNECT_SOCKET_API_AND_PUSH', [$e]);
        }
    }
}



if (!function_exists('request_socket_api')) {

    function request_socket_api($config_path, $data)
    {
//        $data['event_id'] = GenId::genEventId();
        $is_method_post = true;
        try {
            logger()->debug("socket params: ", $data);
            // Send socket
            $url = config('socket_api.base_url') . config($config_path);

            if ($is_method_post) {
                $response = Http::post($url, $data);
            } else {
                $response = Http::get($url, $data);
            }

            if ($response->ok()) {
                logger()->debug("Success request to socket api: $url", [$response->json()]);
            } else {
                logger()->error("Error request to socket api: $url");
            }

        } catch (\Throwable $e) {
            logger()->error('ERROR_CONNECT_SOCKET_API', [$e]);
        }
    }
}

if (!function_exists('request_push_notification')) {

    function request_push_notification($data, $notification)
    {
        logger()->debug('request_push_notification', [$notification]);
//        $data['event_id'] = GenId::genEventId();

        try {
            PushNotificationService::sendPushNotificationMultiDevice($notification, $data);
        } catch (\Throwable $e) {
            logger()->error('ERROR_REQUEST_PUSH_NOTIFICATION', [$e]);
        }
    }
}

if (!function_exists('array_except_null_value')) {

    function array_except_null_value(array $arr)
    {
        return array_filter($arr, function($value) {
            return isset($value);
        });
    }
}


if (!function_exists('is_same_value')) {

    function is_same_value($arr, $compare_value)
    {
        return count(array_unique($arr)) === 1 && end($arr) === $compare_value;

    }
}


if (!function_exists('get_now_timestamp')) {

    function get_now_timestamp()
    {
        return Carbon::now()->toDateTimeString();

    }
}

if (!function_exists('throw_if_api_exception')) {

    /**
     * throw_if_api_exception
     *
     * @param Throwable $e
     * @throws Throwable
     */
    function throw_if_api_exception(Throwable $e)
    {
        if($e instanceof APIRuntimeException){
            throw $e;
        }

        if($e instanceof ModelNotFoundException){
            throw $e;
        }
    }
}
if (!function_exists('remove_newline_character')) {

    /**
     * throw_if_api_exception
     *
     * @param Throwable $e
     * @throws Throwable
     */
    function remove_newline_character(string $message)
    {
        return  str_replace("\n", '', $message);

    }
}

if (!function_exists('format_json_encode')) {

    /**
     * throw_if_api_exception
     *
     * @param Throwable $e
     * @throws Throwable
     */
    function dd_with_format(array $data)
    {
        return  dd(json_encode($data, JSON_PRETTY_PRINT));

    }
}

if (!function_exists('sort_list_friends')) {
    function sort_list_friends($a, $b) {
        $fca = ord(substr($a['profile']['nickname'], 0, 1)); $fcb = ord(substr($b['profile']['nickname'], 0, 1));
        if (($fca >= 127 && $fcb >= 127) || ($fca < 127 && $fcb < 127))
            $res = strtolower($a['profile']['nickname']) > strtolower($b['profile']['nickname']) ? 1 : -1;
        else
            $res = strtolower($a['profile']['nickname']) > strtolower($b['profile']['nickname']) ? -1 : 1;
        return $res;
    }
}


if (!function_exists('throw_model_not_found_if_not_exist')) {
    function throw_model_not_found_if_not_exist($model, $class_model) {
        if(!$model){
            throw (new ModelNotFoundException)->setModel(
                $class_model, $model
            );
        }
    }
}


