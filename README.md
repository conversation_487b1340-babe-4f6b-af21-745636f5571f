## vagrant 
```
vagrant up
vagrant ssh
cd /opt/app/rimocha
```


## DB init
```
sudo grep 'temporary password' /var/log/mysqld.log (get generate pass login for root)

# access with temporary password
mysql -u root -p
ALTER USER 'root'@'localhost' IDENTIFIED BY 'V1n1c0rp@$' \g
exit

# init DB
sudo MYSQL_PWD=V1n1c0rp@$ mysql -uroot < /opt/app/rimocha/vagrant/sql/bootstrap.sql
```

## first init
```
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link

php artisan ide-helper:generate
php artisan vendor:publish --provider="Laracasts\Flash\FlashServiceProvider"
```

## generate lang
```
php artisan vue-i18n:generate
```

## generate route
 ```
php artisan ziggy:generate "resources/js/ssr-route/ziggy.js"
```

## yarn
```
yarn run dev
```

## laravel passport
```
# generate passort database
php artisan migrate

# generate passpost key
php artisan passport:install

#add header to request api
Content-Type: application/json
X-Requested-With: XMLHttpRequest
Authorization: Bearer ACCESS_TOKEN_KEY
```


| page       | URL                                                |
|:-----------|:---------------------------------------------------|
| admin page |     *************/admin/auth/login                 |
