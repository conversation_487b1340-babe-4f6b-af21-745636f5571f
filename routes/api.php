<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Master data
Route::get('/zipcode-data', 'MasterController@getZipcodeData');
Route::get('/emergency-relation', 'MasterController@getEmergencyRelation');
Route::get('/job-categories', 'MasterController@getJobCategories');
Route::get('/residence-types', 'MasterController@getResidenceTypes');
Route::get('/countries', 'MasterController@getCountries');
Route::get('/banks', 'MasterController@getBanks');
Route::get('/bank/{code}/branches', 'MasterController@getBankBranches');

// Auth
Route::post('/login', 'AuthController@login');
Route::post('/sign-up', 'AuthController@signUp')->name('sign-up');
Route::post('/verify-email', 'EmailVerificationController@verifyEmail')->name('verify.email');
Route::post('/forgot-password', 'AuthController@forgotPassword')->name('forgot-password');
Route::post('/reset-password', 'AuthController@resetPassword')->name('reset-password');

// Job
Route::get('/job', 'JobController@index');

// Public Announcement
Route::get('/announcement', 'NotificationController@announcementIndex');

Route::group(['middleware' => 'auth:api'], function () {
    // Auth
    Route::post('/logout', 'AuthController@logout');
    Route::patch('/change-password', 'AuthController@changePassword')->name('change-password');
    Route::get('/get-profile', 'UserController@getProfile')->name('get-profile');

    // Notification
    Route::get('/notification', 'NotificationController@index')->name('notification.index');
    Route::get('/notification/{id}', 'NotificationController@show')->name('notification.show');

    Route::get('/job/{id}/show', 'JobController@show');
    Route::get('/job-user-applied', 'JobController@listJobUserApplied');
    Route::post('/cancel-job-applied', 'JobController@cancelJobApplied');

    Route::post('/update-profile', 'UserController@updateProfile');
    Route::post('/job/{id}/application', 'JobController@applyToJob');
    Route::post('/job/{id}/favorite', 'JobController@addToFavorites');
    Route::delete('/job/{id}/favorite', 'JobController@removeToFavorite');
});

