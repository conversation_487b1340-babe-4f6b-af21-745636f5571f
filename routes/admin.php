<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::middleware(['guest:admin'])->group(function () {
    Route::get('/auth/login', 'Auth\LoginController@show')->name('auth.show');
    Route::post('/auth/login', 'Auth\LoginController@login')->name('auth.login');

});

Route::group(['middleware' => ['auth:admin']], function () {
    // logout
    Route::get('/auth/logout', 'Auth\LoginController@logout')->name('auth.logout');

    //Profile
    Route::get('/profile', 'Account\ViewController@show')->name('profile.show');
    Route::get('/profile/{account_id}/edit', 'Account\EditController@edit')->name('profile.edit');
    Route::put('/profile/{account_id}/update', 'Account\EditController@update')->name('profile.update');
    Route::get('/profile/{account_id}/password/form', 'Account\PasswordController@form')->name('profile.password.form');
    Route::put('/profile/{account_id}/password/update', 'Account\PasswordController@changePassword')->name('profile.password.update');

    // Home
    Route::get('/home', 'Home\IndexController@index')->name('home.index');

    // Account
    Route::get('/account', 'Account\ViewController@index')->name('account.index');
    Route::get('account/form', 'Account\CreateController@create')->name('account.create');
    Route::post('account/store', 'Account\CreateController@store')->name('account.store');
    Route::get('/account/{account_id}/show', 'Account\ViewController@show')->name('account.show');
    Route::delete('/account/{account_id}', 'Account\DeleteController@delete')->name('account.delete');

    //User
    Route::post('/user/upload-avatar', 'User\UploadStorage\UploadController@uploadAvatar')->name('user.upload-avatar');
    Route::get('/user', 'User\ViewController@index')->name('user.index');
    Route::get('/user/form', 'User\CreateController@create')->name('user.create');
    Route::post('/user/store', 'User\CreateController@store')->name('user.store');
    Route::get('/user/{user_id}/form', 'User\EditController@edit')->name('user.edit');
    Route::put('/user/{user_id}/update', 'User\EditController@update')->name('user.update');
    Route::get('/user/show/{user_id}/{index}', 'User\ViewController@show')->name('user.show');
    Route::delete('/user/{user_id}', 'User\DeleteController@delete')->name('user.delete');

    //User update warning
    Route::post('/user/{user_id}/update-warning', 'User\UserInformDetail\UpdateWarningController@store')->name('user.update-warning');

    // Get User friend
    Route::get('/user/{user_id}/friend', 'User\UserInformDetail\UserFriendController@index')->name('user.friend');

    // User Message
    Route::get('/user/{user_id}/{partner_id}/message/show', 'User\UserInformDetail\UserMessageController@show')->name('user.message.show');

    // Get User Buy Ticket
    Route::get('/user/{user_id}/get-buy-ticket', 'User\UserInformDetail\UserTicketController@index')->name('user.get-buy-ticket');

    // Get User Buy Point
    Route::get('/user/{user_id}/get-buy-point', 'User\UserInformDetail\UserPointController@getBuyPoint')->name('user.get-buy-point');

    // Get User Present Point
    Route::get('/user/{user_id}/get-present-point', 'User\UserInformDetail\UserPointController@getPresentPoint')->name('user.get-present-point');

    // Get User Receive Point
    Route::get('/user/{user_id}/get-receive-point', 'User\UserInformDetail\UserPointController@getReceivePoint')->name('user.get-receive-point');

    // Get User Block
    Route::get('/user/{user_id}/get-user-block','User\UserInformDetail\UserBlockController@index')->name('user.get-user-block');

    // Get User Report
    Route::get('/user/{user_id}/get-user-report', 'User\UserInformDetail\UserReportController@index')->name('user.get-user-report');

    // Get User Contact
    Route::get('/user/{user_id}/get-user-contact', 'User\UserInformDetail\UserContactController@index')->name('user.get-user-contact');
    Route::get('/user/{user_id}/get-user-contact/{contact_id}/show', 'User\UserInformDetail\UserContactController@show')->name('user.get-user-contact.detail');

    // Report
    Route::get('report/show', 'User\UserInformDetail\UserReportController@show')->name('detail_user_report.show');

    // Contact
    Route::get('contact/show', 'User\UserInformDetail\UserContactController@show')->name('detail_user_contact.show');

    // Report Feedback
    Route::get('report/feedback/create', 'FeedbackReport\CreateController@create')->name('feedback_report.create');
    Route::post('report/feedback/store', 'FeedbackReport\CreateController@store')->name('feedback_report.store');
    Route::get('report/feedback/{feedback_id}', 'FeedbackReport\ViewController@show')->name('feedback_report.show');

    // Contact Feedback
    Route::get('contact/feedback/create', 'FeedbackContact\CreateController@create')->name('feedback_contact.create');
    Route::post('contact/feedback/store', 'FeedbackContact\CreateController@store')->name('feedback_contact.store');
    Route::get('contact/feedback/{feedback_id}', 'FeedbackContact\ViewController@show')->name('feedback_contact.show');

    // Notification
    Route::get('/notification', 'Notification\ViewController@index')->name('notification.index');
    Route::get('/notification/create', 'Notification\CreateController@create')->name('notification.create');
    Route::post('/notification/store', 'Notification\CreateController@store')->name('notification.store');
    Route::get('/notification/{notification_id}', 'Notification\ViewController@show')->name('notification.show');

    // User Report
    Route::get('/user_report', 'UserReport\ViewController@index')->name('user_report.index');
    Route::post('/user_report', 'UserReport\CreateController@store')->name('user_report.store');
    Route::get('/user_report/{user_report_id}', 'UserReport\ViewController@show')->name('user_report.show');
    Route::get('/user_report/{user_report_id}/edit', 'UserReport\EditController@edit')->name('user_report.edit');
    Route::post('/user_report/{user_report_id}', 'UserReport\EditController@update')->name('user_report.update');
    Route::delete('/user_report/{user_report_id}', 'UserReport\DeleteController@delete')->name('user_report.delete');

    // User Contact
    Route::get('/user_contact', 'UserContact\ViewController@index')->name('user_contact.index');
    Route::get('/user_contact/create', 'UserContact\CreateController@create')->name('user_contact.create');
    Route::post('/user_contact', 'UserContact\CreateController@store')->name('user_contact.store');
    Route::get('/user_contact/{user_contact_id}', 'UserContact\ViewController@show')->name('user_contact.show');
    Route::get('/user_contact/{user_contact_id}/edit', 'UserContact\EditController@edit')->name('user_contact.edit');
    Route::post('/user_contact/{user_contact_id}', 'UserContact\EditController@update')->name('user_contact.update');
    Route::delete('/user_contact/{user_contact_id}', 'UserContact\DeleteController@delete')->name('user_contact.delete');

    // Party
    Route::get('/party', 'Party\ViewController@index')->name('party.index');

    // User Verification
    Route::get('/user_verification', 'UserVerification\ViewController@index')->name('user_verification.index');
    Route::post('/user_verification/approve/{user_id}', 'UserVerification\ApproveController@approve')->name('user_verification.approve');

    // Guest Contact
    Route::get('/guest_contact','GuestContact\ViewController@index')->name('guest_contact.index');
    Route::get('/guest_contact/{guest_contact_id}/show', 'GuestContact\ViewController@show')->name('guest_contact.show');

    //Feedback Guest Contact
    Route::get('/guest_contact/{guest_contact_id}/feedback/create', 'FeedbackGuestContact\CreateController@create')->name('feedback_guest_contact.create');
    Route::post('/guest_contact/feedback/store', 'FeedbackGuestContact\CreateController@store')->name('feedback_guest_contact.store');
    Route::get('/guest_contact/feedback/{feedback_id}', 'FeedbackGuestContact\ViewController@show')->name('feedback_guest_contact.show');

    //Download CSV
    Route::get('/user/download_csv','DownloadCSV\UserCSVController@download')->name('user.download_csv');
    Route::get('/user/contact/download_csv','DownloadCSV\UserContactCSVController@download')->name('user_contact.download_csv');

// Agora
    Route::get('/aggregate', 'Agora\CreateController@aggregate')->name('agora.aggregate');
    Route::get('/acquire', 'Agora\CreateController@acquire')->name('agora.acquire');
    Route::get('/start', 'Agora\CreateController@start')->name('agora.start');
    Route::get('/query', 'Agora\CreateController@query')->name('agora.query');
    Route::get('/stop', 'Agora\CreateController@stop')->name('agora.stop');

    // Play Video Agora
    Route::get('/party/{party_history_id}/play_video','Party\PlayVideoController@index')->name('party.play_video');

});


