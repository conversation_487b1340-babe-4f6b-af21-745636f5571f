<?php

use App\Eloquent\Account;
use Illuminate\Database\Seeder;

/**
 * Class AccountSeeder
 */
class AccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws Throwable
     */
    public function run(): void
    {
        DB::transaction(static function () {
            Account::query()->create([
                'login_id' => 'admin',
                'password' => bcrypt('Rimocha@#20'),
                'email' => '<EMAIL>'
            ]);
        });
    }

}
