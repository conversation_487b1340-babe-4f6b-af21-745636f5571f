<?php

use App\Eloquent\Account;
use Illuminate\Database\Seeder;

/**
 * Class DevInitSeeder
 */
class DevInitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws Throwable
     */
    public function run(): void
    {
//        $this->call(PointPlanSeeder::class);
//        $this->call(TicketPlanSeeder::class);
//        $this->call(AccountSeeder::class);


//        DB::transaction(function () {
//            $this->localGeneralAccount();
//        });
    }

    /**
     * localGeneralAccount
     */
    private function localGeneralAccount(): void
    {
        Account::query()->create([
            'login_id' => 'admin',
            'password' => bcrypt('Rimocha@#20'),
            'email' => '<EMAIL>'
        ]);

    }
}
