<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterSettingParty extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('setting_parties', function (Blueprint $table) {
            $table->unsignedTinyInteger('from_age_partner')->after('to_age');
            $table->unsignedTinyInteger('to_age_partner')->after('to_age');
            $table->dateTime('update_status_at')->after('to_age');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('setting_parties', function (Blueprint $table) {
            $table->dropColumn('from_age_partner');
            $table->dropColumn('to_age_partner');
            $table->dropColumn('update_status_at');
        });
    }
}
