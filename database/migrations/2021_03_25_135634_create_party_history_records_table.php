<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartyHistoryRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('party_history_records', function (Blueprint $table) {
            $table->unsignedInteger('party_history_id');
            $table->string('channel_id');
            $table->string('record_token');
            $table->integer('record_uid');
            $table->string('record_resource_id');
            $table->string('record_sid');
            $table->tinyInteger('record_status');
            $table->string('record_file_id');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('party_history_records');
    }
}
