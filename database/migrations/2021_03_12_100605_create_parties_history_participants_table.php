<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartiesHistoryParticipantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('parties_history_participants', function (Blueprint $table) {
            $table->integer('user_id')->unsigned()->unique();
            $table->integer('party_id')->unsigned()->unique();
            $table->integer('parties_history_group_id')->unsigned();
            $table->integer('party_participant_status')->unsigned();
            $table->dateTime('started_at')->nullable();
            $table->dateTime('ended_at')->nullable();
            $table->dateTime('expire_at')->nullable();
            $table->tinyInteger('number_extended')->default(0);
            $table->time('party_time')->nullable();
            $table->time('real_party_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('parties_history_participants');
    }
}
