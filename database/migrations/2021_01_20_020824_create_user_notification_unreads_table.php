<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserNotificationUnreadsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('user_notification_unreads', function(Blueprint $table)
		{
			$table->integer('user_id')->unsigned()->primary('pk_user_notification_unreads_user_id');
			$table->integer('number_unread_notification')->default(0);
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('user_notification_unreads');
	}

}
