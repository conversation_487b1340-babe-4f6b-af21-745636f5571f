<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUsersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('users', function(Blueprint $table)
		{
			$table->increments('id');
			$table->string('member_id', 7);
			$table->string('password', 160);
			$table->string('email');
			$table->integer('mobile_platform')->unsigned()->default(1);
			$table->string('fcm_token')->nullable();
			$table->integer('user_status')->unsigned()->default(1);
			$table->unsignedTinyInteger('is_warning')->default(1);
			$table->date('last_access_on')->nullable();
			$table->dateTime('registered_at');
			$table->softDeletes();
			$table->dateTime('created_at')->nullable();
			$table->dateTime('updated_at')->nullable();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('users');
	}

}
