<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePartyExtendAcceptsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('party_extend_accepts', function(Blueprint $table)
		{
			$table->integer('party_id');
			$table->integer('user_id')->unsigned();
			$table->integer('is_extended');
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
			$table->primary(['party_id','user_id'], 'pk_party_extend_accepts_party_id_user_id');
			$table->index(['user_id'], 'fk_parties_has_users_users1_idx');
			$table->index(['party_id'], 'fk_parties_has_users_parties1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('party_extend_accepts');
	}

}
