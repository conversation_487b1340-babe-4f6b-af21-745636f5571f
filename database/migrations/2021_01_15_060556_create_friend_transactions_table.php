<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateFriendTransactionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('friend_transactions', function(Blueprint $table)
		{
			$table->integer('id', true);
			$table->integer('send_user_id')->unsigned();
			$table->integer('receive_user_id')->unsigned();
			$table->string('invite_token', 100)->unique('point_token_UNIQUE');
			$table->dateTime('created_at')->nullable();
			$table->index(['send_user_id'], 'fk_point_transactions_users1_idx');
			$table->index(['receive_user_id'], 'fk_point_transactions_users2_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('friend_transactions');
	}

}
