<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePartyGroupsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('party_groups', function(Blueprint $table)
		{
			$table->integer('id', true);
			$table->integer('party_id');
			$table->unsignedTinyInteger('gender');
			$table->unsignedTinyInteger('gender_partner')->default(1);
			$table->integer('from_age');
			$table->integer('to_age');
			$table->integer('from_age_partner');
			$table->integer('to_age_partner');
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
			$table->index(['party_id'], 'fk_party_groups_parties1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('party_groups');
	}

}
