<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('messages', function(Blueprint $table)
		{
			$table->increments('id');
			$table->integer('message_conversation_id')->unsigned();
			$table->integer('sender_id')->unsigned();
			$table->softDeletes();
			$table->string('message')->default('');
			$table->dateTime('created_at');
			$table->index(['sender_id'], 'fk_messages_users1_idx');
			$table->index(['message_conversation_id'], 'fk_messages_conversation1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('messages');
	}

}
