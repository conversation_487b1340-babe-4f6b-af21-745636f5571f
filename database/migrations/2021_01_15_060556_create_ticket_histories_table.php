<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTicketHistoriesTable extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ticket_histories', function(Blueprint $table)
        {
            $table->increments('id');
            $table->integer('user_id')->unsigned();
            $table->unsignedTinyInteger('ticket_plan_div');
            $table->integer('ticket_update_div')->unsigned();
            $table->unsignedTinyInteger('buy_situation')->nullable();
            $table->integer('party_id')->nullable();
            $table->integer('ticket_plan_id')->unsigned()->nullable();
            $table->string('name', 100)->nullable();
            $table->integer('money')->unsigned()->nullable();
            $table->integer('ticket')->unsigned();
            $table->integer('time_hour')->unsigned();
            $table->integer('time_minute')->unsigned();
            $table->integer('old_ticket')->unsigned();
            $table->integer('new_ticket')->unsigned();
            $table->dateTime('ticket_at');
            $table->softDeletes();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->index(['ticket_plan_id'], 'fk_ticket_histories_ticket_plans1_idx');
            $table->index(['party_id'], 'fk_ticket_histories_parties1_idx');
            $table->index(['user_id'], 'user_id');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ticket_histories');
    }

}
