<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateDeletedMessageConversationsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('deleted_message_conversations', function(Blueprint $table)
		{
			$table->integer('message_conversation_id')->unsigned();
			$table->integer('user_id')->unsigned();
			$table->dateTime('created_at');
			$table->primary(['message_conversation_id','user_id'], 'pk_deleted_message_conversations_message_conversation_id_user_id');
			$table->index(['message_conversation_id'], 'fk_delete_conversations_conversation1_idx');
			$table->index(['user_id'], 'fk_delete_conversations_users1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('deleted_message_conversations');
	}

}
