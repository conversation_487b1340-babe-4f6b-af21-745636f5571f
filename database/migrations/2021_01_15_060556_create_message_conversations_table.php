<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMessageConversationsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('message_conversations', function(Blueprint $table)
		{
			$table->increments('id');
			$table->integer('creator_id')->unsigned();
			$table->string('channel_id', 45);
			$table->softDeletes();
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
			$table->index(['creator_id'], 'fk_conversation_users1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('message_conversations');
	}

}
