<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRefundPointsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('refund_points', function (Blueprint $table) {
            $table->integer('point_history_id')->primary('fk_refund_points_point_history_id');
            $table->integer('refund_ratio_id');
            $table->integer('refund_money');
            $table->mediumInteger('refund_point');
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->index(['point_history_id'], 'point_history_id');
            $table->index(['refund_ratio_id'], 'refund_ratio_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('refund_points');
    }
}
