<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePointPlansTable extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('point_plans', function(Blueprint $table)
        {
            $table->integer('id', true);
            $table->string('name', 100);
            $table->string('ios_package_id', 100);
            $table->string('android_package_id', 100);
            $table->integer('point')->unsigned();
            $table->integer('money')->unsigned();
            $table->softDeletes();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('point_plans');
    }

}
