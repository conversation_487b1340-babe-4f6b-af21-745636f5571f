<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateDeletedMessagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('deleted_messages', function(Blueprint $table)
		{
			$table->integer('message_id')->unsigned();
			$table->integer('user_id')->unsigned();
			$table->dateTime('created_at');
			$table->primary(['message_id','user_id'], 'pk_deleted_messages_message_id_user_id');
			$table->index(['message_id'], 'fk_deleted_messages_messages1_idx');
			$table->index(['user_id'], 'fk_deleted_messages_users1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('deleted_messages');
	}

}
