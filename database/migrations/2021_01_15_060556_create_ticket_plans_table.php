<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTicketPlansTable extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ticket_plans', function(Blueprint $table)
        {
            $table->increments('id');
            $table->string('name', 100)->unique('name_UNIQUE');
            $table->unsignedTinyInteger('ticket_plan_div');
            $table->integer('normal_money')->unsigned();
            $table->integer('money')->unsigned();
            $table->integer('ticket')->unsigned();
            $table->integer('time_hour')->unsigned();
            $table->integer('time_minute')->unsigned();
            $table->string('ios_package_id', 100);
            $table->string('android_package_id', 100);
            $table->softDeletes();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ticket_plans');
    }

}
