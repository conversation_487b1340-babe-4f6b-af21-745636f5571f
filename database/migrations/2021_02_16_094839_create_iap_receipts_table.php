<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateIapReceiptsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('iap_receipts', function(Blueprint $table)
		{
			$table->increments('id');
			$table->integer('mobile_platform')->unsigned();
			$table->string('transaction_id', 50)->unique();
			$table->string('product_id', 100);
			$table->dateTime('purchase_at');
			$table->dateTime('created_at');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('iap_receipts');
	}

}
