<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserBlocksTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('user_blocks', function(Blueprint $table)
		{
			$table->integer('user_id')->unsigned();
			$table->integer('block_user_id')->unsigned();
			$table->dateTime('created_at')->nullable();
			$table->dateTime('updated_at')->nullable();
			$table->primary(['user_id','block_user_id'], 'pk_user_blocks_user_id_block_user_id');
			$table->unique(['user_id','block_user_id'], 'user_id');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('user_blocks');
	}

}
