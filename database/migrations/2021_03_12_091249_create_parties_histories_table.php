<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartiesHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('parties_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('party_id')->unique();
            $table->tinyInteger('invite_member_type')->unsigned();
            $table->tinyInteger('group_type')->unsigned();
            $table->integer('creator_id')->unsigned();
            $table->dateTime('match_at');
            $table->dateTime('start_at')->nullable();
            $table->dateTime('expire_at')->nullable();
            $table->dateTime('ended_at')->nullable();
            $table->time('party_time')->nullable();
            $table->time('real_party_time')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('parties_histories');
    }
}
