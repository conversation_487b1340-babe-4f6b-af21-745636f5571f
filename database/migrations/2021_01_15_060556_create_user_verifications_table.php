<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserVerificationsTable extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_verifications', function(Blueprint $table)
        {
            $table->integer('user_id')->unsigned()->primary('pk_user_verifications_user_id');
            $table->unsignedTinyInteger('identification_type');
            $table->unsignedTinyInteger('approval_status');
            $table->integer('storage_file_id')->unsigned();
            $table->dateTime('created_at')->nullable();
            $table->dateTime('updated_at')->nullable();
            $table->index(['user_id'], 'user_id');
            $table->index(['storage_file_id'], 'storage_file_id');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_verifications');
    }

}
