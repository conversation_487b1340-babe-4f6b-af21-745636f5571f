<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUserProfilesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('user_profiles', function(Blueprint $table)
		{
			$table->integer('user_id')->unsigned()->primary('pk_user_profiles_user_id');
			$table->integer('avatar_id')->unsigned()->nullable();
			$table->string('nickname', 100)->nullable();
			$table->unsignedTinyInteger('gender');
			$table->unsignedTinyInteger('gender_partner');
            $table->date('birthday')->nullable();
			$table->string('occupation', 100)->nullable();
			$table->dateTime('created_at')->nullable();
			$table->dateTime('updated_at')->nullable();
			$table->index(['avatar_id'], 'avatar_id');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('user_profiles');
	}

}
