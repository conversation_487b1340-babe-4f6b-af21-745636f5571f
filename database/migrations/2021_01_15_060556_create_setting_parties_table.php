<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSettingPartiesTable extends Migration {

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('setting_parties', function(Blueprint $table)
        {
            $table->increments('id');
            $table->unsignedTinyInteger('invite_member_type');
            $table->unsignedTinyInteger('group_type');
            $table->unsignedTinyInteger('gender')->default(1);
            $table->integer('from_age')->nullable();
            $table->integer('to_age')->nullable();
            $table->unsignedTinyInteger('status')->default(1);
            $table->integer('creator_id')->unsigned();
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            $table->index(['creator_id'], 'fk_setting_parties_users_idx');
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('setting_parties');
    }

}
