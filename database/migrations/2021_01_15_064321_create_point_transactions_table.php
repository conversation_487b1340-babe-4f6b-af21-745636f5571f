<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePointTransactionsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('point_transactions', function(Blueprint $table)
		{
			$table->integer('id', true);
			$table->integer('user_id')->unsigned();
			$table->integer('receive_user_id')->unsigned();
			$table->integer('point_plan_id');
			$table->string('point_token', 100)->unique('point_token_UNIQUE');
			$table->dateTime('created_at')->nullable();
			$table->index(['user_id'], 'fk_point_transactions_users1_idx');
			$table->index(['point_plan_id'], 'fk_point_transactions_point_plans1_idx');
			$table->index(['receive_user_id'], 'fk_point_transactions_users2_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('point_transactions');
	}

}
