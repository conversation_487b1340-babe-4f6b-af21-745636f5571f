<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFeedbacksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feedbacks', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('enable_warning')->unsigned();
            $table->text('content');
            $table->integer('send_status');
            $table->dateTime('send_schedule_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->integer('feedback_div')->unsigned();
            $table->string('foreign_table');
            $table->integer('foreign_id')->unsigned()->nullable();
            $table->integer('target_user_id')->nullable();
            $table->integer('creator_id')->unsigned();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feedbacks');
    }
}
