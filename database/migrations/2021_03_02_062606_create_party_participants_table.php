<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePartyParticipantsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('party_participants', function(Blueprint $table)
		{
			$table->integer('user_id')->unsigned();
			$table->integer('party_id');
			$table->integer('party_group_id');
			$table->unsignedTinyInteger('party_participant_status');
			$table->string('agora_channel_uid', 20)->nullable();
			$table->dateTime('started_at')->nullable();
			$table->dateTime('ended_at')->nullable();
			$table->dateTime('expire_at')->nullable();
			$table->string('agora_token')->nullable();
			$table->unsignedTinyInteger('number_extended')->default(0);
			$table->time('party_time')->nullable();
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
			$table->primary(['user_id','party_id'], 'pk_party_participants_user_id_party_id');
			$table->index(['user_id'], 'fk_users_has_setting_parties_users1_idx');
			$table->index(['party_group_id'], 'fk_party_participants_party_groups1_idx');
			$table->index(['party_id'], 'fk_party_participants_parties1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('party_participants');
	}

}
