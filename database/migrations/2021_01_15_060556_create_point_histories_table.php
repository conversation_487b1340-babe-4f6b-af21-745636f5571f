<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePointHistoriesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('point_histories', function(Blueprint $table)
		{
			$table->increments('id');
			$table->integer('user_id')->unsigned();
			$table->integer('point_update_div')->unsigned();
			$table->integer('point_plan_id')->nullable();
			$table->integer('party_id')->nullable();
			$table->integer('point')->unsigned();
			$table->integer('money')->unsigned()->nullable();
			$table->integer('old_point')->unsigned();
			$table->integer('new_point')->unsigned();
			$table->dateTime('point_at');
			$table->softDeletes();
			$table->dateTime('created_at')->nullable();
			$table->dateTime('updated_at')->nullable();
			$table->index(['party_id'], 'fk_point_histories_parties1_idx');
			$table->index(['user_id'], 'user_id');
			$table->index(['point_plan_id'], 'fk_point_histories_point_plans1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('point_histories');
	}

}
