<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateNotificationsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('notifications', function(Blueprint $table)
		{
			$table->increments('id');
			$table->integer('notification_div')->unsigned();
			$table->string('foreign_table', 100);
			$table->integer('foreign_id')->unsigned()->nullable();
			$table->string('title');
			$table->text('body');
			$table->unsignedTinyInteger('send_status');
			$table->dateTime('send_schedule_at')->nullable();
			$table->dateTime('sent_at')->nullable();
			$table->integer('creator_id')->unsigned();
			$table->integer('target_user_id')->unsigned();
			$table->softDeletes();
			$table->dateTime('created_at')->nullable();
			$table->dateTime('updated_at')->nullable();
			$table->index(['creator_id'], 'fk_notifications_accounts1_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('notifications');
	}

}
