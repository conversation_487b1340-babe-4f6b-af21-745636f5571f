<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePartiesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('parties', function(Blueprint $table)
		{
			$table->integer('id', true);
			$table->string('channel_id', 100);
			$table->unsignedTinyInteger('invite_member_type');
			$table->unsignedTinyInteger('group_type');
			$table->unsignedTinyInteger('party_status')->default(1);
			$table->integer('creator_id')->unsigned();
			$table->dateTime('match_at');
			$table->dateTime('start_at')->nullable();
			$table->dateTime('expire_at')->nullable();
			$table->dateTime('ended_at')->nullable();
			$table->time('party_time')->nullable();
			$table->unsignedTinyInteger('is_matched_extend')->default(0);
			$table->softDeletes();
			$table->dateTime('created_at');
			$table->dateTime('updated_at');
			$table->index(['creator_id'], 'fk_setting_parties_users_idx');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('parties');
	}

}
