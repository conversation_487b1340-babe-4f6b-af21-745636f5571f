<?php

namespace Database\Seeders;

use App\Eloquent\Job;
use App\Eloquent\JobCategory;
use App\Eloquent\StorageFile;
use App\Eloquent\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Src\Enums\ApprovalStatus;

class DevelopSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        StorageFile::factory()->create();

        User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('12345678'),
            'email_verification_at' => now(),
            'user_status' => ApprovalStatus::APPROVED
        ]);

        JobCategory::factory()->count(3)->create();

        Job::factory()->count(3)->create();
    }
}
