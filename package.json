{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "lint": "eslint --ext .js,.vue resources/js/components resources/js/store"}, "dependencies": {"@core-player/playcore-hls": "^0.1.1", "@fortawesome/fontawesome-free": "^5.11.2", "admin-lte": "^3.0.5", "axios": "^0.19", "bootstrap": "^4.4.1", "bootstrap-vue": "^2.16.0", "cross-env": "^7.0", "hls.js": "^1.0.0", "image-exists": "^1.1.0", "jquery": "^3.4.1", "laravel-mix": "^5.0.1", "lodash": "^4.17.19", "lodash.defaultsdeep": "^4.6.0", "luxon": "^1.25.0", "moment": "^2.23.0", "node-sass": "^4.11.0", "popper.js": "^1.12", "resolve-url-loader": "^2.3.1", "sass": "^1.20.1", "sass-loader": "^8.0.0", "vee-validate": "^3.3.9", "vue": "^2.5.17", "vue-chat-scroll": "^1.4.0", "vue-datetime": "^1.0.0-beta.14", "vue-i18n": "^8.20.0", "vue-template-compiler": "^2.6.10", "vue-toasted": "^1.1.28", "vuejs-datetimepicker": "^1.1.13", "vuex": "^3.5.1", "weekstart": "^1.0.1"}, "devDependencies": {"babel-eslint": "^10.0.1", "babel-plugin-istanbul": "^5.1.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-3": "^6.24.1", "babel-preset-vue": "^2.0.2", "eslint": "^7.6.0", "eslint-config-standard": "^12.0.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.1.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^8.0.0", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^5.0.0-beta.5"}}