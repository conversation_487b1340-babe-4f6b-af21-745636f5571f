{"private": true, "type": "module", "scripts": {"build": "php artisan ziggy:generate \"resources/js/ssr-route/ziggy.js\" --types & vite build", "dev": "vite", "lint": "eslint resources/js --ext .ts,.js,.vue", "lint:fix": "eslint resources/js --ext .ts,.js,.vue --fix"}, "dependencies": {"@ckeditor/ckeditor5-build-decoupled-document": "^41.4.2", "@ckeditor/ckeditor5-vue": "^7.3.0", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue3": "^6.1.17", "@inertiajs/vue3": "^2.0.5", "@popperjs/core": "^2.11.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vee-validate/rules": "^4.15.0", "dotenv": "^16.5.0", "flatpickr": "^4.6.13", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "vee-validate": "^4.5.8", "vue": "^3.5.13", "vue-flatpickr-component": "^11.0.5", "vue-toastification": "^2.0.0-rc.5", "ziggy-js": "^2.5.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@heroicons/vue": "^2.2.0", "@tailwindcss/postcss": "^4.0.0", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.10.7", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/server-renderer": "^3.5.13", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "axios": "^1.7.4", "concurrently": "^9.0.1", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "globals": "^16.0.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^4.0.0", "typescript": "~5.7.3", "vite": "^6.0.11", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^2.2.0"}}