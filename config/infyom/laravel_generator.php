<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Paths
    |--------------------------------------------------------------------------
    |
    */

    'path' => [

        'migration'         => database_path('migrations/'),

        'model'             => app_path('Eloquent/'),

        'enum'        => base_path('src/Enum/'),

        'data_model'        => base_path('src/Domain/Admin/Models/'),

        'api_data_model'        => base_path('src/Domain/Api/Models/'),

        'datatables'        => app_path('DataTables/'),

        'repository'        => base_path('src/Domain/Admin/Services/'),

        'api_repository'        => base_path('src/Domain/Api/Services/'),

        'routes'            => base_path('routes/admin.php'),

        'api_routes'        => base_path('routes/api.php'),

        'request'           => base_path('src/Domain/Admin/Requests/'),

        'validator'         => base_path('src/Domain/Admin/Validators/'),

        'api_request'       => base_path('src/Domain/Api/Requests/'),

        'controller'        => base_path('src/Domain/Admin/Controllers/'),

        'api_controller'    => base_path('src/Domain/Api/Controllers/'),

        'repository_test'   => base_path('tests/Repositories/'),

        'api_test'          => base_path('tests/APIs/'),

        'tests'             => base_path('tests/'),

        'views'             => resource_path('views/'),

        'vue'               => resource_path('js/components/'),

        'schema_files'      => resource_path('model_schemas/'),

        'templates_dir'     => resource_path('infyom/infyom-generator-templates/'),

        'seeder'            => database_path('seeds/'),

        'database_seeder'   => database_path('seeds/DatabaseSeeder.php'),

        'modelJs'           => resource_path('assets/js/models/'),

        'factory'           => database_path('factories/'),

        'view_provider'     => app_path('Providers/ViewServiceProvider.php'),

        'base_domain'       => base_path('src/Domain/'),

    ],

    /*
    |--------------------------------------------------------------------------
    | Namespaces
    |--------------------------------------------------------------------------
    |
    */

    'namespace' => [

        'model'             => 'App\Eloquent',

        'enum'             => 'Src\Enum',

        'data_model'        => 'Src\Domain\Admin\Models',

        'api_data_model'    => 'Src\Domain\Api\Models',

        'datatables'        => 'App\DataTables',

        'repository'        => 'Src\Domain\Admin\Services',

        'api_repository'    => 'Src\Domain\Api\Services',

        'controller'        => 'Src\Domain\Admin\Controllers',

        'api_controller'    => 'Src\Domain\Api\Controllers',

        'request'           => 'Src\Domain\Admin\Requests',

        'validator'         => 'Src\Domain\Admin\Validators',

        'api_request'       => 'Src\Domain\Api\Requests',

        'repository_test'   => 'Tests\Repositories',

        'api_test'          => 'Tests\APIs',

        'tests'             => 'Tests',
    ],

    /*
    |--------------------------------------------------------------------------
    | Templates
    |--------------------------------------------------------------------------
    |
    */

    'templates'         => 'adminlte-templates',

    /*
    |--------------------------------------------------------------------------
    | Model extend class
    |--------------------------------------------------------------------------
    |
    */

    'model_extend_class' => 'Eloquent',

    /*
    |--------------------------------------------------------------------------
    | API routes prefix & version
    |--------------------------------------------------------------------------
    |
    */

    'api_prefix'  => 'api',

    'api_version' => '',

    /*
    |--------------------------------------------------------------------------
    | Options
    |--------------------------------------------------------------------------
    |
    */

    'options' => [

        'softDelete' => false,

        'save_schema_file' => false,

        'localized' => true,

        'tables_searchable_default' => false,

        'repository_pattern' => true,

        'excluded_fields' => ['id'], // Array of columns that doesn't required while creating module
    ],

    /*
    |--------------------------------------------------------------------------
    | Prefixes
    |--------------------------------------------------------------------------
    |
    */

    'prefixes' => [

        'route' => 'admin',  // using admin will create route('admin.?.index') type routes

        'path' => '',

        'view' => 'admin',  // using backend will create return view('backend.?.index') type the backend views directory

        'public' => '',

        'locale' => 'admin',
    ],

    /*
    |--------------------------------------------------------------------------
    | Add-Ons
    |--------------------------------------------------------------------------
    |
    */

    'add_on' => [

        'swagger'       => false,

        'tests'         => true,

        'datatables'    => false,

        'menu'          => [

            'enabled'       => true,

            'menu_file'     => 'admin/layouts/element/sidebar.blade.php',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Timestamp Fields
    |--------------------------------------------------------------------------
    |
    */

    'timestamps' => [

        'enabled'       => true,

        'created_at'    => 'created_at',

        'updated_at'    => 'updated_at',

        'deleted_at'    => 'deleted_at',
    ],

    /*
    |--------------------------------------------------------------------------
    | Save model files to `App/Models` when use `--prefix`. see #208
    |--------------------------------------------------------------------------
    |
    */
    'ignore_model_prefix' => false,

    /*
    |--------------------------------------------------------------------------
    | Specify custom doctrine mappings as per your need
    |--------------------------------------------------------------------------
    |
    */
    'from_table' => [

        'doctrine_mappings' => [],
    ],

];
