<?php

return [
    'base_url' =>  env('SOCKET_BASE_URL', 'http://127.0.0.1:8700'),
    'setting_party' => [
        'invite' => '/api/setting_party/invite',
        'creator_join' => '/api/setting_party/creator_join',
        'other_group_joined' => '/api/setting_party/other_group_joined',
        'joined' => '/api/setting_party/joined',
        'deleted' => '/api/setting_party/deleted'
    ],
    'message' => [
        'update_message' => '/api/message/update_message',
        'deleted_message' => '/api/message/deleted_message',
        'update_message_conversation' => '/api/message/update_message_conversation',
    ],
    'party' => [
        'matched' => '/api/party/matched',
        'matching_timeout' => '/api/party/matching_timeout',
        'started' => '/api/party/started',
        'joined' => '/api/party/joined',
        'notice_end' => '/api/party/notice_end',
        'stage_user_extended' => '/api/party/stage_user_extended',
        'stage_user_expired' => '/api/party/stage_user_expired',
        'stage_new' => '/api/party/stage_new',
        'end' => '/api/party/end',
        'ask_extend' => '/api/party/ask_extend',
        'accepted_extend' => '/api/party/accepted_extend',
        'extended' => '/api/party/extended',
        'user_extended' => '/api/party/user_extended',
    ],
    'point' => [
        'confirm_present' => '/api/point/confirm_present',
        'decline_receive' => '/api/point/decline_receive',
        'subtracted' => '/api/point/subtracted',
    ],
    'friend' => [
        'invite' => '/api/friend/invite',
        'add_friend_success' => '/api/friend/add_friend_success',
    ],
    'notification' => [
        'new' => '/api/notification/new',
    ]
];
