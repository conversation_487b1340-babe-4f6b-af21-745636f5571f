import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
export default defineConfigWithVueTs(
  {
    files: ['**/*.{js,vue,ts}'],
    ignores: ['socket-server/**'],
    rules: {
      'no-trailing-spaces': 'error',
      semi: ['error', 'always'],
      'vue/multi-word-component-names': 'off',
      'vue/no-mutating-props': [
        'error',
        {
          shallowOnly: true,
        },
      ],
      'comma-dangle': 'off',
      indent: ['error', 2],
      'object-curly-spacing': ['error', 'always'],
    },
  },
  pluginVue.configs['flat/essential'],
  vueTsConfigs.recommended,
);
