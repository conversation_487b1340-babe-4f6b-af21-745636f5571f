# -*- mode: ruby -*-
# vi: set ft=ruby :

Encoding.default_external = 'UTF-8'
box      = 'generic/centos7'
hostname = 'rimocha'
domain   = 'rimocha.test'
ip       = '*************'
ram      = '1028'
cpu      = '1'

VAGRANTFILE_API_VERSION = "2"

Vagrant.configure(VAGRANTFILE_API_VERSION) do |config|
  config.vm.box = box
  config.vm.hostname = domain
  config.vm.network 'private_network', ip: ip
  config.vm.network "forwarded_port", guest: 80, host: 8080
  config.vm.network "forwarded_port", guest: 3306, host: 3306, auto_correct: true
  config.vbguest.auto_update = true

  config.vm.provider 'virtualbox' do |vb|
    vb.customize [
        'modifyvm', :id,
        '--name', hostname,
        '--memory', ram,
        '--cpus', cpu
    ]
  end

  config.vm.synced_folder '.', '/opt/app/rimocha', id: "vagrant", :nfs => false, :mount_options => ["dmode=777","fmode=777"]
    config.vm.provision :shell, :path => "vagrant/install.sh"
    config.vm.provision :shell, :path => "vagrant/after.sh"
    config.vm.provision :shell, run: "always", :path => "vagrant/always.sh"

end
