{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2.5", "ext-curl": "*", "ext-json": "*", "bensampo/laravel-enum": "^1.38", "doctrine/dbal": "~2.3", "fideloper/proxy": "^4.2", "fruitcake/laravel-cors": "^2.0", "google/apiclient": "^2.0", "guzzlehttp/guzzle": "^6.3", "kreait/firebase-php": "^5.0", "kreait/laravel-firebase": "^3.0", "laracasts/flash": "^3.1", "laravel/framework": "^7.24", "laravel/passport": "^9.3", "laravel/socialite": "^5.1", "laravel/tinker": "^2.0", "league/flysystem-aws-s3-v3": "^1.0", "pbmedia/laravel-ffmpeg": "^7.5", "socialiteproviders/apple": "^4.1", "tightenco/ziggy": "^0.9.4", "twilio/sdk": "^6.17"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.8", "facade/ignition": "^2.0", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "phpunit/phpunit": "^8.5", "martinlindhe/laravel-vue-i18n-generator": "0.1.46"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Src\\": "src/"}, "classmap": ["database/seeds", "database/factories"], "files": ["src/Utils/helpers.php", "src/Utils/constant.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}