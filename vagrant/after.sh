#!/usr/bin/env bash

# Setting for MySQL
systemctl start mysqld.service
grep "A temporary password" /var/log/mysqld.log
mysql_secure_installation
systemctl restart mysqld.service

# AP directory setting
echo -e "\e[1;32;40m Setup application \e[m";
rm -Rf /var/www/html
test -e /opt/app/xxxxx && rmdir /opt/app/xxxxx
ln -s /opt/app/rimocha/public /var/www/html

# Apache setting
export PHP_IDE_CONFIG="serverName=rimocha"
cp /opt/app/rimocha/vagrant/conf/httpd.conf /etc/httpd/conf/httpd.conf
systemctl restart httpd
systemctl enable httpd

# other.
systemctl restart mysqld

# hosts setting.
echo -e "\e[1;32;40m # HOSTS ADD # \e[m";
cat << EOL >> /etc/hosts
127.0.0.1 rimocha.test
EOL

# Storage::cloud()
mkdir /opt/app/rimocha/storage/app/public/file
ln -s /opt/app/rimocha/storage/app/public/file /opt/app/rimocha/public/file

# Change permission.
chmod 777 /opt/app/rimocha/storage -Rf
chmod 777 /opt/app/rimocha/bootstrap/cache -Rf

# env copy
cp /opt/app/rimocha/.env.example  /opt/app/rimocha/.env

# Application setup.
/usr/local/bin/composer self-update
cd /opt/app/rimocha
sudo -uvagrant /usr/local/bin/composer install

# crontab setting.
echo "* * * * * php /opt/app/rimocha/artisan schedule:run >> /dev/null 2>&1" > /etc/cron.d/rimocha

# IP display.
echo -e "\e[1;32;40m # SHOW IP ADDRESS # \e[m";
ip addr show eth1

echo
echo -e "\e[1;32;40m ## Please access rimocha http://$(ip addr show eth1 | perl -ne 'print "$1" if /inet\ (\d+\.\d+\.\d+\.\d+)/')/mgr/auth/login ## \e[m";
echo -e "\e[1;32;40m ## FINISHED. ## \e[m";
echo

exit 0
